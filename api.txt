
// 简化的 Controller - 只需要一个 Service
@Controller
@RequestMapping({"/api/polygon/"})
public class PolygonController {
    @Autowired
    private PolygonService polygonService;
    // ======================== TICKERS ========================
    @RequestMapping({"getTickers.do"})
    @ResponseBody
    public ServerResponse getTickers(
            @RequestParam(value = "ticker", required = false) String ticker,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "market", defaultValue = "stocks") String market,
            @RequestParam(value = "exchange", required = false) String exchange,
            @RequestParam(value = "cusip", required = false) String cusip,
            @RequestParam(value = "cik", required = false) String cik,
            @RequestParam(value = "date", required = false) String date,
            @RequestParam(value = "search", required = false) String search,
            @RequestParam(value = "active", defaultValue = "true") Boolean active,
            @RequestParam(value = "sort", defaultValue = "ticker") String sort,
            @RequestParam(value = "order", defaultValue = "asc") String order,
            @RequestParam(value = "limit", defaultValue = "1000") Integer limit) {
        return polygonService.getTickers(ticker, type, market, exchange, cusip, cik,
                date, search, active, sort, order, limit);
    }

    @RequestMapping({"getTickerDetails.do"})
    @ResponseBody
    public ServerResponse getTickerDetails(
            @RequestParam("ticker") String ticker,
            @RequestParam(value = "date", required = false) String date) {
        return polygonService.getTickerDetails(ticker, date);
    }

    @RequestMapping({"getRelatedTickers.do"})
    @ResponseBody
    public ServerResponse getRelatedTickers(@RequestParam("ticker") String ticker) {
        return polygonService.getRelatedTickers(ticker);
    }

    @RequestMapping({"getTickerNews.do"})
    @ResponseBody
    public ServerResponse getTickerNews(
            @RequestParam("ticker") String ticker,
            @RequestParam(value = "published_utc", required = false) String publishedUtc,
            @RequestParam(value = "published_utc_gte", required = false) String publishedUtcGte,
            @RequestParam(value = "published_utc_lte", required = false) String publishedUtcLte,
            @RequestParam(value = "order", defaultValue = "desc") String order,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit,
            @RequestParam(value = "sort", defaultValue = "published_utc") String sort) {
        return polygonService.getTickerNews(ticker, publishedUtc, publishedUtcGte,
                publishedUtcLte, order, limit, sort);
    }

    @RequestMapping({"getTickerTypes.do"})
    @ResponseBody
    public ServerResponse getTickerTypes(
            @RequestParam(value = "asset_class", required = false) String assetClass,
            @RequestParam(value = "locale", required = false) String locale) {
        return polygonService.getTickerTypes(assetClass, locale);
    }

    // ======================== AGGREGATES (K线) ========================

Claude X, [2025/7/8 11:46]
@RequestMapping({"getAggregates.do"})
    @ResponseBody
    public ServerResponse getAggregates(
            @RequestParam("ticker") String ticker,
            @RequestParam("multiplier") Integer multiplier,
            @RequestParam("timespan") String timespan,
            @RequestParam("from") String from,
            @RequestParam("to") String to,
            @RequestParam(value = "adjusted", defaultValue = "true") Boolean adjusted,
            @RequestParam(value = "sort", defaultValue = "asc") String sort,
            @RequestParam(value = "limit", defaultValue = "5000") Integer limit) {
        return polygonService.getAggregates(ticker, multiplier, timespan, from, to,
                adjusted, sort, limit);
    }

    @RequestMapping({"getGroupedDaily.do"})
    @ResponseBody
    public ServerResponse getGroupedDaily(
            @RequestParam("date") String date,
            @RequestParam(value = "adjusted", defaultValue = "true") Boolean adjusted,
            @RequestParam(value = "include_otc", defaultValue = "false") Boolean includeOtc) {
        return polygonService.getGroupedDaily(date, adjusted, includeOtc);
    }

    @RequestMapping({"getDailyOpenClose.do"})
    @ResponseBody
    public ServerResponse getDailyOpenClose(
            @RequestParam("ticker") String ticker,
            @RequestParam("date") String date,
            @RequestParam(value = "adjusted", defaultValue = "true") Boolean adjusted) {
        return polygonService.getDailyOpenClose(ticker, date, adjusted);
    }

    @RequestMapping({"getPreviousClose.do"})
    @ResponseBody
    public ServerResponse getPreviousClose(
            @RequestParam("ticker") String ticker,
            @RequestParam(value = "adjusted", defaultValue = "true") Boolean adjusted) {
        return polygonService.getPreviousClose(ticker, adjusted);
    }

    // ======================== TRADES & QUOTES ========================

    @RequestMapping({"getTrades.do"})
    @ResponseBody
    public ServerResponse getTrades(
            @RequestParam("ticker") String ticker,
            @RequestParam(value = "timestamp", required = false) String timestamp,
            @RequestParam(value = "timestamp_gte", required = false) String timestampGte,
            @RequestParam(value = "timestamp_gt", required = false) String timestampGt,
            @RequestParam(value = "timestamp_lte", required = false) String timestampLte,
            @RequestParam(value = "timestamp_lt", required = false) String timestampLt,
            @RequestParam(value = "order", defaultValue = "asc") String order,
            @RequestParam(value = "limit", defaultValue = "5000") Integer limit,
            @RequestParam(value = "sort", defaultValue = "timestamp") String sort) {
        return polygonService.getTrades(ticker, timestamp, timestampGte, timestampGt,
                timestampLte, timestampLt, order, limit, sort);
    }

    @RequestMapping({"getQuotes.do"})
    @ResponseBody
    public ServerResponse getQuotes(
            @RequestParam("ticker") String ticker,
            @RequestParam(value = "timestamp", required = false) String timestamp,
            @RequestParam(value = "timestamp_gte", required = false) String timestampGte,
            @RequestParam(value = "timestamp_gt", required = false) String timestampGt,
            @RequestParam(value = "timestamp_lte", required = false) String timestampLte,
            @RequestParam(value = "timestamp_lt", required = false) String timestampLt,
            @RequestParam(value = "order", defaultValue = "asc") String order,
            @RequestParam(value = "limit", defaultValue = "5000") Integer limit,
            @RequestParam(value = "sort", defaultValue = "timestamp") String sort) {
        return polygonService.getQuotes(ticker, timestamp, timestampGte, timestampGt,
                timestampLte, timestampLt, order, limit, sort);
    }

    @RequestMapping({"getLastTrade.do"})
    @ResponseBody
    public ServerResponse getLastTrade(@RequestParam("ticker") String ticker) {
        return polygonService.getLastTrade(ticker);
    }

Claude X, [2025/7/8 11:46]
@RequestMapping({"getLastQuote.do"})
    @ResponseBody
    public ServerResponse getLastQuote(@RequestParam("ticker") String ticker) {
        return polygonService.getLastQuote(ticker);
    }

    // ======================== SNAPSHOTS ========================

    @RequestMapping({"getAllTickersSnapshot.do"})
    @ResponseBody
    public ServerResponse getAllTickersSnapshot(
            @RequestParam(value = "tickers", required = false) String tickers,
            @RequestParam(value = "include_otc", defaultValue = "false") Boolean includeOtc) {
        return polygonService.getAllTickersSnapshot(tickers, includeOtc);
    }

    @RequestMapping({"getTickerSnapshot.do"})
    @ResponseBody
    public ServerResponse getTickerSnapshot(@RequestParam("ticker") String ticker) {
        return polygonService.getTickerSnapshot(ticker);
    }

    @RequestMapping({"getGainersSnapshot.do"})
    @ResponseBody
    public ServerResponse getGainersSnapshot(
            @RequestParam(value = "include_otc", defaultValue = "false") Boolean includeOtc) {
        return polygonService.getGainersSnapshot(includeOtc);
    }

    @RequestMapping({"getLosersSnapshot.do"})
    @ResponseBody
    public ServerResponse getLosersSnapshot(
            @RequestParam(value = "include_otc", defaultValue = "false") Boolean includeOtc) {
        return polygonService.getLosersSnapshot(includeOtc);
    }

    // ======================== REFERENCE DATA ========================

    @RequestMapping({"getExchanges.do"})
    @ResponseBody
    public ServerResponse getExchanges(
            @RequestParam(value = "asset_class", required = false) String assetClass,
            @RequestParam(value = "locale", required = false) String locale) {
        return polygonService.getExchanges(assetClass, locale);
    }

    @RequestMapping({"getMarketStatus.do"})
    @ResponseBody
    public ServerResponse getMarketStatus() {
        return polygonService.getMarketStatus();
    }

    @RequestMapping({"getMarketHolidays.do"})
    @ResponseBody
    public ServerResponse getMarketHolidays() {
        return polygonService.getMarketHolidays();
    }

    @RequestMapping({"getStockSplits.do"})
    @ResponseBody
    public ServerResponse getStockSplits(
            @RequestParam(value = "ticker", required = false) String ticker,
            @RequestParam(value = "execution_date", required = false) String executionDate,
            @RequestParam(value = "execution_date_gte", required = false) String executionDateGte,
            @RequestParam(value = "execution_date_lte", required = false) String executionDateLte,
            @RequestParam(value = "order", defaultValue = "asc") String order,
            @RequestParam(value = "limit", defaultValue = "1000") Integer limit,
            @RequestParam(value = "sort", defaultValue = "execution_date") String sort) {
        return polygonService.getStockSplits(ticker, executionDate, executionDateGte,
                executionDateLte, order, limit, sort);
    }

Claude X, [2025/7/8 11:46]
@RequestMapping({"getDividends.do"})
    @ResponseBody
    public ServerResponse getDividends(
            @RequestParam(value = "ticker", required = false) String ticker,
            @RequestParam(value = "ex_dividend_date", required = false) String exDividendDate,
            @RequestParam(value = "ex_dividend_date_gte", required = false) String exDividendDateGte,
            @RequestParam(value = "ex_dividend_date_lte", required = false) String exDividendDateLte,
            @RequestParam(value = "record_date", required = false) String recordDate,
            @RequestParam(value = "declaration_date", required = false) String declarationDate,
            @RequestParam(value = "pay_date", required = false) String payDate,
            @RequestParam(value = "frequency", required = false) Integer frequency,
            @RequestParam(value = "cash_amount", required = false) Double cashAmount,
            @RequestParam(value = "dividend_type", required = false) String dividendType,
            @RequestParam(value = "order", defaultValue = "asc") String order,
            @RequestParam(value = "limit", defaultValue = "1000") Integer limit,
            @RequestParam(value = "sort", defaultValue = "ex_dividend_date") String sort) {
        return polygonService.getDividends(ticker, exDividendDate, exDividendDateGte,
                exDividendDateLte, recordDate, declarationDate,
                payDate, frequency, cashAmount, dividendType,
                order, limit, sort);
    }

    @RequestMapping({"getConditions.do"})
    @ResponseBody
    public ServerResponse getConditions(
            @RequestParam(value = "asset_class", required = false) String assetClass,
            @RequestParam(value = "data_type", required = false) String dataType,
            @RequestParam(value = "id", required = false) String conditionId,
            @RequestParam(value = "sip", required = false) String sip,
            @RequestParam(value = "order", defaultValue = "asc") String order,
            @RequestParam(value = "limit", defaultValue = "1000") Integer limit,
            @RequestParam(value = "sort", defaultValue = "name") String sort) {
        return polygonService.getConditions(assetClass, dataType, conditionId,
                sip, order, limit, sort);
    }

    // ======================== FUNDAMENTALS ========================

    @RequestMapping({"getStockFinancials.do"})
    @ResponseBody
    public ServerResponse getStockFinancials(
            @RequestParam("ticker") String ticker,
            @RequestParam(value = "period_of_report_date", required = false) String periodOfReportDate,
            @RequestParam(value = "period_of_report_date_gte", required = false) String periodOfReportDateGte,
            @RequestParam(value = "period_of_report_date_lte", required = false) String periodOfReportDateLte,
            @RequestParam(value = "filing_date", required = false) String filingDate,
            @RequestParam(value = "filing_date_gte", required = false) String filingDateGte,
            @RequestParam(value = "filing_date_lte", required = false) String filingDateLte,
            @RequestParam(value = "timeframe", required = false) String timeframe,
            @RequestParam(value = "include_sources", defaultValue = "false") Boolean includeSources,
            @RequestParam(value = "order", defaultValue = "desc") String order,
            @RequestParam(value = "limit", defaultValue = "100") Integer limit,
            @RequestParam(value = "sort", defaultValue = "period_of_report_date") String sort) {
        return polygonService.getStockFinancials(ticker, periodOfReportDate,
                periodOfReportDateGte, periodOfReportDateLte,
                filingDate, filingDateGte, filingDateLte,
                timeframe, includeSources, order, limit, sort);
    }

    // ======================== WEBSOCKET ========================

Claude X, [2025/7/8 11:46]
@RequestMapping({"subscribeRealTime.do"})
    @ResponseBody
    public ServerResponse subscribeRealTime(@RequestParam("symbols") String symbols) {
        return polygonService.subscribe(symbols);
    }

    @RequestMapping({"unsubscribeRealTime.do"})
    @ResponseBody
    public ServerResponse unsubscribeRealTime(@RequestParam("symbols") String symbols) {
        return polygonService.unsubscribe(symbols);
    }

    @RequestMapping({"getSubscriptionStatus.do"})
    @ResponseBody
    public ServerResponse getSubscriptionStatus() {
        return polygonService.getSubscriptionStatus();
    }

    @RequestMapping({"reconnectWebSocket.do"})
    @ResponseBody
    public ServerResponse reconnectWebSocket() {
        return polygonService.reconnect();
    }

    @RequestMapping({"disconnectWebSocket.do"})
    @ResponseBody
    public ServerResponse disconnectWebSocket() {
        return polygonService.disconnect();
    }
}

T ｜ WILL NEVER DM FIRST, [2025/7/8 13:43]
👌

Claude X, [2025/7/9 18:52]
/
     * 用户订阅股票实时数据
     */
    @RequestMapping({"subscribeRealTime.do"})
    @ResponseBody
    public ServerResponse subscribeRealTime(
            @RequestParam("symbols") String symbols,
            @RequestParam("userId") String userId) {
        return webSocketService.subscribe(symbols, userId);
    }

    /
     * 用户取消订阅股票实时数据
     */
    @RequestMapping({"unsubscribeRealTime.do"})
    @ResponseBody
    public ServerResponse unsubscribeRealTime(
            @RequestParam("symbols") String symbols,
            @RequestParam("userId") String userId) {
        return webSocketService.unsubscribe(symbols, userId);
    }

    /
     * 获取最新价格（从Redis缓存）
     */
    @RequestMapping({"getLatestPrice.do"})
    @ResponseBody
    public ServerResponse getLatestPrice(@RequestParam("symbol") String symbol) {
        return webSocketService.getLatestPrice(symbol);
    }

    /
     * 批量获取最新价格（从Redis缓存）
     */
    @RequestMapping({"getLatestPrices.do"})
    @ResponseBody
    public ServerResponse getLatestPrices(@RequestParam("symbols") String symbols) {
        return webSocketService.getLatestPrices(symbols);
    }

    // ======================== WEBSOCKET - 系统管理功能 ========================

    @RequestMapping({"getSubscriptionStatus.do"})
    @ResponseBody
    public ServerResponse getSubscriptionStatus() {
        return webSocketService.getSubscriptionStatus();
    }

    @RequestMapping({"reconnectWebSocket.do"})
    @ResponseBody
    public ServerResponse reconnectWebSocket() {
        return webSocketService.reconnect();
    }

    @RequestMapping({"disconnectWebSocket.do"})
    @ResponseBody
    public ServerResponse disconnectWebSocket() {
        return webSocketService.disconnect();
    }
