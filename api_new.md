# Polygon API 接口详细分析与应用场景

## 概述

基于后端提供的 Polygon API Controller，结合富途牛牛项目的页面结构，本文档详细分析每个接口的具体应用场景、入参要求、对应页面以及缺失的功能模块。

---

## 1. TICKERS 模块 - 股票基础信息

### 1.1 getTickers.do - 获取股票列表

**应用场景**: 股票搜索、市场浏览、股票筛选
**对应页面**:

- `FutuMarket.vue` - 市场页面的股票列表
- `FutuWatchlist.vue` - 自选股添加时的搜索功能
- **缺失页面**: 股票筛选器页面 `StockScreener.vue`

**入参分析**:

```javascript
{
  ticker: "AAPL",           // 可选，具体股票代码搜索
  type: "CS",               // 可选，股票类型 (CS=普通股, ETF=交易所交易基金)
  market: "stocks",         // 默认stocks，市场类型
  exchange: "NASDAQ",       // 可选，交易所筛选
  search: "Apple",          // 可选，公司名称模糊搜索
  active: true,             // 默认true，是否只显示活跃股票
  sort: "ticker",           // 默认ticker，排序字段
  order: "asc",             // 默认asc，排序方向
  limit: 1000               // 默认1000，返回数量限制
}
```

**返回数据结构预期**:

```javascript
{
  success: true,
  data: {
    results: [
      {
        ticker: "AAPL",
        name: "Apple Inc.",
        market: "stocks",
        locale: "us",
        primary_exchange: "NASDAQ",
        type: "CS",
        active: true,
        currency_name: "usd",
        cik: "0000320193",
        composite_figi: "BBG000B9XRY4",
        share_class_figi: "BBG001S5N8V8",
        last_updated_utc: "2023-01-01T00:00:00.000Z"
      }
    ],
    status: "OK",
    request_id: "xxx",
    count: 1,
    next_url: "xxx"
  }
}
```

### 1.2 getTickerDetails.do - 获取股票详细信息

**应用场景**: 股票详情页面的基础信息展示
**对应页面**: `FutuStockDetail.vue` - 股票详情页

**入参分析**:

```javascript
{
  ticker: "AAPL",           // 必需，股票代码
  date: "2023-01-01"        // 可选，指定日期的信息
}
```

**返回数据结构预期**:

```javascript
{
  success: true,
  data: {
    results: {
      ticker: "AAPL",
      name: "Apple Inc.",
      market: "stocks",
      locale: "us",
      primary_exchange: "NASDAQ",
      type: "CS",
      active: true,
      currency_name: "usd",
      description: "Apple Inc. designs, manufactures, and markets smartphones...",
      homepage_url: "https://www.apple.com",
      total_employees: 154000,
      list_date: "1980-12-12",
      branding: {
        logo_url: "https://api.polygon.io/v1/reference/company-branding/d3d3LmFwcGxlLmNvbQ/images/2022-01-10_logo.svg",
        icon_url: "https://api.polygon.io/v1/reference/company-branding/d3d3LmFwcGxlLmNvbQ/images/2022-01-10_icon.png"
      },
      share_class_shares_outstanding: 15728700000,
      weighted_shares_outstanding: 15728700000,
      market_cap: 2771126665000
    }
  }
}
```

### 1.3 getRelatedTickers.do - 获取相关股票

**应用场景**: 股票详情页的相关推荐
**对应页面**: `FutuStockDetail.vue` - 相关股票推荐区域
**缺失功能**: 需要在股票详情页添加相关股票推荐组件

### 1.4 getTickerNews.do - 获取股票新闻

**应用场景**: 股票详情页的新闻资讯、发现页面的新闻流
**对应页面**:

- `FutuStockDetail.vue` - 股票相关新闻
- `FutuDiscover.vue` - 发现页面的新闻流
- **缺失页面**: 新闻详情页 `NewsDetail.vue`

**入参分析**:

```javascript
{
  ticker: "AAPL",                    // 必需，股票代码
  published_utc_gte: "2023-01-01",   // 可选，新闻发布时间范围开始
  published_utc_lte: "2023-12-31",   // 可选，新闻发布时间范围结束
  order: "desc",                     // 默认desc，按时间排序
  limit: 10,                         // 默认10，返回数量
  sort: "published_utc"              // 默认published_utc，排序字段
}
```

### 1.5 getTickerTypes.do - 获取股票类型

**应用场景**: 股票筛选器的类型筛选选项
**缺失页面**: 股票筛选器页面 `StockScreener.vue`

---

## 2. AGGREGATES 模块 - K线数据

### 2.1 getAggregates.do - 获取K线数据

**应用场景**: K线图表数据源
**对应页面**:

- `FutuStockDetail.vue` - 股票详情页的K线图
- `/page/kline/index.vue` - 专门的K线页面

**入参分析**:

```javascript
{
  ticker: "AAPL",           // 必需，股票代码
  multiplier: 1,            // 必需，时间倍数 (1, 5, 15, 30, 60)
  timespan: "minute",       // 必需，时间单位 (minute, hour, day, week, month, quarter, year)
  from: "2023-01-01",       // 必需，开始日期
  to: "2023-12-31",         // 必需，结束日期
  adjusted: true,           // 默认true，是否调整股价
  sort: "asc",              // 默认asc，排序方向
  limit: 5000               // 默认5000，数据点数量
}
```

**返回数据结构预期**:

```javascript
{
  success: true,
  data: {
    results: [
      {
        v: 75834,              // 成交量
        vw: 150.0123,          // 成交量加权平均价
        o: 149.89,             // 开盘价
        c: 150.23,             // 收盘价
        h: 151.12,             // 最高价
        l: 149.34,             // 最低价
        t: 1609459200000,      // 时间戳
        n: 1                   // 交易次数
      }
    ],
    ticker: "AAPL",
    queryCount: 1,
    resultsCount: 1,
    adjusted: true,
    status: "OK",
    request_id: "xxx",
    count: 1
  }
}
```

### 2.2 getGroupedDaily.do - 获取市场日线数据

**应用场景**: 市场概览页面的整体市场数据
**对应页面**: `FutuMarket.vue` - 市场页面的整体行情

### 2.3 getDailyOpenClose.do - 获取日开收数据

**应用场景**: 股票详情页的当日开收盘信息
**对应页面**: `FutuStockDetail.vue` - 股票基本信息区域

### 2.4 getPreviousClose.do - 获取前收盘价

**应用场景**: 股票列表和详情页的涨跌幅计算基准
**对应页面**:

- `FutuMarket.vue` - 股票列表的涨跌幅显示
- `FutuWatchlist.vue` - 自选股的涨跌幅显示
- `FutuStockDetail.vue` - 股票详情的涨跌幅计算

---

## 3. TRADES & QUOTES 模块 - 交易与报价

### 3.1 getTrades.do - 获取交易记录

**应用场景**: 股票详情页的实时交易流水
**对应页面**: `FutuStockDetail.vue` - 交易明细区域
**缺失功能**: 需要添加实时交易流水组件

### 3.2 getQuotes.do - 获取报价数据

**应用场景**: 股票详情页的买卖盘口数据
**对应页面**: `FutuStockDetail.vue` - 五档买卖盘显示
**缺失功能**: 需要添加五档盘口组件

### 3.3 getLastTrade.do - 获取最新交易

**应用场景**: 股票列表的最新成交价显示
**对应页面**:

- `FutuMarket.vue` - 股票列表的实时价格
- `FutuWatchlist.vue` - 自选股的实时价格

### 3.4 getLastQuote.do - 获取最新报价

**应用场景**: 股票详情页的实时买卖价显示
**对应页面**: `FutuStockDetail.vue` - 实时买卖价区域

---

## 4. SNAPSHOTS 模块 - 快照数据

### 4.1 getAllTickersSnapshot.do - 获取所有股票快照

**应用场景**: 市场页面的股票列表数据源
**对应页面**: `FutuMarket.vue` - 市场页面主要数据源

### 4.2 getTickerSnapshot.do - 获取单个股票快照

**应用场景**: 股票详情页的实时数据刷新
**对应页面**: `FutuStockDetail.vue` - 实时数据更新

### 4.3 getGainersSnapshot.do - 获取涨幅榜

**应用场景**: 市场页面的涨幅排行榜
**对应页面**: `FutuMarket.vue` - 涨幅榜单
**缺失功能**: 需要添加排行榜切换组件

### 4.4 getLosersSnapshot.do - 获取跌幅榜

**应用场景**: 市场页面的跌幅排行榜
**对应页面**: `FutuMarket.vue` - 跌幅榜单

---

## 5. REFERENCE DATA 模块 - 参考数据

### 5.1 getExchanges.do - 获取交易所信息

**应用场景**: 股票筛选器的交易所筛选选项
**缺失页面**: 股票筛选器页面 `StockScreener.vue`

### 5.2 getMarketStatus.do - 获取市场状态

**应用场景**: 全局市场开闭市状态显示
**对应页面**: 所有页面的顶部状态栏
**缺失功能**: 需要添加全局市场状态组件

### 5.3 getMarketHolidays.do - 获取市场假期

**应用场景**: 交易日历功能
**缺失页面**: 交易日历页面 `TradingCalendar.vue`

### 5.4 getStockSplits.do - 获取股票分割信息

**应用场景**: 股票详情页的公司行动信息
**对应页面**: `FutuStockDetail.vue` - 公司行动区域
**缺失功能**: 需要添加公司行动组件

### 5.5 getDividends.do - 获取股息信息

**应用场景**: 股票详情页的股息历史
**对应页面**: `FutuStockDetail.vue` - 股息信息区域
**缺失功能**: 需要添加股息历史组件

### 5.6 getConditions.do - 获取交易条件

**应用场景**: 交易页面的订单条件设置
**对应页面**:

- `/page/trading/buy.vue` - 买入页面
- `/page/kline/buyStock.vue` - K线买入页面
- **缺失页面**: 卖出页面 `TradingSell.vue`

---

## 6. FUNDAMENTALS 模块 - 基本面数据

### 6.1 getStockFinancials.do - 获取财务数据

**应用场景**: 股票详情页的财务分析
**对应页面**: `FutuStockDetail.vue` - 财务数据区域
**缺失功能**: 需要添加财务数据展示组件
**缺失页面**: 财务分析页面 `FinancialAnalysis.vue`

---

## 7. WEBSOCKET 模块 - 实时数据

### 7.1 subscribeRealTime.do - 订阅实时数据

**应用场景**: 股票详情页和自选股的实时价格推送
**对应页面**:

- `FutuStockDetail.vue` - 实时价格更新
- `FutuWatchlist.vue` - 自选股实时更新
- `FutuMarket.vue` - 市场页面实时更新

**入参分析**:

```javascript
{
  symbols: "AAPL,MSFT,GOOGL",  // 必需，股票代码列表，逗号分隔
  userId: "user123"            // 必需，用户ID
}
```

### 7.2 unsubscribeRealTime.do - 取消订阅

**应用场景**: 页面离开时取消实时数据订阅
**对应页面**: 所有使用实时数据的页面的 beforeDestroy 钩子

### 7.3 getLatestPrice.do - 获取最新价格

**应用场景**: 单个股票的最新价格获取
**对应页面**: `FutuStockDetail.vue` - 价格刷新

### 7.4 getLatestPrices.do - 批量获取最新价格

**应用场景**: 股票列表的批量价格更新
**对应页面**:

- `FutuMarket.vue` - 市场列表价格更新
- `FutuWatchlist.vue` - 自选股价格更新

### 7.5 getSubscriptionStatus.do - 获取订阅状态

**应用场景**: 检查WebSocket连接状态
**对应页面**: 全局状态管理

### 7.6 reconnectWebSocket.do - 重连WebSocket

**应用场景**: 网络异常时的自动重连
**对应页面**: 全局错误处理

### 7.7 disconnectWebSocket.do - 断开WebSocket

**应用场景**: 应用退出时的资源清理
**对应页面**: 应用生命周期管理

---

## 缺失的页面和功能模块

### 急需开发的页面

1. **StockScreener.vue** - 股票筛选器页面
2. **NewsDetail.vue** - 新闻详情页面
3. **TradingCalendar.vue** - 交易日历页面
4. **FinancialAnalysis.vue** - 财务分析页面
5. **TradingSell.vue** - 卖出交易页面

### 需要增强的现有页面功能

1. **FutuStockDetail.vue** 需要添加:
   - 五档买卖盘口组件
   - 实时交易流水组件
   - 财务数据展示组件
   - 公司行动信息组件
   - 股息历史组件
   - 相关股票推荐组件

2. **FutuMarket.vue** 需要添加:
   - 排行榜切换组件 (涨幅榜/跌幅榜/成交量榜)
   - 市场状态指示器

3. **全局功能** 需要添加:
   - WebSocket 连接管理
   - 实时数据推送服务
   - 市场状态全局组件

### API 集成优先级建议

1. **高优先级**: getTickers, getTickerDetails, getAggregates, getAllTickersSnapshot
2. **中优先级**: getTickerNews, getGainersSnapshot, getLosersSnapshot, subscribeRealTime
3. **低优先级**: getStockFinancials, getMarketHolidays, getConditions

---

## 总结

当前API覆盖了股票交易应用的核心功能，但在财务分析、新闻资讯、实时数据推送等方面还需要完善页面和组件。建议按照优先级逐步集成API，同时开发缺失的页面和功能模块。
