/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    Elalert: typeof import('./src/components/elalert.vue')['default']
    Foot: typeof import('./src/components/foot.vue')['default']
    FutuTabbar: typeof import('./src/components/FutuTabbar.vue')['default']
    LoginDialog: typeof import('./src/components/loginDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
