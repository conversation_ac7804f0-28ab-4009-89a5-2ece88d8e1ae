'use strict'
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require('path')

module.exports = {
  dev: {

    // Paths
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
      // 代理到真实API服务器解决跨域问题
      '/api': {
        target: 'http://api.coin-tx.exchange',
        secure: false,
        changeOrigin: true,
        logLevel: 'debug',
        pathRewrite: {
          '^/api': '/api'
        },
        onProxyReq: function(proxyReq, req, res) {
          console.log('[PROXY] Request:', req.method, req.url, '-> http://api.coin-tx.exchange' + req.url);
          // 添加必要的请求头
          proxyReq.setHeader('Origin', 'http://api.coin-tx.exchange');
          proxyReq.setHeader('Referer', 'http://api.coin-tx.exchange');
        },
        onProxyRes: function(proxyRes, req, res) {
          console.log('[PROXY] Response:', proxyRes.statusCode, req.url);
        },
        onError: function(err, req, res) {
          console.log('[PROXY ERROR]:', err.message);
        }
      },
      // 代理用户相关接口
      '/user': {
        target: 'http://api.coin-tx.exchange',
        secure: false,
        changeOrigin: true,
        logLevel: 'debug',
        onProxyReq: function(proxyReq, req, res) {
          console.log('[USER PROXY] Request:', req.method, req.url, '-> http://api.coin-tx.exchange' + req.url);
          // 添加必要的请求头
          proxyReq.setHeader('Origin', 'http://api.coin-tx.exchange');
          proxyReq.setHeader('Referer', 'http://api.coin-tx.exchange');
        },
        onProxyRes: function(proxyRes, req, res) {
          console.log('[USER PROXY] Response:', proxyRes.statusCode, req.url);
        },
        onError: function(err, req, res) {
          console.log('[USER PROXY ERROR]:', err.message);
        }
      }
    },

    // Various Dev Server settings
    host: '127.0.0.1', // can be overwritten by process.env.HOST
    port: 8012, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: true,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    // Use Eslint Loader?
    // If true, your code will be linted during bundling and
    // linting errors and warnings will be shown in the console.
    useEslint: false,
    // If true, eslint errors and warnings will also be shown in the error overlay
    // in the browser.
    showEslintErrorsInOverlay: false,

    /**
     * Source Maps
     */

    // https://webpack.js.org/configuration/devtool/#development
    devtool: 'cheap-module-eval-source-map',

    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,

    cssSourceMap: true
  },

  build: {
    // Template for index.html
    index: path.resolve(__dirname, '../dist/index.html'),

    // Paths
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'static',
    assetsPublicPath: './',

    /**
     * Source Maps
     */

    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: '#source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ['js', 'css'],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report
  }
}
