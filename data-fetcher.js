const fs = require('fs');
const https = require('https');

// 随机emoji数组
const emojis = [
    '\u{1F911}', // 🤑
    '\u{1F47B}', // 👻
    '\u{1F680}', // 🚀
    '\u{1F48E}', // 💎
    '\u{1F525}', // 🔥
    '\u{26A1}',  // ⚡
    '\u{1F31F}', // 🌟
    '\u{1F4B0}', // 💰
    '\u{1F3AF}', // 🎯
    '\u{1F984}', // 🦄
    '\u{1F3AA}', // 🎪
    '\u{1F3A8}', // 🎨
    '\u{1F3AD}', // 🎭
    '\u{1F308}', // 🌈
    '\u{2B50}',  // ⭐
    '\u{1F4AB}', // 💫
    '\u{1F38A}', // 🎊
    '\u{1F389}', // 🎉
    '\u{1F52E}', // 🔮
    '\u{1F49D}', // 💝
    '\u{1F381}', // 🎁
    '\u{1F3C6}', // 🏆
    '\u{1F451}', // 👑
    '\u{1F4AF}'  // 💯
];

// 获取随机emoji
function getRandomEmoji() {
    return emojis[Math.floor(Math.random() * emojis.length)];
}

// 发送POST请求的函数
function makeRequest(pageNum) {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            pageNum: pageNum,
            pageSize: 25,
            chainId: 102,
            params: {
                sortType: "desc",
                sortsortField: "block_user_num"
            }
        });

        const options = {
            hostname: 'hidex.ai',
            port: 443,
            path: '/api/frontend/app/pubSmart/list',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData),
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        };

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve(jsonData);
                } catch (error) {
                    reject(new Error(`解析JSON失败: ${error.message}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.write(postData);
        req.end();
    });
}

// 主函数
async function fetchAllData() {
    console.log('开始获取数据...');

    const allWallets = [];
    let currentPage = 1;
    let totalPages = 1;

    try {
        // 首先获取第一页数据来确定总页数
        console.log(`正在获取第 ${currentPage} 页数据...`);
        const firstResponse = await makeRequest(currentPage);

        if (!firstResponse.success || firstResponse.code !== 200) {
            throw new Error(`API请求失败: ${firstResponse.message || '未知错误'}`);
        }

        const total = firstResponse.data.total;
        const pageSize = 25;
        totalPages = Math.ceil(total / pageSize);

        console.log(`总共 ${total} 条数据，需要获取 ${totalPages} 页`);

        // 处理第一页数据
        if (firstResponse.data.rows && firstResponse.data.rows.length > 0) {
            firstResponse.data.rows.forEach(item => {
                if (item.name && item.walletAddress) {
                    allWallets.push({
                        trackedWalletAddress: item.walletAddress,
                        name: item.name,
                        emoji: getRandomEmoji(),
                        alertsOn: true
                    });
                }
            });
        }

        // 获取剩余页面数据
        for (currentPage = 2; currentPage <= totalPages; currentPage++) {
            console.log(`正在获取第 ${currentPage} 页数据... (${currentPage}/${totalPages})`);

            try {
                const response = await makeRequest(currentPage);

                if (response.success && response.code === 200 && response.data.rows) {
                    response.data.rows.forEach(item => {
                        if (item.name && item.walletAddress) {
                            allWallets.push({
                                trackedWalletAddress: item.walletAddress,
                                name: item.name,
                                emoji: getRandomEmoji(),
                                alertsOn: true
                            });
                        }
                    });
                } else {
                    console.warn(`第 ${currentPage} 页数据获取失败:`, response.message);
                }

                // 添加延迟避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 500));

            } catch (error) {
                console.error(`获取第 ${currentPage} 页数据时出错:`, error.message);
                // 继续处理下一页，不中断整个流程
            }
        }

        console.log(`数据获取完成！总共获取到 ${allWallets.length} 条有效数据`);

        // 保存数据到文件
        const outputFile = 'wallet-data.json';
        fs.writeFileSync(outputFile, JSON.stringify(allWallets, null, 2), 'utf8');
        console.log(`数据已保存到 ${outputFile}`);

        // 显示前几条数据作为示例
        console.log('\n前5条数据示例:');
        console.log(JSON.stringify(allWallets.slice(0, 5), null, 2));

        return allWallets;

    } catch (error) {
        console.error('获取数据时发生错误:', error.message);
        throw error;
    }
}

// 运行脚本
if (require.main === module) {
    fetchAllData()
        .then((data) => {
            console.log(`\n✅ 脚本执行完成！共处理 ${data.length} 条数据`);
        })
        .catch((error) => {
            console.error('❌ 脚本执行失败:', error.message);
            process.exit(1);
        });
}

module.exports = { fetchAllData };
