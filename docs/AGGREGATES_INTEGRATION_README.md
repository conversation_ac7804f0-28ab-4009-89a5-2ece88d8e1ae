# AGGREGATES Module K-line Data Integration

## 概述

本文档描述了AGGREGATES模块K线数据功能的集成实现，包括API调用、WebSocket实时数据、HQChart适配器更新以及错误处理机制。

## 架构设计

### 数据流向

```
API Backend → AggregatesService → HQChartAdapter → HQChart
                     ↓
              RealTimeWebSocket → 实时数据更新
```

### 核心组件

1. **AggregatesService** (`src/services/aggregatesService.js`)
   - 处理AGGREGATES API调用
   - 数据格式化和验证
   - 错误处理和降级机制

2. **RealTimeWebSocket** (`src/services/realTimeWebSocket.js`)
   - WebSocket连接管理
   - 实时数据订阅
   - 自动重连机制

3. **HQChartAdapter** (`src/utils/hqchartAdapter.js`)
   - HQChart数据适配
   - 多数据源整合
   - 格式转换

4. **KLineService** (`src/services/klineService.js`)
   - 增强的K线服务
   - 向后兼容性
   - 智能数据源选择

## API接口

### AGGREGATES模块

#### getAggregates.do - 获取K线数据

```javascript
// 请求参数
{
  ticker: "AAPL",           // 必需，股票代码
  multiplier: 1,            // 必需，时间倍数
  timespan: "day",          // 必需，时间单位
  from: "2024-01-01",       // 必需，开始日期
  to: "2024-12-31",         // 必需，结束日期
  adjusted: true,           // 可选，是否调整股价
  sort: "asc",              // 可选，排序方向
  limit: 5000               // 可选，数据点数量
}

// 返回格式
{
  success: true,
  data: {
    results: [
      {
        v: 75834,              // 成交量
        vw: 150.0123,          // 成交量加权平均价
        o: 149.89,             // 开盘价
        c: 150.23,             // 收盘价
        h: 151.12,             // 最高价
        l: 149.34,             // 最低价
        t: 1609459200000,      // 时间戳
        n: 1                   // 交易次数
      }
    ]
  }
}
```

#### getPreviousClose.do - 获取前收盘价

```javascript
// 请求参数
{
  ticker: "AAPL",           // 必需，股票代码
  adjusted: true            // 可选，是否调整股价
}
```

#### getDailyOpenClose.do - 获取日开收数据

```javascript
// 请求参数
{
  ticker: "AAPL",           // 必需，股票代码
  date: "2024-07-11",       // 必需，日期
  adjusted: true            // 可选，是否调整股价
}
```

### WebSocket模块

#### subscribeRealTime.do - 订阅实时数据

```javascript
// 请求参数
{
  symbols: "AAPL,MSFT",     // 必需，股票代码列表
  userId: "user123"         // 必需，用户ID
}
```

#### getLatestPrice.do - 获取最新价格

```javascript
// 请求参数
{
  symbol: "AAPL"            // 必需，股票代码
}
```

## 使用方法

### 1. 基本K线数据获取

```javascript
import AggregatesService from '@/services/aggregatesService'

// 获取日线数据
const klineData = await AggregatesService.getAggregates({
  ticker: 'AAPL',
  multiplier: 1,
  timespan: 'day',
  from: '2024-01-01',
  to: '2024-12-31'
})

console.log('K线数据:', klineData)
```

### 2. 实时数据订阅

```javascript
import { connectRealTimeWS, subscribeRealTime } from '@/services/realTimeWebSocket'

// 连接WebSocket
await connectRealTimeWS('user123')

// 订阅实时数据
subscribeRealTime('AAPL', (data) => {
  console.log('实时价格更新:', data.data.price)
})
```

### 3. HQChart集成

```javascript
import { HQChartAdapter } from '@/utils/hqchartAdapter'

// HQChart会自动调用适配器
const option = {
  Symbol: 'AAPL',
  NetworkFilter: (data, callback) => {
    HQChartAdapter.NetworkFilter(data, callback)
  }
}
```

## 错误处理

### 降级机制

1. **API失败降级**
   - AGGREGATES API失败 → 原有API
   - 原有API失败 → 模拟数据

2. **WebSocket失败降级**
   - WebSocket连接失败 → 模拟实时数据
   - 自动重连机制（最多5次）

3. **数据验证**
   - 参数验证和默认值设置
   - 数据格式检查和转换
   - 异常数据过滤

### 错误日志

所有服务都包含详细的错误日志：

```javascript
console.error('❌ [ServiceName] 操作失败:', error)
console.warn('⚠️ [ServiceName] 警告信息:', warning)
console.log('✅ [ServiceName] 操作成功:', result)
```

## 测试

### 运行测试

1. **浏览器控制台测试**

```javascript
// 在浏览器控制台中运行
runAggregatesTest()
```

2. **单独测试各模块**

```javascript
// 测试AGGREGATES服务
AggregatesTest.testAggregatesService()

// 测试WebSocket服务
AggregatesTest.testWebSocketService()

// 测试HQChart适配器
AggregatesTest.testHQChartAdapter()
```

### 测试覆盖

- ✅ AGGREGATES API调用
- ✅ WebSocket连接和订阅
- ✅ HQChart数据适配
- ✅ 错误处理和降级
- ✅ 数据格式转换
- ✅ 实时数据推送

## 性能优化

### 数据缓存

- 前收盘价缓存（避免重复请求）
- 实时价格缓存（减少API调用）

### 连接管理

- WebSocket连接复用
- 自动断开清理
- 心跳保活机制

### 内存管理

- 定时清理过期数据
- 限制历史数据数量
- 及时释放回调函数

## 配置说明

### 时间周期映射

```javascript
0: 日线 (day, 1)
1: 1分钟 (minute, 1)
2: 5分钟 (minute, 5)
3: 15分钟 (minute, 15)
4: 30分钟 (minute, 30)
5: 60分钟 (hour, 1)
6: 周线 (week, 1)
7: 月线 (month, 1)
```

### WebSocket配置

```javascript
maxReconnectAttempts: 5      // 最大重连次数
reconnectDelay: 1000         // 重连延迟(ms)
heartbeatTimer: 30000        // 心跳间隔(ms)
mockDataInterval: 5000       // 模拟数据间隔(ms)
```

## 故障排除

### 常见问题

1. **K线数据为空**
   - 检查股票代码是否正确
   - 确认日期范围是否有效
   - 查看控制台错误日志

2. **实时数据不更新**
   - 检查WebSocket连接状态
   - 确认订阅是否成功
   - 验证用户ID是否有效

3. **HQChart显示异常**
   - 检查数据格式是否正确
   - 确认适配器是否正常工作
   - 查看HQChart错误信息

### 调试工具

```javascript
// 检查WebSocket状态
import { getRealTimeWSStatus } from '@/services/realTimeWebSocket'
console.log('WebSocket状态:', getRealTimeWSStatus())

// 检查AGGREGATES服务
import AggregatesService from '@/services/aggregatesService'
// 查看控制台日志中的详细信息
```

## 更新日志

### v1.0.0 (2024-07-12)

- ✅ 完成AGGREGATES API集成
- ✅ 实现WebSocket实时数据服务
- ✅ 更新HQChart适配器
- ✅ 增强K线服务
- ✅ 添加完整的错误处理
- ✅ 创建测试套件
- ✅ 编写集成文档

## 联系信息

如遇到问题或需要支持，请查看：

1. 控制台错误日志
2. 网络请求状态
3. API响应数据格式
4. WebSocket连接状态
