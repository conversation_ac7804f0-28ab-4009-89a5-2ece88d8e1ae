# Polygon API 数据映射优化说明

## 概述

本次优化对 `FutuStockDetail.vue` 组件中的 `loadPolygonStockData` 方法进行了重大改进，将 Polygon API 返回的数据正确映射到富途牛牛风格的股票详情页面中。

## 主要改进

### 1. 扩展了 stockData 数据结构

添加了以下新字段来存储 Polygon API 数据：

```javascript
stockData: {
  // 原有字段...

  // 新增的 Polygon API 数据字段
  marketCapValue: null,        // 市值数值（用于计算）
  sharesOutstanding: null,     // 总股本
  shareFloat: null,           // 流通股
  weekHigh52: null,           // 52周最高
  weekLow52: null,            // 52周最低
  allTimeHigh: null,          // 历史最高
  allTimeLow: null,           // 历史最低
  dividend: null,             // 股息
  dividendYield: null,        // 股息率
  peRatio: null,              // 市盈率
  pbRatio: null,              // 市净率
  employees: null,            // 员工人数
  listDate: null,             // 上市日期
  description: null,          // 公司描述
  homepage: null,             // 官网
  turnoverRate: '0.69%',      // 换手率
  askBidRatio: '0.00%',       // 委比
  volumeRatio: '0.90',        // 量比
  amplitude: '2.20%',         // 振幅
  avgPrice: '144.246'         // 平均价
}
```

### 2. 改进了数据映射逻辑

#### Ticker Details (股票详细信息) 映射：
```javascript
// 从 getTickerDetails API 获取：
- 公司名称 → stockData.name
- 股票代码 → stockData.symbol
- 主要交易所 → stockData.market
- 市值 → stockData.marketCap (格式化)
- 总股本 → stockData.sharesOutstanding (格式化)
- 流通股 → stockData.shareFloat (格式化)
- 52周高低点 → stockData.weekHigh52/weekLow52
- 公司信息 → 公司信息页面
```

#### Ticker Snapshot (股票快照) 映射：
```javascript
// 从 getTickerSnapshot API 获取：
- 最新价格 → stockData.price
- 开盘价/最高价/最低价/昨收 → stockData.open/high/low/prevClose
- 涨跌幅 → stockData.change/changePercent
- 成交量 → stockData.volume (格式化)
- 振幅计算 → stockData.amplitude
- 平均价计算 → stockData.avgPrice
- 交易时间 → stockData.closeTime
```

#### Daily Open Close (日开收数据) 映射：
```javascript
// 从 getDailyOpenClose API 获取：
- 开盘价 → stockData.open (dailyData.open)
- 收盘价 → stockData.price (dailyData.close)
- 最高价 → stockData.high (dailyData.high)
- 最低价 → stockData.low (dailyData.low)
- 成交量 → stockData.volume (dailyData.volume)
```

#### Previous Close (前收盘价) 映射：
```javascript
// 从 getPreviousClose API 获取：
- 前收盘价 → stockData.prevClose (prevData.c)
- 重新计算涨跌幅 → stockData.change/changePercent
```

#### Aggregates (K线数据) 映射：
```javascript
// 从 getAggregates API 获取：
- 成交额计算 → stockData.tradeAmount
- 30天最高价 → stockData.weekHigh52 (临时)
- 30天最低价 → stockData.weekLow52 (临时)
```

#### Ticker News (股票新闻) 映射：
```javascript
// 显示在"资讯"标签页：
- 新闻标题、描述、作者、时间
- 新闻图片和链接
```

### 3. 新增了数据格式化工具方法

```javascript
formatMarketCap(marketCap)    // 格式化市值 (万亿/亿/万)
formatShares(shares)          // 格式化股份数 (亿股/万股)
formatNumber(number)          // 格式化大数字 (千分符)
formatMarketTime(timestamp)   // 格式化市场时间
formatNewsTime(timestamp)     // 格式化新闻时间 (相对时间)
```

### 4. 新增了新闻和公司信息展示

#### 新闻页面 (`activeTab === 'news'`)：
- 显示相关股票新闻列表
- 包含新闻标题、描述、作者、时间
- 支持点击打开原文链接
- 显示新闻缩略图

#### 公司信息页面 (`activeTab === 'company'`)：
- 显示公司简介
- 官方网站链接
- 员工人数
- 上市日期
- 公司地址

### 5. 模板动态化

将原本硬编码的数据替换为动态绑定：

```vue
<!-- 原来： -->
<span class="value">145.000</span>

<!-- 现在： -->
<span class="value">{{ stockData.high }}</span>
```

## 数据映射表

| 页面显示 | Polygon API 字段 | stockData 字段 | 格式化方法 |
|---------|-----------------|---------------|-----------|
| 最高 | dailyOpenClose.high | stockData.high | toFixed(3) |
| 最低 | dailyOpenClose.low | stockData.low | toFixed(3) |
| 今开 | dailyOpenClose.open | stockData.open | toFixed(3) |
| 昨收 | previousClose.results[0].c | stockData.prevClose | toFixed(3) |
| 成交量 | dailyOpenClose.volume | stockData.volume | formatVolume |
| 成交额 | aggregates 计算 | stockData.tradeAmount | formatMarketCap |
| 总市值 | details.market_cap | stockData.marketCap | formatMarketCap |
| 总股本 | details.share_class_shares_outstanding | stockData.sharesOutstanding | formatShares |
| 流通股 | details.weighted_shares_outstanding | stockData.shareFloat | formatShares |
| 52周最高 | details.week_52_high | stockData.weekHigh52 | toFixed(3) |
| 52周最低 | details.week_52_low | stockData.weekLow52 | toFixed(3) |
| 振幅 | 计算值 | stockData.amplitude | 百分比 |
| 平均价 | 计算值 | stockData.avgPrice | toFixed(3) |

## 使用说明

1. **数据加载**：
   ```javascript
   // 在 created() 钩子中调用
   if (query.symbol) {
     this.loadPolygonStockData(query.symbol)
   }
   ```

2. **错误处理**：
   - 所有 API 调用都包含错误处理
   - 失败时不会影响页面正常显示
   - 未获取到的数据显示为 `--`

3. **性能优化**：
   - 使用 `Promise.all` 并发请求多个 API
   - 避免阻塞页面渲染

4. **数据一致性**：
   - 保持与原有数据结构兼容
   - 优先显示 Polygon API 数据，回退到默认值

## 注意事项

1. **API 限制**：
   - 注意 Polygon API 的调用频率限制
   - 考虑添加缓存机制减少重复请求

2. **数据精度**：
   - 价格数据保留3位小数
   - 百分比数据保留2位小数
   - 市值/股本数据格式化为中文习惯

3. **错误恢复**：
   - API 失败时页面仍能正常显示
   - 使用默认值或显示 `--`

4. **用户体验**：
   - 添加加载状态指示
   - 新闻和公司信息按需加载
   - 支持点击新闻打开原文

## 测试建议

1. 测试不同股票代码的数据展示
2. 测试网络异常情况下的错误处理
3. 验证数据格式化的正确性
4. 检查新闻和公司信息的交互功能

## 后续优化方向

1. 添加数据缓存机制
2. 实现实时数据更新
3. 优化大数据量的渲染性能
4. 添加更多技术指标的计算和显示
