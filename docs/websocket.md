# Real-time Stock Market WebSocket API Documentation

## Overview

This document describes how to use the WebSocket interface to get real-time stock price data. Through WebSocket connection, you can subscribe to stocks and receive real-time price updates without polling.

## API Endpoint

wss://api.coin-tx.exchange/ws/polygon?userId={Your User ID}

Parameters:
- `userId`: Required, your unique user identifier

Example:
wss://api.coin-tx.exchange/ws/polygon?userId=user123

## Quick Start

### Step 1: Establish Connection

const ws = new WebSocket('wss://api.coin-tx.exchange/ws/polygon?userId=user123');

### Step 2: Listen to Connection Status

// Connection successful
ws.onopen = function() {
    console.log('Connection successful');
};

// Connection closed
ws.onclose = function() {
    console.log('Connection closed');
};

// Connection error
ws.onerror = function(error) {
    console.error('Connection error:', error);
};

### Step 3: Send and Receive Messages

// Receive messages
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received message:', data);
};

// Send message (needs to be after connection is successful)
function sendMessage(message) {
    if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
    }
}

## Features

### 1. Subscribe to Stocks

Subscribe to one or more stocks to receive real-time price updates.

Send message:
{
    "action": "subscribe",
    "symbols": "AAPL,GOOGL,MSFT"
}

Parameters:
- `action`: Fixed value "subscribe"
- symbols: Stock symbols, multiple separated by commas

Response example:
{
    "type": "subscribe_response",
    "status": "success",
    "symbols": "AAPL,GOOGL,MSFT",
    "message": "Subscription successful"
}

### 2. Unsubscribe

Stop receiving price updates for specified stocks.

Send message:
{
    "action": "unsubscribe",
    "symbols": "AAPL"
}

Response example:
{
    "type": "unsubscribe_response",
    "status": "success",
    "symbols": "AAPL",
    "message": "Unsubscription successful"
}

### 3. Query Subscribed Stocks

Get the list of all currently subscribed stocks.

Send message:
{
    "action": "getSubscriptions"
}

Response example:
{
    "type": "subscriptions",
    "status": "success",
    "data": {
        "subscriptions": ["AAPL", "GOOGL", "MSFT"],
        "count": 3
    }
}

### 4. Heartbeat

Keep connection alive, recommended to send every 30 seconds.

Send message:
{
    "action": "ping"
}

Response example:
{
    "type": "pong",
    "serverTime": 1234567890000
}

## Received Message Types

### 1. Welcome Message

Automatically received when connection is successful.

{
    "type": "welcome",
    "userId": "user123",
    "message": "WebSocket connection successful",
    "timestamp": 1234567890000
}

### 2. Real-time Price Updates

Automatically pushed when price changes after subscribing to stocks.

{
    "type": "price_update",
    "symbol": "AAPL",
    "data": {
        "price": 150.25,
        "size": 100,
        "timestamp": 1234567890000
    }
}

Field descriptions:
- symbol: Stock symbol
- price: Latest trade price
- size: Trade volume
- timestamp: Timestamp (milliseconds)

### 3. Error Messages

Returned when operation fails.

{
    "type": "error",
    "message": "Error description",
    "status": "error"
}

## Complete Usage Examples

### Basic Example

// Create WebSocket connection
const ws = new WebSocket('wss://api.coin-tx.exchange/ws/polygon?userId=user123');

// Subscribe to stocks after connection success
ws.onopen = function() {
    console.log('Connection successful');

    // Subscribe to stocks
    ws.send(JSON.stringify({
        action: 'subscribe',
        symbols: 'AAPL,GOOGL'
    }));

    // Start heartbeat
    setInterval(() => {
        ws.send(JSON.stringify({ action: 'ping' }));
    }, 30000);
};

// Handle received messages
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);

    switch(data.type) {
        case 'welcome':
            console.log('Welcome message:', data.message);
            break;

        case 'price_update':
            console.log(`${data.symbol} latest price: $${data.data.price}`);
            // Update page display
            updatePriceDisplay(data.symbol, data.data.price);
            break;

        case 'subscribe_response':
            console.log('Subscription successful:', data.symbols);
            break;

        case 'error':
            console.error('Error:', data.message);
            break;
    }
};

// Update page price display
function updatePriceDisplay(symbol, price) {
    const element = document.getElementById(`price-${symbol}`);
    if (element) {
        element.textContent = `$${price.toFixed(2)}`;
    }
}

### Vue.js 示例
