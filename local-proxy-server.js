const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();
const PORT = 8013;

// 启用 CORS
app.use(cors());

// 代理所有 /api 请求到真实的API服务器
app.use('/api', createProxyMiddleware({
  target: 'http://api.coin-tx.exchange',
  changeOrigin: true,
  pathRewrite: {
    '^/api': '/api', // 保持路径不变
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`[PROXY] ${req.method} ${req.url} -> http://api.coin-tx.exchange${req.url}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`[PROXY] ${proxyRes.statusCode} ${req.url}`);
  },
  onError: (err, req, res) => {
    console.error(`[PROXY ERROR] ${req.url}:`, err.message);
    res.status(500).json({
      success: false,
      message: 'Proxy server error',
      error: err.message
    });
  }
}));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'Local proxy server is running',
    target: 'http://api.coin-tx.exchange',
    port: PORT
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 Local proxy server is running on http://localhost:${PORT}`);
  console.log(`📡 Proxying /api requests to: http://api.coin-tx.exchange`);
  console.log(`🔍 Health check: http://localhost:${PORT}/health`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down proxy server...');
  process.exit(0);
});
