{"name": "vue-liang-rong-wap", "version": "1.0.0", "description": "A Vue.js project", "author": "", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "lint": "eslint --ext .js,.vue src", "build": "node build/build.js"}, "dependencies": {"animate.css": "^3.7.2", "axios": "^0.19.0", "bootstrap": "^4.4.1", "chart.js": "^2.9.4", "chokidar": "^3.3.0", "clipboard": "^2.0.11", "dayjs": "^1.11.6", "echarts": "^4.5.0", "element-resize-detector": "^1.2.4", "element-ui": "^2.13.0", "express": "^4.18.2", "hqchart": "^1.1.8763", "jquery": "^3.4.1", "js-md5": "^0.7.3", "klinecharts": "^8.6.3", "lib-flexible": "^0.3.2", "mint-ui": "^2.2.13", "pdfjs-dist": "2.2.228", "qrcode": "^1.4.4", "qrcodejs2": "^0.0.2", "qs": "^6.9.1", "vant": "^2.12.51", "vue": "^2.5.2", "vue-clipboard2": "^0.3.1", "vue-i18n": "^8.14.1", "vue-photo-preview": "^1.1.3", "vue-router": "^3.0.7", "vue-svg-icon": "^1.2.9", "vue-touch": "^1.1.0", "vuex": "^3.1.2", "watchpack": "^1.6.0"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-eslint": "^8.2.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.5", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.0.1", "cors": "^2.8.5", "css-loader": "^0.28.11", "eslint": "^4.15.0", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "eslint-plugin-vue": "^4.0.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "http-proxy-middleware": "^3.0.5", "less": "^3.10.3", "less-loader": "^5.0.0", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "px2rem-loader": "^0.1.9", "rimraf": "^2.6.0", "semver": "^5.3.0", "shelljs": "^0.8.5", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.1.2", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}