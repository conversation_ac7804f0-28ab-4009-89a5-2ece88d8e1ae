window._iconfont_svg_string_ = '<svg><symbol id="icon-icon_A" viewBox="0 0 1024 1024"><path d="M799.5904 940.8H230.816A141.3568 141.3568 0 0 1 89.6 799.5904v-568.768A141.3568 141.3568 0 0 1 230.816 89.6h568.7744A141.3568 141.3568 0 0 1 940.8 230.8224v353.344a32.0768 32.0768 0 1 1-64.1536 0v-353.344a77.1264 77.1264 0 0 0-77.0432-77.0432H230.816a77.1264 77.1264 0 0 0-77.0368 77.0432v568.7744a77.1328 77.1328 0 0 0 77.0368 77.0432h568.7744a77.1328 77.1328 0 0 0 77.0432-77.0432v-46.4768a32.0768 32.0768 0 1 1 64.1536 0v46.4768A141.3568 141.3568 0 0 1 799.5904 940.8z m-228.3008-256.64a32.0832 32.0832 0 0 1-32.0768-32.0832V416.768a34.7968 34.7968 0 0 1 63.2064-20.096l106.8096 150.4768v-138.88a32.0832 32.0832 0 0 1 64.16 0V639.232a34.7968 34.7968 0 0 1-63.168 20.1472l-106.88-150.5216v143.1808a32.0832 32.0832 0 0 1-32.0512 32.0832z m-21.2224-250.3936l0.0576 0.0768z" fill="#FB3F50" ></path><path d="M448 570.432a31.7952 31.7952 0 1 0 0-63.584H313.6V441.1456h134.4a31.7952 31.7952 0 1 0 0-63.584H294.1312A44.4352 44.4352 0 0 0 249.6 421.7984v225.2032a44.4352 44.4352 0 0 0 44.5312 44.2368H448a31.7952 31.7952 0 1 0 0-63.584H313.6V570.432h134.4z" fill="#FFCA1E" ></path></symbol></svg>', (function (n) { var t = (t = document.getElementsByTagName('script'))[t.length - 1], e = t.getAttribute('data-injectcss'), t = t.getAttribute('data-disable-injectsvg'); if (!t) { var i, o, a, d, c, s = function (t, e) { e.parentNode.insertBefore(t, e) }; if (e && !n.__iconfont__svg__cssinject__) { n.__iconfont__svg__cssinject__ = !0; try { document.write('<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>') } catch (t) { console && console.log(t) } }i = function () { var t, e = document.createElement('div'); e.innerHTML = n._iconfont_svg_string_, (e = e.getElementsByTagName('svg')[0]) && (e.setAttribute('aria-hidden', 'true'), e.style.position = 'absolute', e.style.width = 0, e.style.height = 0, e.style.overflow = 'hidden', e = e, (t = document.body).firstChild ? s(e, t.firstChild) : t.appendChild(e)) }, document.addEventListener ? ~['complete', 'loaded', 'interactive'].indexOf(document.readyState) ? setTimeout(i, 0) : (o = function () { document.removeEventListener('DOMContentLoaded', o, !1), i() }, document.addEventListener('DOMContentLoaded', o, !1)) : document.attachEvent && (a = i, d = n.document, c = !1, r(), d.onreadystatechange = function () { d.readyState == 'complete' && (d.onreadystatechange = null, l()) }) } function l () { c || (c = !0, a()) } function r () { try { d.documentElement.doScroll('left') } catch (t) { return void setTimeout(r, 50) }l() } }(window))
