import { post, get } from '@/axios/index'
// import APIUrl from '@/axios/api.url'

// var img_url = APIUrl.util.image // 这个就是图片上传的api url

// ==================== POLYGON API - TICKERS 模块 ====================

// 获取股票列表
export function getTickers (options) {
  return get('/api/polygon/getTickers.do', options)
}

// 获取股票详细信息
export function getTickerDetails (options) {
  return get('/api/polygon/getTickerDetails.do', options)
}

// 获取相关股票
export function getRelatedTickers (options) {
  return get('/api/polygon/getRelatedTickers.do', options)
}

// 获取股票新闻
export function getTickerNews (options) {
  return get('/api/polygon/getTickerNews.do', options)
}

// 获取股票类型
export function getTickerTypes (options) {
  return get('/api/polygon/getTickerTypes.do', options)
}

// ==================== POLYGON API - AGGREGATES 模块 ====================

// 获取K线数据
export function getAggregates (options) {
  return get('/api/polygon/getAggregates.do', options)
}

// 获取市场日线数据
export function getGroupedDaily (options) {
  return get('/api/polygon/getGroupedDaily.do', options)
}

// 获取日开收数据
export function getDailyOpenClose (options) {
  return get('/api/polygon/getDailyOpenClose.do', options)
}

// 获取前收盘价
export function getPreviousClose (options) {
  return get('/api/polygon/getPreviousClose.do', options)
}

// ==================== POLYGON API - TRADES & QUOTES 模块 ====================

// 获取交易记录
export function getTrades (options) {
  return get('/api/polygon/getTrades.do', options)
}

// 获取报价数据
export function getQuotes (options) {
  return get('/api/polygon/getQuotes.do', options)
}

// 获取最新交易
export function getLastTrade (options) {
  return get('/api/polygon/getLastTrade.do', options)
}

// 获取最新报价
export function getLastQuote (options) {
  return get('/api/polygon/getLastQuote.do', options)
}

// ==================== POLYGON API - SNAPSHOTS 模块 ====================

// 获取所有股票快照
export function getAllTickersSnapshot (options) {
  return get('/api/polygon/getAllTickersSnapshot.do', options)
}

// 获取单个股票快照
export function getTickerSnapshot (options) {
  return get('/api/polygon/getTickerSnapshot.do', options)
}

// 获取涨幅榜
export function getGainersSnapshot (options) {
  return get('/api/polygon/getGainersSnapshot.do', options)
}

// 获取跌幅榜
export function getLosersSnapshot (options) {
  return get('/api/polygon/getLosersSnapshot.do', options)
}

// ==================== POLYGON API - REFERENCE DATA 模块 ====================

// 获取交易所信息
export function getExchanges (options) {
  return get('/api/polygon/getExchanges.do', options)
}

// 获取市场状态
export function getMarketStatus (options) {
  return get('/api/polygon/getMarketStatus.do', options)
}

// 获取市场假期
export function getMarketHolidays (options) {
  return get('/api/polygon/getMarketHolidays.do', options)
}

// 获取股票分割信息
export function getStockSplits (options) {
  return get('/api/polygon/getStockSplits.do', options)
}

// 获取股息信息
export function getDividends (options) {
  return get('/api/polygon/getDividends.do', options)
}

// 获取交易条件
export function getConditions (options) {
  return get('/api/polygon/getConditions.do', options)
}

// ==================== POLYGON API - FUNDAMENTALS 模块 ====================

// 获取财务数据
export function getStockFinancials (options) {
  return get('/api/polygon/getStockFinancials.do', options)
}

// ======================== WEBSOCKET 模块 - 实时数据 ========================

// 订阅实时数据
export function subscribeRealTime (options) {
  return post('/api/polygon/subscribeRealTime.do', options)
}

// 取消订阅实时数据
export function unsubscribeRealTime (options) {
  return post('/api/polygon/unsubscribeRealTime.do', options)
}

// 获取最新价格
export function getLatestPrice (options) {
  return get('/api/polygon/getLatestPrice.do', options)
}

// 批量获取最新价格
export function getLatestPrices (options) {
  return get('/api/polygon/getLatestPrices.do', options)
}

// 获取订阅状态
export function getSubscriptionStatus (options) {
  return get('/api/polygon/getSubscriptionStatus.do', options)
}

// 重连WebSocket
export function reconnectWebSocket (options) {
  return post('/api/polygon/reconnectWebSocket.do', options)
}

// 断开WebSocket
export function disconnectWebSocket (options) {
  return post('/api/polygon/disconnectWebSocket.do', options)
}

// A股特有功能已移除 - 每日停复牌、十大成交股、涨停相关

export function getKLineData (options) {
  return get('/api/stock/getKline.do', options)
}
export function getKLinePirce (options) {
  return get('/api/stock/getPrice.do', options)
}
// 就可以使用 post 和 get 了
export function queryIndexNews (options) {
  return post('/api/index/queryIndexNews.do', options)
}
// 获取产品配置信息
export function getProductSetting (options) {
  return post('/api/admin/getProductSetting.do', options)
}

export function submitSubscribe (options) {
  return post('/user/submitSubscribe.do', options)
}
// 大宗交易记录
export function buyStockDzList (options) {
  return post('/user/buyStockDzList.do', options)
}
// 登录
export function login (options) {
  return post('/api/user/login.do', options)
}

// 注册
export function register (options) {
  return post('/api/user/reg.do', options)
}

// 邮箱注册
export function registerWithEmail (options) {
  return post('/api/sms/regWithEmail.do', options)
}

// 注销登录
export function logout (options) {
  return post('/api/user/logout.do', options)
}

// 验证是否注册
export function checkPhone (options) {
  return post('/api/user/checkPhone.do', options)
}

// 更改密码 -- 忘记密码
export function forgetPas (options) {
  return get('/api/user/updatePwd.do', options)
}

// 修改密码
export function changePassword (options) {
  return get('/user/updatePwd.do', options)
}

// 获取验证码  -- 注册
export function getCode (options) {
  return get('/api/sms/sendRegSms.do', options)
}

// 获取验证码  -- 忘记密码
export function sendForgetSms (options) {
  return get('/api/sms/sendForgetSms.do', options)
}

// 发送邮箱验证码 -- 注册
export function sendRegEmail (options) {
  return post('/api/sms/sendRegEmail.do', options)
}

// 发送邮箱验证码 -- 忘记密码
export function sendForgetEmail (options) {
  return post('/api/sms/sendForgetEmail.do', options)
}

// 获取图片验证码   -- 查看验证码
export function getCode2 (options) {
  return get('/code/getCode.do', options)
}

// 验证图片验证码 -- 验证
export function checkCode (options) {
  return get('/code/checkCode.do', options)
}

// /*** 首页 ****/
// 查询首页显示的指数
export function getIndexMarket (options) {
  return get('/api/index/queryHomeIndex.do', options)
}

// 查询列表页显示的指数
export function getListMarket (options) {
  return get('/api/index/queryListIndex.do', options)
}

// 查询指数是否可交易
export function getTransMarket (options) {
  return get('/api/index/queryTransIndex.do', options)
}

// 获取大盘指数
export function getMarket (options) {
  return get('/api/stock/getMarket.do', options)
}

// 股票列表数据
export function getStock (options) {
  return get('/api/stock/getStock.do', options)
}

// 单只股票行情数据
export function getSingleStock (options) {
  return post('/api/stock/getSingleStock.do', options)
}

// 单只指数行情数据
export function getSingleIndex (options) {
  return post('/api/index/querySingleIndex.do', options)
}

// 添加自选
export function addOption (options) {
  return post('/user/addOption.do', options)
}

// 删除自选
export function delOption (options) {
  return post('/user/delOption.do', options)
}

// 获取期货列表
export function getFutures (options) {
  return get('/api/futures/queryList.do', options)
}

// 获取首页显示的期货列表
export function getHomeFutures (options) {
  return get('/api/futures/queryHome.do', options)
}

// 获取期货列表
export function getListFutures (options) {
  return get('/api/futures/queryList.do', options)
}

// 查询期货产品的交易状态
export function queryTrans (options) {
  return get('/api/futures/queryTrans.do', options)
}

// 查询基币的汇率，对外暴露
export function queryExchange (options) {
  return get('/api/futures/queryExchange.do', options)
}

// 查询单个期货产品的行情（行情源的数据）
export function querySingleMarket (options) {
  return get('/api/futures/querySingleMarket.do', options)
}

// A股特有功能已移除 - 新股申购、大宗交易、VIP抢筹相关

// 交易详情
export function findBySn (options) {
  return get('/user/position/findBySn.do', options)
}

// A股特有功能已移除 - 大宗交易下单、新股申购相关

// VIP抢筹下单
export function buyVipQc (options) {
  return post('/user/buyVipQc.do', options)
}

// 期货下单
export function buyFutures (options) {
  return post('/user/buyFutures.do', options)
}

// 挂单
export function guadan (options) {
  return post('/user/addOrder.do', options)
}

// 删除挂单
export function delGuaDan (options) {
  return post('/user/delOrder.do', options)
}
// 挂单列表
export function getorderList (options) {
  return post('/user/orderList.do', options)
}

// 美股详情
export function getUsDetail (options) {
  return post('/api/stock/getSingleStockByCode.do', options)
}

// 下单
export function buy (options) {
  return post('/user/buy.do', options)
}

// 指数下单
export function indexBuy (options) {
  return post('/user/buyIndex.do', options)
}

// 用户平仓
export function sell (options) {
  return post('/user/sell.do', options)
}

// 指数平仓
export function sellIndex (options) {
  return post('/user/sellIndex.do', options)
}

// 期货平仓
export function sellFutures (options) {
  return post('/user/sellFutures.do', options)
}

// /***用户中心***/
// 用户资金户转
export function AmtChange (options) {
  return post('/user/transAmt.do', options)
}

// 用户详情
export function getUserInfo (options) {
  return post('/user/getUserInfo.do', options)
}

// 添加银行卡
export function addBankCard (options) {
  return post('/user/bank/add.do', options)
}

// 修改银行卡
export function updateBankCard (options) {
  return post('/user/bank/update.do', options)
}

// 获取银行卡信息
export function getBankCard (options) {
  return post('/user/bank/getBankInfo.do', options)
}

// 获取我的持仓单
export function getOrderList (options) {
  return post('/user/position/list.do', options)
}

// 获取我的持仓单 - 指数
export function getIndexOrderList (options) {
  return post('/user/index/position/list.do', options)
}

// 获取我的持仓单 - 期货
export function getFuturesOrderList (options) {
  return post('/user/futures/position/list.do', options)
}

// 获取我的自选列表
export function getMyList (options) {
  return get('/user/option/list.do', options)
}

// 实名认证
export function userAuth (options) {
  return post('/user/auth.do', options)
}

// 资金明细
export function cashDetail (options) {
  return post('/user/cash/list.do', options)
}

// 提现记录
export function rechargeList (options) {
  return post('/user/recharge/list.do', options)
}

// 充值记录
export function withdrawList (options) {
  return post('/user/withdraw/list.do', options)
}

// 充值
export function inMoney (options) {
  return post('/user/recharge/inMoney.do', options)
}

// 提现
export function outMoney (options) {
  return post('/user/withdraw/outMoney.do', options)
}

// 取消提现
export function canceloutMoney (options) {
  return post('/user/withdraw/cancel.do', options)
}

// k线图
export function getMinK (options) {
  return post('/api/stock/getMinK.do', options)
}

// k线图
export function getMinKEcharts (options) {
  return post('/api/stock/getMinK_Echarts.do', options)
}

// 是否已添加自选
export function isOption (options) {
  return post('/user/isOption.do', options)
}

// 获取网站设置信息
export function getSetting (options) {
  return post('/api/admin/getSetting.do', options)
}

// 获取指数网站设置信息
export function getIndexSetting (options) {
  return post('/api/admin/getIndexSetting.do', options)
}

// 获取期货网站设置信息
export function getFuturesSetting (options) {
  return post('/api/admin/getFuturesSetting.do', options)
}

// 获取首页banner
export function getBannerByPlat (options) {
  return post('/api/site/getBannerByPlat.do', options)
}

// 公告列表
export function getArtList (options) {
  return post('/api/art/list.do', options)
}

// 公告详情
export function getArtDetail (options) {
  return post('/api/art/detail.do', options)
}

// 获取支付渠道
export function getPayInfo (options) {
  return post('/api/site/getPayInfo.do', options)
}

// 获取单个渠道信息
export function getPayInfoDetail (options) {
  return post('/api/site/getPayInfoById.do', options)
}

// 获取网站设置信息
export function getInfoSite (options) {
  return post('/api/site/getInfo.do', options)
}

// k线图 分时
export function getMinuteLine (options) {
  return post('/api/realTime/findStock.do', options)
}

// 新增渠道  支付宝扫码
export function getjuhe1 (options) {
  return post('/user/pay/juhe1.do', options)
}

// H5支付
export function getjuheH5 (options) {
  return post('/user/pay/juheh5.do', options)
}

// 支付渠道
export function payLInk (url, options) {
  return post(url, options)
}

// 图片上传 uploadimg
export function uploadimg (options) {
  return post('/user/upload.do', options)
}

// 查询点差费率
export function findSpreadRateOne (options) {
  return post('/api/user/findSpreadRateOne.do', options)
}

// ==================最新修改内容：日线、添加自选等，2020年7月10日15:37:20======================
// 期货分钟-k线图
export function getFuturesMinKEcharts (options) {
  return post('/api/stock/getFuturesMinK_Echarts.do', options)
}

// 指数分钟-k线图
export function getIndexMinKEcharts (options) {
  return post('/api/stock/getIndexMinK_Echarts.do', options)
}

// 股票日线图
export function getDayK (options) {
  return post('/api/stock/getDayK.do', options)
}

// 期货日线图
export function getFuturesDayK (options) {
  return post('/api/stock/getFuturesDayK.do', options)
}

// 指数日线图
export function getIndexDayK (options) {
  return post('/api/stock/getIndexDayK.do', options)
}

// 查询期货详情
export function queryFuturesByCode (options) {
  return get('/api/futures/queryFuturesByCode.do', options)
}

// ==================最新修改内容：新版-新闻资讯、交易大厅，2020年8月26日10:39======================

// 查询期货详情
export function queryNewsList (type) {
  return get(`/api/news/getNewsList.do?pageNum=1&pageSize=15&type=${type}`, {})
}

// 查询新闻详情
export function queryNewsDetail (type) {
  return get(`/api/news/getDetail.do?id=${type}`, {})
}

// A股特有功能已移除 - 配资申请相关

// A股特有功能已移除 - 分仓配资相关

// 获取消息列表
export function getNoticeList (options) {
  return post('/user/cash/getMessagelist.do', options)
}

// A股特有功能已移除 - 配资持仓和平仓相关

export function getZs () {
  return get('https://69.push2.eastmoney.com/api/qt/clist/get?pn=1&pz=25&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=|0|0|0|web&fid=&fs=b:MK0010&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26,f22,f11,f62,f128,f136,f115,f152&_=1666359071216')
}

// A股特有功能已移除 - 股票排序、资金密码、VIP抢筹、港币汇率兑换相关
// 获取验证码
export function sendRegSms (options) {
  return post(`/api/sms/sendRegSms.do`, options)
}
