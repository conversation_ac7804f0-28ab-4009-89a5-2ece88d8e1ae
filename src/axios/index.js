// 蓝湖的话，暂时用这个账号打开：<EMAIL>   cxcxx1205
// 手机端：http://www.shehua56.com/wap2.0/
// 账户：15888888888     密码:123456

import axios from 'axios' // 引入axios
import qs from 'qs' // 引入qs
import store from '@/store' // 引入 store
import router from '@/router'
import APIUrl from './api.url' // 引入api.url.js
import { Toast } from 'mint-ui' // 引入 Toast 提示

// axios 默认配置  更多配置查看Axios中文文档
axios.defaults.timeout = 50000 // 超时默认值
axios.defaults.baseURL = APIUrl.baseURL // 默认baseURL
axios.defaults.responseType = 'json' // 默认数据响应类型
axios.defaults.headers.common['Content-Type'] = 'application/json;charset=UTF-8'
// axios.defaults.headers.common['Content-Type'] = 'application/x-www-form-urlencoded';
axios.defaults.withCredentials = true // 表示跨域请求时是否需要使用凭证

// http request 拦截器
// 在ajax发送之前拦截 比如对所有请求统一添加header token
axios.interceptors.request.use(
  config => {
    // 添加语言设置
    config.headers['lang'] = localStorage.getItem('language') ? localStorage.getItem('language') : 'zh-CN'

    // 添加 token 到请求头
    const token = store.getters.token || localStorage.getItem('USERTOKEN')
    if (token) {
      config.headers['USERTOKEN'] = token
    }

    return config
  },
  err => {
    return Promise.reject(err)
  }
)

// http response 拦截器
// ajax请求回调之前拦截 对请求返回的信息做统一处理 比如error为401无权限则跳转到登陆界面
axios.interceptors.response.use(
  response => {

    // 检查响应数据
    if (response.data && response.data.msg) {
      const msg = response.data.msg

      // 检查是否需要登录（支持多种登录提示信息）
      const loginMessages = [
        '请先登录',
        '請先登錄',
        '請先登陸',
        '请重新登录',
        '请重新登入',
        '登录已过期',
        '未登录',
        '需要登录'
      ]

      const needLogin = loginMessages.some(loginMsg => msg.indexOf(loginMsg) > -1)

      if (needLogin) {
        // 清除 store 中的登录状态
        store.dispatch('logout')

        // 显示提示信息
        Toast({
          message: '登录已过期，请重新登录',
          position: 'top',
          duration: 2000
        })

        // 跳转到登录页面
        const currentPath = router.currentRoute.fullPath

        // 避免递归重定向：如果当前已经在登录页面，不添加重定向参数
        if (currentPath.includes('/futu-login')) {
          router.replace('/futu-login')
        } else {
          // 清理路径，确保不包含已有的重定向参数
          let cleanPath = currentPath
          if (cleanPath.includes('redirect=')) {
            const url = new URL(cleanPath, window.location.origin)
            url.searchParams.delete('redirect')
            cleanPath = url.pathname + url.search
          }

          router.replace({
            path: '/futu-login',
            query: {
              redirect: cleanPath || '/watchlist'
            }
          })
        }

        // 返回一个被拒绝的 Promise，阻止后续处理
        return Promise.reject(new Error('需要登录'))
      }
    }

    // 检查接口是否成功
    if (response.data && response.data.status === 0 && response.data.success === true) {
      // 接口调用成功
      return response
    } else if (response.data && response.data.status !== 0) {
      // 接口调用失败，但不是登录问题

      // 可以在这里添加全局错误提示
      if (response.data.msg && response.data.msg !== '请先登录') {
        Toast({
          message: response.data.msg,
          position: 'top',
          duration: 2000
        })
      }
    }

    return response
  },
  error => {
    // 网络错误或其他错误
    if (error.response) {
      // 服务器返回了错误状态码
      const status = error.response.status
      let message = '请求失败'

      switch (status) {
        case 401:
          message = '未授权，请重新登录'
          store.dispatch('logout')
          router.replace('/futu-login')
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = `请求失败 (${status})`
      }

      Toast({
        message,
        position: 'top',
        duration: 2000
      })
    } else if (error.request) {
      // 请求已发出但没有收到响应
      Toast({
        message: '网络连接失败，请检查网络',
        position: 'top',
        duration: 2000
      })
    } else {
      // 其他错误
      Toast({
        message: error.message || '请求失败',
        position: 'top',
        duration: 2000
      })
    }

    return Promise.reject(error)
  }
)

export default axios // 这句千万不能漏下！！！

/**
 * post 方法封装
 * @param url
 * @param data
 * @returns {Promise}
 */
export function post (url, data = {}, out) {
  return new Promise((resolve, reject) => {
    axios.post(url, qs.stringify(data))
      .then(response => {
        resolve(response.data)
      }, err => {
        reject(err)
      })
  })
}

/**
 * get 方法封装
 * @param url
 * @param data
 * @returns {Promise}
 */
export function get (url, data = {}) {
  return new Promise((resolve, reject) => {
    axios.get(url, { params: data })
      .then(response => {
        resolve(response.data)
      }, err => {
        reject(err)
      })
  })
}

/**
 * 其他delete等的封装类似
 * 可以查看中文文档 自行封装
 */
