<template>
  <div class="futu-tabbar">
    <van-tabbar v-model="activeTab" @change="onTabChange" active-color="#FF6600" inactive-color="#999999">
      <!-- 自选 -->
      <van-tabbar-item name="watchlist">
        <template #icon="props">
          <van-icon :name="props.active ? 'like' : 'like-o'" size="22px" />
        </template>
        <span>Watchlists</span>
      </van-tabbar-item>

      <!-- 市场 -->
      <van-tabbar-item name="market">
        <template #icon="props">
          <van-icon :name="props.active ? 'chart-trending-o' : 'chart-trending-o'" size="22px" />
        </template>
        <span>Markets</span>
      </van-tabbar-item>

      <!-- 开户 -->
      <van-tabbar-item name="open-account">
        <template #icon="props">
          <van-icon :name="props.active ? 'point-gift-o' : 'point-gift-o'" size="22px" />
        </template>
        <span>Open Account</span>
      </van-tabbar-item>

      <!-- 理财 -->
      <van-tabbar-item name="wealth">
        <template #icon="props">
          <van-icon :name="props.active ? 'bar-chart-o' : 'bar-chart-o'" size="22px" />
        </template>
        <span>Wealth</span>
      </van-tabbar-item>

      <!-- 发现 -->
      <van-tabbar-item name="more">
        <template #icon="props">
          <van-icon :name="props.active ? 'apps-o' : 'apps-o'" size="22px" />
        </template>
        <span>More</span>
      </van-tabbar-item>

      <!-- 我的 -->
      <van-tabbar-item name="profile">
        <template #icon="props">
          <van-icon :name="props.active ? 'user-o' : 'user-o'" size="22px" />
        </template>
        <span>Me</span>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
export default {
  name: 'FutuTabbar',
  data () {
    return {
      activeTab: 'watchlist'
    }
  },
  methods: {
    onTabChange (name) {
      this.activeTab = name
      this.$emit('tab-change', name)

      // 路由跳转
      switch (name) {
        case 'market':
          this.$router.push('/market')
          break
        case 'watchlist':
          this.$router.push('/watchlist')
          break
        case 'open-account':
          this.$router.push('/open-account')
          break
        case 'wealth':
          this.$router.push('/wealth')
          break
        case 'more':
          this.$router.push('/discover')
          break
        case 'profile':
          this.$router.push('/profile')
          break
      }
    },

    // 根据当前路由设置活跃tab
    setActiveTabFromRoute () {
      const path = this.$route.path
      if (path === '/market') {
        this.activeTab = 'market'
      } else if (path === '/watchlist') {
        this.activeTab = 'watchlist'
      } else if (path.startsWith('/open-account')) {
        this.activeTab = 'open-account'
      } else if (path === '/wealth' || path === '/trading' || path.includes('/buyStocks')) {
        this.activeTab = 'wealth'
      } else if (path === '/discover') {
        this.activeTab = 'more'
      } else if (path === '/profile' || path === '/user' || path.includes('/user/')) {
        this.activeTab = 'profile'
      }
    }
  },

  mounted () {
    this.setActiveTabFromRoute()
  },

  watch: {
    '$route' () {
      this.setActiveTabFromRoute()
    }
  }
}
</script>

<style scoped>
.futu-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border-top: 1px solid #E5E5E5;
}

/* 富途橙色主题 */
:root {
  --futu-primary: #FF6600;
  --futu-red: #FF6B6B;
  --futu-green: #51CF66;
  --futu-gray: #999999;
  --futu-bg: #F8F9FA;
  --futu-border: #E5E5E5;
}

::v-deep .van-tabbar {
  background: #fff;
  border-top: 1px solid var(--futu-border);
  height: 60px;
}

::v-deep .van-tabbar-item {
  color: #999999;
  font-size: 11px;
  padding: 6px 4px 4px;
  flex: 1;
}

::v-deep .van-tabbar-item--active {
  color: var(--futu-primary);
}

::v-deep .van-tabbar-item__icon {
  margin-bottom: 2px;
  font-size: 22px;
}

::v-deep .van-tabbar-item__text {
  margin-top: 2px;
  font-weight: 400;
  font-size: 11px;
  line-height: 1.2;
}

::v-deep .van-icon {
  transition: color 0.2s ease;
}
</style>
