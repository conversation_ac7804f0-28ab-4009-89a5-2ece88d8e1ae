<template>
  <div class="market-depth">
    <!-- 买卖盘标题和比例 -->
    <div class="depth-header">
      <div class="buy-section">
        <span class="section-title">买盘</span>
      </div>
      <div class="ratio-section">
        <span class="buy-ratio">{{ buyRatio }}%</span>
        <div class="ratio-bar">
          <div class="buy-bar" :style="{ width: buyRatio + '%' }"></div>
          <div class="sell-bar" :style="{ width: sellRatio + '%' }"></div>
        </div>
        <span class="sell-ratio">{{ sellRatio }}%</span>
      </div>
      <div class="sell-section">
        <span class="section-title">卖盘</span>
      </div>
      <div class="building-icon">🏛️</div>
    </div>

    <!-- 当前价格行 -->
    <div class="current-price-row">
      <div class="price-left">
        <span class="exchange">{{ currentPrice.exchange }}</span>
        <span class="current-price">{{ currentPrice.bid }}</span>
        <span class="volume">{{ currentPrice.bidVolume }}</span>
      </div>
      <div class="price-right">
        <span class="exchange">{{ currentPrice.exchange }}</span>
        <span class="current-price">{{ currentPrice.ask }}</span>
        <span class="volume">{{ currentPrice.askVolume }}</span>
      </div>
    </div>

    <!-- 深度摆盘标题 -->
    <div class="depth-title">
      <span class="title-text">深度摆盘</span>
      <span class="info-icon">ⓘ</span>
      <div class="title-right">
        <span class="building-icon-small">🏛️</span>
        <span class="count">({{ depthData.length }})</span>
        <span class="menu-icon">☰</span>
        <span class="count">{{ depthData.length }}</span>
      </div>
    </div>

    <!-- 深度数据内容 -->
    <div class="depth-content">
      <div class="depth-row" v-for="(row, index) in depthData" :key="index">
        <!-- 买盘 -->
        <div class="buy-side">
          <div class="depth-item">
            <span class="exchange">{{ row.exchange }}</span>
            <span class="price buy-price">{{ row.buyPrice }}</span>
            <span class="volume">{{ row.buyVolume }}</span>
            <div class="depth-bar buy-depth-bar"
                 :class="'volume-level-' + row.buyVolumeLevel"
                 :style="{ width: row.buyDepthWidth + '%' }"></div>
          </div>
        </div>

        <!-- 卖盘 -->
        <div class="sell-side">
          <div class="depth-item">
            <span class="exchange">{{ row.exchange }}</span>
            <span class="price sell-price">{{ row.sellPrice }}</span>
            <span class="volume">{{ row.sellVolume }}</span>
            <div class="depth-bar sell-depth-bar"
                 :class="'volume-level-' + row.sellVolumeLevel"
                 :style="{ width: row.sellDepthWidth + '%' }"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <!-- <div v-if="loading" class="loading-container">
      <div class="loading-spinner">📊 正在加载深度数据...</div>
    </div> -->

    <!-- 错误状态 -->
    <div v-if="error && !loading" class="error-container">
      <div class="error-message">{{ error }}</div>
      <button class="retry-btn" @click="loadDepthData">重试</button>
    </div>
  </div>
</template>

<script>
import { getTrades, getQuotes, getLastTrade, getLastQuote, getTickerSnapshot } from '@/axios/api'

export default {
  name: 'MarketDepth',
  props: {
    ticker: {
      type: String,
      required: true,
      default: 'NVDA'
    }
  },
  data() {
    return {
      // 买卖比例
      buyRatio: 50.0,
      sellRatio: 50.0,

      // 当前价格
      currentPrice: {
        exchange: 'NASDAQ',
        bid: '--',
        ask: '--',
        bidVolume: '--',
        askVolume: '--'
      },

      // 深度数据
      depthData: [],

      // 加载状态
      loading: false,
      error: null,

      // 刷新定时器
      refreshTimer: null,

      // 实际获取到的数据
      rawQuotes: [],
      rawTrades: [],
      lastQuoteData: null,
      lastTradeData: null
    }
  },
  mounted() {
    this.loadDepthData()
    this.startAutoRefresh()
  },
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },
  watch: {
    ticker: {
      handler(newTicker) {
        if (newTicker) {
          this.loadDepthData()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 加载深度数据
    async loadDepthData() {
      if (!this.ticker) {
        console.warn('📊 [MarketDepth] 没有提供股票代码')
        return
      }

      try {
        this.loading = true
        this.error = null

                // 创建API调用的Promise数组，每个都包装错误处理
        const apiCalls = [
          // 获取最新报价 (买卖价格)
          getLastQuote({ ticker: this.ticker }).catch(error => ({ error, type: 'lastQuote' })),

          // 获取最新交易 (最新成交价)
          getLastTrade({ ticker: this.ticker }).catch(error => ({ error, type: 'lastTrade' })),

          // 获取报价数据 (买卖盘深度)
          getQuotes({
            ticker: this.ticker,
            order: 'desc',
            limit: 20,
            sort: 'timestamp'
          }).catch(error => ({ error, type: 'quotes' })),

          // 获取交易记录 (成交记录)
          getTrades({
            ticker: this.ticker,
            order: 'desc',
            limit: 50,
            sort: 'timestamp'
          }).catch(error => ({ error, type: 'trades' })),

          // 获取股票快照数据
          getTickerSnapshot({ ticker: this.ticker }).catch(error => ({ error, type: 'snapshot' }))
        ]

        // 等待所有API调用完成
        const [lastQuoteResponse, lastTradeResponse, quotesResponse, tradesResponse, snapshotResponse] = await Promise.all(apiCalls)
        // 处理最新报价数据
        if (lastQuoteResponse && !lastQuoteResponse.error && lastQuoteResponse.success) {
          this.lastQuoteData = lastQuoteResponse.data && lastQuoteResponse.data.results
          this.updateCurrentPriceFromLastQuote(this.lastQuoteData)
        } else if (lastQuoteResponse && lastQuoteResponse.error) {
          console.warn('📊 [MarketDepth] 最新报价获取失败:', lastQuoteResponse.error)
        }

        // 处理最新交易数据
        if (lastTradeResponse && !lastTradeResponse.error && lastTradeResponse.success) {
          this.lastTradeData = lastTradeResponse.data && lastTradeResponse.data.results
        } else if (lastTradeResponse && lastTradeResponse.error) {
          console.warn('📊 [MarketDepth] 最新交易获取失败:', lastTradeResponse.error)
        }

        // 处理股票快照数据（包含更多实时信息）
        if (snapshotResponse && !snapshotResponse.error && snapshotResponse.success) {
          const snapshotData = snapshotResponse.data && snapshotResponse.data.results
          if (snapshotData) {
            this.updateCurrentPriceFromSnapshot(snapshotData)
          }
        } else if (snapshotResponse && snapshotResponse.error) {
          console.warn('📊 [MarketDepth] 股票快照获取失败:', snapshotResponse.error)
        }

        // 处理报价数据（买卖盘深度）
        if (quotesResponse && !quotesResponse.error && quotesResponse.success) {
          this.rawQuotes = (quotesResponse.data && quotesResponse.data.results) || []

          // next_url 是Polygon API的分页参数，用于获取下一页数据
          if (quotesResponse.data && quotesResponse.data.next_url) {
            console.log('📊 [MarketDepth] 报价数据有下一页:', quotesResponse.data.next_url)
          }
        } else if (quotesResponse && quotesResponse.error) {
          console.warn('📊 [MarketDepth] 报价数据获取失败:', quotesResponse.error)
        }

        // 处理交易数据
        if (tradesResponse && !tradesResponse.error && tradesResponse.success) {
          this.rawTrades = (tradesResponse.data && tradesResponse.data.results) || []

          // next_url 是Polygon API的分页参数，用于获取下一页数据
          if (tradesResponse.data && tradesResponse.data.next_url) {
            console.log('📊 [MarketDepth] 交易数据有下一页:', tradesResponse.data.next_url)
          }
        } else if (tradesResponse && tradesResponse.error) {
          console.warn('📊 [MarketDepth] 交易数据获取失败:', tradesResponse.error)
        }

        // 生成深度盘口数据
        this.generateDepthData()

        this.loading = false

      } catch (error) {
        console.error('❌ [MarketDepth] 加载深度数据失败:', error)
        this.error = '加载失败，请重试'
        this.loading = false
      }
    },

    // 从最新报价更新当前价格
    updateCurrentPriceFromLastQuote(data) {
      if (!data) return

      // Polygon API lastQuote 数据结构：
      // p = bid_price, P = ask_price, s = bid_size, S = ask_size, x = exchange
      this.currentPrice = {
        exchange: this.getExchangeName(data.x) || 'NASDAQ',
        bid: data.p ? parseFloat(data.p).toFixed(3) : '--',
        ask: data.P ? parseFloat(data.P).toFixed(3) : '--',
        bidVolume: data.s ? this.formatVolume(data.s) : '--',
        askVolume: data.S ? this.formatVolume(data.S) : '--'
      }
    },

    // 从股票快照更新当前价格（补充信息）
    updateCurrentPriceFromSnapshot(data) {
      if (!data) return

      // 如果有 lastQuote 信息，优先使用
      if (data.lastQuote) {
        const quote = data.lastQuote
        this.currentPrice = {
          exchange: this.getExchangeName(quote.x) || this.currentPrice.exchange,
          bid: quote.p ? parseFloat(quote.p).toFixed(3) : this.currentPrice.bid,
          ask: quote.P ? parseFloat(quote.P).toFixed(3) : this.currentPrice.ask,
          bidVolume: quote.s ? this.formatVolume(quote.s) : this.currentPrice.bidVolume,
          askVolume: quote.S ? this.formatVolume(quote.S) : this.currentPrice.askVolume
        }
      }

      // 如果 lastTrade 有更新的价格信息
      if (data.lastTrade && data.lastTrade.p) {
        const tradePrice = parseFloat(data.lastTrade.p).toFixed(3)
      }
    },

    // 生成深度盘口数据
    generateDepthData() {
      const depthRows = []

      // 获取基准价格
      let basePrice = this.getBasePrice()

      // 如果获取不到基准价格，使用默认值
      if (!basePrice) {
        basePrice = 150.000
        console.warn('📊 [MarketDepth] 使用默认基准价格:', basePrice)
      }

      // 生成10档买卖盘数据
      for (let i = 0; i < 10; i++) {
        // 计算买卖价格（买价递减，卖价递增）
        const buyPrice = (basePrice - (i + 1) * 0.01).toFixed(3)
        const sellPrice = (basePrice + (i + 1) * 0.01).toFixed(3)

        // 从实际数据中获取或生成合理的挂单量
        const buyVolume = this.getVolumeForPrice(buyPrice, 'buy', i)
        const sellVolume = this.getVolumeForPrice(sellPrice, 'sell', i)

        // 计算深度条宽度（根据挂单量）
        const maxVolume = 500 // 设定最大参考挂单量
        const buyDepthWidth = Math.max(8, Math.min(95, (buyVolume / maxVolume) * 100))
        const sellDepthWidth = Math.max(8, Math.min(95, (sellVolume / maxVolume) * 100))

        depthRows.push({
          exchange: this.currentPrice.exchange,
          buyPrice,
          buyVolume: buyVolume.toString(),
          buyDepthWidth: buyDepthWidth,
          buyVolumeLevel: this.calculateVolumeLevel(buyVolume),
          sellPrice,
          sellVolume: sellVolume.toString(),
          sellDepthWidth: sellDepthWidth,
          sellVolumeLevel: this.calculateVolumeLevel(sellVolume)
        })
      }

      this.depthData = depthRows
      this.updateBuySellRatio()

    },

    // 获取基准价格
    getBasePrice() {
      // 优先从最新报价获取中间价
      if (this.lastQuoteData && this.lastQuoteData.p && this.lastQuoteData.P) {
        const bidPrice = parseFloat(this.lastQuoteData.p)
        const askPrice = parseFloat(this.lastQuoteData.P)
        const midPrice = (bidPrice + askPrice) / 2
        return midPrice
      }

      // 从最新交易获取成交价
      if (this.lastTradeData && this.lastTradeData.price) {
        const tradePrice = parseFloat(this.lastTradeData.price)
        return tradePrice
      }

      // 从当前价格获取
      if (this.currentPrice.bid !== '--' && this.currentPrice.ask !== '--') {
        const bidPrice = parseFloat(this.currentPrice.bid)
        const askPrice = parseFloat(this.currentPrice.ask)
        if (!isNaN(bidPrice) && !isNaN(askPrice)) {
          const midPrice = (bidPrice + askPrice) / 2
          return midPrice
        }
      }
      return null
    },

    // 获取指定价格的挂单量
    getVolumeForPrice(price, side, level) {
      // 尝试从实际报价数据中获取
      if (this.rawQuotes.length > 0) {
        // 根据 level 从报价数据中获取对应档位的数据
        if (level < this.rawQuotes.length) {
          const quote = this.rawQuotes[level]
          if (side === 'buy' && quote.bid_size) {
            return parseInt(quote.bid_size)
          }
          if (side === 'sell' && quote.ask_size) {
            return parseInt(quote.ask_size)
          }
        }
      }

      // 生成合理的模拟挂单量
      // 一般来说，越接近当前价格的档位挂单量越大
      const baseVolume = 50 // 基础挂单量
      const levelMultiplier = Math.max(0.3, 1 - level * 0.1) // 档位越远，挂单量越少
      const randomFactor = 0.7 + Math.random() * 0.6 // 随机波动因子

      const volume = Math.floor(baseVolume * levelMultiplier * randomFactor * (1 + Math.random() * 3))
      return Math.max(10, volume) // 最小10股
    },


    // 更新买卖比例
    updateBuySellRatio() {
      if (this.depthData.length === 0) {
        this.buyRatio = 50.0
        this.sellRatio = 50.0
        return
      }

      // 计算所有档位的总挂单量
      const totalBuyVolume = this.depthData.reduce((sum, item) => sum + parseInt(item.buyVolume), 0)
      const totalSellVolume = this.depthData.reduce((sum, item) => sum + parseInt(item.sellVolume), 0)
      const totalVolume = totalBuyVolume + totalSellVolume

      if (totalVolume > 0) {
        this.buyRatio = parseFloat(((totalBuyVolume / totalVolume) * 100).toFixed(1))
        this.sellRatio = parseFloat(((totalSellVolume / totalVolume) * 100).toFixed(1))
      } else {
        this.buyRatio = 50.0
        this.sellRatio = 50.0
      }
    },

    // 计算挂单量等级
    calculateVolumeLevel(volume) {
      const vol = parseInt(volume) || 0

      if (vol <= 20) return 1        // 20股以下 - 很少
      if (vol <= 60) return 2        // 60股以下 - 一般
      if (vol <= 120) return 3       // 120股以下 - 较多
      if (vol <= 250) return 4       // 250股以下 - 很多
      return 5                       // 250股以上 - 最多
    },

    // 格式化交易所名称
    getExchangeName(exchange) {
      const exchangeMap = {
        1: 'NYSE', // 纽约证券交易所
        2: 'NASDAQ', // 纳斯达克
        3: 'NYSE American',
        4: 'NYSE Arca',
        // 可以根据需要添加更多交易所
      }
      return exchangeMap[exchange] || 'NASDAQ'
    },

    // 格式化成交量
    formatVolume(volume) {
      if (!volume) return '--'
      const vol = parseInt(volume)
      if (vol >= 1000000) {
        return (vol / 1000000).toFixed(1) + 'M'
      } else if (vol >= 1000) {
        return (vol / 1000).toFixed(1) + 'K'
      }
      return vol.toString()
    },

    // 开始自动刷新
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        this.loadDepthData()
      }, 2000) // 每10秒刷新一次
    }
  }
}
</script>

<style scoped>
.market-depth {
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
}

/* 深度盘口标题 */
.depth-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.buy-section, .sell-section {
  flex: 1;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.buy-section .section-title {
  color: #ff4444;
}

.sell-section {
  text-align: right;
}

.sell-section .section-title {
  color: #22c55e;
}

.ratio-section {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.buy-ratio {
  color: #ff4444;
  font-size: 12px;
  font-weight: 600;
  min-width: 45px;
  text-align: center;
}

.sell-ratio {
  color: #22c55e;
  font-size: 12px;
  font-weight: 600;
  min-width: 45px;
  text-align: center;
}

.ratio-bar {
  flex: 1;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
  display: flex;
}

.buy-bar {
  background: #ff4444;
  transition: width 0.3s ease;
}

.sell-bar {
  background: #22c55e;
  transition: width 0.3s ease;
}

.building-icon {
  font-size: 16px;
  margin-left: 8px;
}

/* 当前价格行 */
.current-price-row {
  display: flex;
  padding: 8px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.price-left, .price-right {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-right {
  justify-content: flex-end;
}

.exchange {
  font-size: 11px;
  color: #666;
  min-width: 40px;
}

.current-price {
  font-size: 14px;
  font-weight: 700;
  color: #22c55e;
  min-width: 60px;
  text-align: center;
}

.volume {
  font-size: 12px;
  color: #333;
  min-width: 30px;
  text-align: center;
}

/* 深度摆盘标题 */
.depth-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;
}

.title-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.info-icon {
  color: #999;
  font-size: 12px;
  margin-left: 4px;
}

.title-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

.building-icon-small {
  font-size: 12px;
}

.count {
  font-size: 11px;
  color: #666;
}

.menu-icon {
  font-size: 12px;
  color: #666;
}

/* 深度数据内容 */
.depth-content {
  background: #fff;
}

.depth-row {
  display: flex;
  border-bottom: 1px solid #f5f5f5;
}

.buy-side, .sell-side {
  flex: 1;
  position: relative;
}

.buy-side {
  border-right: 1px solid #f0f0f0;
  background: rgba(255, 182, 193, 0.2); /* 粉红色背景 */
}

.sell-side {
  background: rgba(144, 238, 144, 0.2); /* 浅绿色背景 */
}

.depth-item {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  position: relative;
  min-height: 32px;
  gap: 4px;
}

.buy-side .depth-item {
  justify-content: flex-start;
}

.sell-side .depth-item {
  justify-content: flex-start;
  padding-left: 40px;
}

.price {
  font-size: 13px;
  font-weight: 600;
  width: 70px;
  text-align: center;
  color: #22c55e; /* 统一使用绿色 */
  flex-shrink: 0;
}

.buy-price {
  color: #22c55e; /* 改为绿色 */
}

.sell-price {
  color: #22c55e; /* 保持绿色 */
}

.depth-item .exchange {
  font-size: 11px;
  color: #666;
  width: 50px;
  text-align: left;
  flex-shrink: 0;
}

.sell-side .depth-item .exchange {
  text-align: left;
}

.depth-item .volume {
  font-size: 12px;
  color: #333;
  width: 60px;
  text-align: center;
  font-weight: 500;
  flex-shrink: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.buy-side .depth-item .volume {
  text-align: center;
}

.sell-side .depth-item .volume {
  text-align: center;
}

/* 深度条 - 根据股数决定宽度，股数越多条越宽 */
.depth-bar {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 0;
  transition: width 0.3s ease;
  border-radius: 2px;
}

.buy-depth-bar {
  right: 0;
  background: linear-gradient(to left, rgba(255, 105, 135, 0.8), rgba(255, 105, 135, 0.4));
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.sell-depth-bar {
  left: 0;
  background: linear-gradient(to right, rgba(34, 197, 94, 0.8), rgba(34, 197, 94, 0.4));
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

/* 根据挂单数量调整深度条颜色强度 */
.depth-bar.volume-level-1 {
  opacity: 0.4; /* 挂单量很少 */
}

.depth-bar.volume-level-2 {
  opacity: 0.6; /* 挂单量一般 */
}

.depth-bar.volume-level-3 {
  opacity: 0.8; /* 挂单量较多 */
}

.depth-bar.volume-level-4 {
  opacity: 1.0; /* 挂单量很多 */
}

.depth-bar.volume-level-5 {
  opacity: 1.0; /* 挂单量最多 */
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  box-shadow: 0 0 8px rgba(255, 105, 135, 0.3);
}

.sell-depth-bar.volume-level-5 {
  box-shadow: 0 0 8px rgba(34, 197, 94, 0.3);
}

/* 大单闪烁效果 */
@keyframes shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 确保文字在深度条之上 */
.depth-item > * {
  position: relative;
  z-index: 1;
}

/* 悬停效果 */
.depth-row:hover {
  background: rgba(0, 0, 0, 0.02);
}

.depth-row:hover .buy-side {
  background: rgba(255, 182, 193, 0.3);
}

.depth-row:hover .sell-side {
  background: rgba(144, 238, 144, 0.3);
}

/* 加载状态样式 */
.loading-container {
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
}

.loading-spinner {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* 错误状态样式 */
.error-container {
  padding: 20px;
  text-align: center;
  background: #fff5f5;
  border: 1px solid #fed7d7;
}

.error-message {
  color: #e53e3e;
  font-size: 14px;
  margin-bottom: 10px;
}

.retry-btn {
  background: #3182ce;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.retry-btn:hover {
  background: #2c5aa0;
}

.retry-btn:active {
  background: #2a4a8a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .depth-header {
    padding: 8px 12px;
    font-size: 12px;
  }

  .current-price-row {
    padding: 6px 12px;
  }

  .depth-title {
    padding: 6px 12px;
  }

  .depth-item {
    padding: 3px 8px;
    min-height: 28px;
  }

  .price {
    font-size: 12px;
    min-width: 50px;
  }

  .depth-item .volume {
    font-size: 11px;
    min-width: 25px;
  }

  .loading-container, .error-container {
    padding: 16px;
  }

  .loading-spinner, .error-message {
    font-size: 13px;
  }

  .retry-btn {
    padding: 6px 12px;
    font-size: 11px;
  }
}
</style>
