<template>
  <van-dialog
    v-model="visible"
    :title="title"
    :show-cancel-button="true"
    :confirm-button-text="confirmText"
    :cancel-button-text="cancelText"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    :close-on-click-overlay="false"
    class="sell-confirm-dialog"
  >
    <div class="dialog-content">
      <div class="position-info">
        <div class="info-row">
          <span class="label">{{ $t('hj119') || '股票名称' }}:</span>
          <span class="value">{{ position.stockName || position.indexName || position.futuresName }}</span>
        </div>
        <div class="info-row">
          <span class="label">{{ $t('hj119') || '买入价格' }}:</span>
          <span class="value">{{ position.buyOrderPrice }}</span>
        </div>
        <div class="info-row">
          <span class="label">{{ $t('hj120') || '当前价格' }}:</span>
          <span class="value">{{ position.now_price }}</span>
        </div>
        <div class="info-row">
          <span class="label">{{ $t('hj117') || '持仓数量' }}:</span>
          <span class="value">{{ (position.orderNum / 100) + ($t('hj117') || '手') }}</span>
        </div>
        <div class="info-row">
          <span class="label">{{ $t('hj118') || '盈亏' }}:</span>
          <span class="value" :class="getProfitClass(position.profitAndLose)">
            {{ position.profitAndLose }}
          </span>
        </div>
      </div>

      <div class="warning-text" v-if="showWarning">
        <p>{{ warningText }}</p>
      </div>
    </div>
  </van-dialog>
</template>

<script>
export default {
  name: 'SellConfirmDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    position: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: String,
      default: 'stock' // stock, index, futures
    },
    warningText: {
      type: String,
      default: ''
    }
  },
  computed: {
    visible: {
      get () {
        return this.value
      },
      set (val) {
        this.$emit('input', val)
      }
    },
    title () {
      const typeMap = {
        stock: '股票平仓确认',
        index: '指数平仓确认',
        futures: '期货平仓确认'
      }
      return typeMap[this.type] || '平仓确认'
    },
    confirmText () {
      return '确认平仓'
    },
    cancelText () {
      return '取消'
    },
    showWarning () {
      return this.warningText && this.warningText.length > 0
    }
  },
  methods: {
    handleConfirm () {
      this.$emit('confirm')
      this.visible = false
    },
    handleCancel () {
      this.$emit('cancel')
      this.visible = false
    },
    getProfitClass (profit) {
      if (profit > 0) return 'profit-positive'
      if (profit < 0) return 'profit-negative'
      return ''
    }
  }
}
</script>

<style lang="less" scoped>
.sell-confirm-dialog {
  /deep/ .van-dialog__header {
    font-size: 0.42rem;
    font-weight: 600;
    padding: 0.4rem 0 0.3rem;
  }

  /deep/ .van-dialog__content {
    padding: 0 0.4rem 0.4rem;
  }

  /deep/ .van-dialog__footer {
    padding: 0.3rem 0.4rem;
  }

  /deep/ .van-dialog__cancel {
    color: #666;
    border: 1px solid #ddd;
  }

  /deep/ .van-dialog__confirm {
    background: #3773dd;
    color: #fff;
  }
}

.dialog-content {
  .position-info {
    background: #f8f8f8;
    border-radius: 0.2rem;
    padding: 0.3rem;
    margin-bottom: 0.3rem;

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.2rem 0;

      &:not(:last-child) {
        border-bottom: 1px solid #eee;
      }

      .label {
        font-size: 0.34rem;
        color: #666;
      }

      .value {
        font-size: 0.36rem;
        color: #333;
        font-weight: 500;

        &.profit-positive {
          color: #f5222d;
        }

        &.profit-negative {
          color: #52c41a;
        }
      }
    }
  }

  .warning-text {
    background: #fff5f5;
    border: 1px solid #ffdddd;
    border-radius: 0.15rem;
    padding: 0.3rem;

    p {
      margin: 0;
      font-size: 0.32rem;
      color: #f5222d;
      line-height: 1.5;
    }
  }
}
</style>
