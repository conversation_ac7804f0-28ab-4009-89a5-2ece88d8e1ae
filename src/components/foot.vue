<template>

</template>

<script>
export default {
  data () {
    return {
      touch: 0
    }
  },
  mounted () {

  },
  methods: {
    goRouter (url, index) {
      // if (index == 3 || index == 4) {
      //   if (window.localStorage.getItem('USERTOKEN') == "" || window.localStorage.getItem('USERTOKEN') == null || window.localStorage.getItem('USERTOKEN') == undefined) {
      //     this.$emit('close')
      //     return;
      //   }
      // }
      this.touch = index
      setTimeout(() => {
        this.touch = 0
      }, 500)
      this.$router.push(url)
      if (navigator.vibrate) {
        // 支持
        navigator.vibrate([55])
      }
    }
  }

}
</script>

<style>
.footCss {
  border-top: 0.01rem solid rgba(192, 192, 192, 0.1);
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 1.3rem;
  display: flex;
  justify-content: space-around;
}

.footDemo {
  width: 20%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  font-size: 0.32rem;
  color: #999;
}

.footDemos {
  width: 20%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  font-size: 0.32rem;
}

.footDemos::before {
  content: '';
  width: 0.9rem;
  height: 0.9rem;
  border-radius: 100%;
  position: absolute;
  background-color: rgba(25, 122, 246, 0.14);
  -webkit-animation: footBlueBg 0.5s linear infinite;
  animation: footBlueBg 0.5s linear infinite;
  transition: all 0.5s;
}

@-webkit-keyframes footBlueBg {
  0% {
    -webkit-transform: scale(1.6);
    transform: scale(1.6);
  }

  25% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
  }

  50% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  75% {
    background-color: rgba(25, 122, 246, 0.1);

  }

  100% {
    background-color: rgba(25, 122, 246, 0);
  }
}

.footImgDeft {
  width: 0.46rem;
  height: 0.46rem;
  margin-bottom: 0.14rem;

}

.footImgDeft img {
  width: 100%;
  height: 100%;
}

.homeImgOut {
  width: 0.9rem;
  height: 0.9rem;
  border-radius: 100%;
  background-color: rgb(25, 122, 246);
}

.homeImg {
  width: 0.9rem;
  height: 0.9rem;
}

.homeImg img {
  width: 100%;
  height: 100%;
}

.blueFont {
  color: #5d7dfb;
}
</style>
