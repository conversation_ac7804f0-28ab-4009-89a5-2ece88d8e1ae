<template>
  <div>
    <div class="hesadf">
      <div>
        <h2>
          <span class="hbnh"
            ><a class="fan" href="javascript:history.back(-1)"></a
          ></span>
          {{ $t("jy490") }}
        </h2>
      </div>
    </div>
    <div class="wrapper">
      <div class="flex">
        <div class="mobile">
          <div
            class="item"
            v-for="(item, index) of conversionList"
            :key="index"
          >
            <div class="title">{{ item.title }}</div>
            <div class="yue">
              {{ $t("hj47") }}：<span>{{ item.price }}</span>
            </div>
          </div>
        </div>
        <div class="right" @click="change">
          <div class="zhuanhuan"></div>
        </div>
      </div>
      <div class="form">
        <div class="label">{{ $t("jy493") }}</div>
        <div class="msg">
          {{ $t("jy494") }} {{ $t("jy495") }} <span>{{ conversion }}</span>
          {{ $t("jy500") }}
        </div>
      </div>
      <div class="form">
        <div class="label">{{ $t("jy497") }}</div>
        <input
          class="form-input"
          :placeholder="$t('jy498')"
          type="number"
          v-model="num"
        />
      </div>

      <div class="btn_zhuanhuan" @click="exchangeOp">{{ $t("jy499") }}</div>

      <div class="form">
        <div class="label">兌換說明：</div>
        <div class="msg">
          APP具體路徑：交易-貨幣兌換，選擇貨幣兌換的幣種、金額之後，即可進行貨幣兌換。
        </div>
        <div class="msg">
          匯率：由上遊銀行提供外匯市場實時匯率
        </div>
        <div class="msg">
          費用：無額外收手續費
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { Toast } from 'mint-ui'
import * as api from '@/axios/api'
import PDFJS from 'pdfjs-dist'

// const Base64 = require('js-base64').Base64

export default {
  data () {
    return {
      num: '',
      userInfo: {},
      conversion: '',
      conversionList: [
        { title: this.$t('jy496'), price: 0, type: 2 },
        { title: this.$t('jy500'), price: 0, type: 1 }
      ]
    }
  },
  created () {
    this.getUserInfo()
    this.getHkExchangeRate()
  },
  mounted: function () {},
  methods: {
    change () {
      this.conversionList.reverse()
    },
    // 兑换
    async exchangeOp () {
      if (!this.num) {
        return Toast(this.$t('jy498'))
      }
      let data = await api.exchangeOp({
        amt: this.num,
        type: this.conversionList[0].type
      })
      if (data.code === 200) {
        this.getUserInfo()
      }
      Toast(data.msg)
    },
    // 获取汇率
    async getHkExchangeRate () {
      let data = await api.getHkExchangeRate()
      if (data.code === 200) {
        this.conversion = data.data
      } else {
        Toast(data.msg)
      }
    },
    async getUserInfo () {
      // 获取用户信息
      let data = await api.getUserInfo()
      if (data.code === 200) {
        // 判断是否登录
        this.userInfo = data.data
        this.conversionList.forEach(f => {
          if (f.title == this.$t('jy500')) {
            f.price = data.data.hkAmt || 0
          }
          if (f.title == this.$t('jy496')) {
            f.price = data.data.enaleWithdrawAmt
          }
        })
      } else {
        // this.$store.commit('dialogVisible',true);
        // 跳转到login
        this.$router.push({ path: '/login' })
      }
    }
  }
}
</script>
<style lang="less" scoped>
body {
  background-color: #fff;
}
.hesadf {
  height: 1.1748rem;
  background: linear-gradient(-55deg, rgb(255, 48, 48), rgb(255, 69, 0));
}

h2 {
  text-align: center;
  height: 1.2549rem;
  width: 100%;
  position: relative;
  line-height: 1.2549rem;
  font-size: 0.4806rem;
  color: #fff;
  background: transparent;
  font-weight: 500;
  z-index: 3;
}

.hbnh {
  position: absolute;
  left: 0.4005rem;
  font-size: 0.4272rem;
  font-weight: 500;
}

.fan {
  width: 0.2403rem;
  height: 0.4272rem;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAAAXNSR0IArs4c6QAAAVdJREFUSEut1usqBVEYxvH/c1MuRJJDkhwTEpIkJUlyCEk++CJJckpycw49Gs3Wa+y9Z82ePd/Xr5n38KwRNR7bE0APMK1OHdtTwAWQGZcdQbZngPMc+QImK0O2Z4GzgIxLuqoE2Z4DTgMyJuk6K08yZHseOAnIqKSbRo2TINsLwHFARiTdxkaVQrYXgaOADEu6K3a7LWR7CTjMD2XdaYq0rZHtFWA/Rz6BIUn3reau6RvZXgX28kMfOfLQbnj/QbbXgN380HuOPJZtwB/I9jqwE5ABSc9lyJ8a2d4AtgPSL+klBfmFbG8CWwHpk/SaivxAtjMgg7Inq0mvpLcqSHehTOvKpzU+oSvFDlj99gesOJCDkp7Kip+yIknT3XL7C0tbum9lMbIMHKQkQEqwJWVSKZTPWTElq0dt6GYxt6uHf8DqX0cBq39BBqz+lR2w+j8RAfv9rfkGqF24CUdT9E4AAAAASUVORK5CYII=)
    no-repeat 50%;
  background-size: 100%;
  display: inline-block;
  margin-right: 0.1335rem;
  vertical-align: middle;
  margin-top: -0.0534rem;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0.267rem;
  padding: 0.267rem;
  border-radius: 0.267rem;
  background: #f1f1f1;
  .left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-right: 0.5rem;
    .top,
    .bottom {
      font-size: 0.35rem;
      font-weight: 500;
      color: #333;
      text-align: center;
    }
    .dian {
      background: url("../../assets/img/icon_dian.png") no-repeat center;
      background-size: 100%, 100%;
      width: 0.5rem;
      height: 0.5rem;
      margin: 0.25rem 0 0.25rem;
    }
  }
  .mobile {
    flex: 1;
    margin-right: 0.25rem;
    .item {
      padding: 0.15rem 0;
      .title {
        font-size: 0.35rem;
        color: #333;
        font-weight: 500;
        margin-bottom: 0.15rem;
      }
      .yue {
        font-size: 0.26rem;
        color: #666;
        span {
          color: #ea3544;
        }
      }
    }
  }
  .right {
    padding: 0.6rem 0.3rem;
    border-radius: 0.1rem;
    .zhuanhuan {
      background: url("../../assets/img/icon_zhuanhuan.png") no-repeat center;
      background-size: 100%, 100%;
      width: 0.8rem;
      height: 0.8rem;
    }
  }
}
.form {
  display: flex;
  flex-direction: column;
  margin: 0.267rem 0.267rem 0;
  padding: 0.267rem 0;
  .label {
    color: #333;
    font-weight: 500;
    text-align: left;
    padding: 0;
  }
  .msg {
    color: #333;
    margin-top: 0.1rem;
    line-height: 1.5;
    span {
      color: #ea3544;
      font-size: 0.36rem;
      font-weight: 600;
    }
  }
  .form-input {
    height: 1rem;
    background: #f7f7f7;
    margin-top: 0.15rem;
    border-radius: 0.2rem;
    padding-left: 0.25rem;
  }
}
.btn_zhuanhuan {
  height: 1.068rem;
  background: linear-gradient(-55deg, rgb(255, 48, 48), rgb(255, 69, 0));
  border-radius: 0.1335rem;
  color: #fff !important;
  font-size: 0.3738rem;
  text-align: center;
  line-height: 1.068rem;
  margin: 0.267rem;
}
</style>
