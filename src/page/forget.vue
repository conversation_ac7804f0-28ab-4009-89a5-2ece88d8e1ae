<template>
  <div class="futu-forget">
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left" @click="goBack">
        <span class="back-arrow">‹</span>
      </div>
      <div class="nav-center">
        <span class="nav-title">{{ currentStep === 1 ? '重置登录密码' : '输入邮箱验证码' }}</span>
      </div>
      <div class="nav-right">
        <span class="close-btn" @click="goBack">✕</span>
      </div>
    </div>

    <!-- 步骤1: 身份验证 -->
    <div class="forget-step" v-if="currentStep === 1">
      <h1 class="page-title">身份验证</h1>

      <div class="email-display">
        <input
          type="email"
          class="email-input"
          placeholder=""
          v-model="email"
          readonly
        >
      </div>

      <button
        class="send-code-button"
        @click="sendVerificationEmail"
        :disabled="!email || sending"
      >
        {{ sending ? '发送中...' : '发送验证邮件' }}
      </button>
    </div>

    <!-- 步骤2: 输入邮箱验证码 -->
    <div class="forget-step" v-if="currentStep === 2">
      <h1 class="page-title">输入邮箱验证码</h1>

      <p class="verification-info">
        验证码发送至：<span class="email-highlight">{{ email }}</span>
      </p>

      <div class="verification-code-container">
        <div class="code-inputs">
          <input
            v-for="(digit, index) in verificationCode"
            :key="index"
            type="text"
            class="code-input"
            maxlength="1"
            v-model="verificationCode[index]"
            @input="handleCodeInput(index, $event)"
            @keydown="handleKeyDown(index, $event)"
            :ref="'codeInput' + index"
          >
        </div>
      </div>

      <div class="resend-section">
        <span v-if="!canResend" class="resend-timer">{{ resendTimer }}秒后可重发</span>
        <span v-else class="resend-link" @click="resendCode">收不到邮箱验证码？</span>
      </div>
    </div>

    <!-- 步骤3: 设置新密码 -->
    <div class="forget-step" v-if="currentStep === 3">
      <h1 class="page-title">设置新密码</h1>

      <div class="password-container">
        <div class="password-input-group">
          <input
            :type="showNewPassword ? 'text' : 'password'"
            class="password-input"
            placeholder="输入新密码"
            v-model="newPassword"
          >
          <span class="password-toggle" @click="toggleNewPassword">
            {{ showNewPassword ? '🙈' : '👁️' }}
          </span>
        </div>

        <div class="password-input-group">
          <input
            :type="showConfirmPassword ? 'text' : 'password'"
            class="password-input"
            placeholder="确认新密码"
            v-model="confirmPassword"
          >
          <span class="password-toggle" @click="toggleConfirmPassword">
            {{ showConfirmPassword ? '🙈' : '👁️' }}
          </span>
        </div>
      </div>

      <button
        class="confirm-button"
        @click="resetPassword"
        :disabled="!canConfirm"
      >
        确认修改
      </button>
    </div>
  </div>
</template>

<script>
import { Toast } from 'mint-ui'
import { getResponseMsg } from '@/utils/utils'
import * as api from '@/axios/api'

export default {
  name: 'FutuForget',
  data () {
    return {
      currentStep: 1, // 1: 身份验证, 2: 输入验证码, 3: 设置新密码
      email: '',
      verificationCode: ['', '', '', '', '', ''],
      newPassword: '',
      confirmPassword: '',
      showNewPassword: false,
      showConfirmPassword: false,
      sending: false,
      canResend: false,
      resendTimer: 60,
      timer: null
    }
  },
  computed: {
    canConfirm () {
      return this.newPassword.length >= 6 &&
             this.confirmPassword.length >= 6 &&
             this.newPassword === this.confirmPassword
    },
    fullVerificationCode () {
      return this.verificationCode.join('')
    }
  },
  created () {
    // 从路由参数或登录页面获取邮箱
    this.email = this.$route.query.email || '<EMAIL>'
  },
  beforeDestroy () {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    goBack () {
      if (this.currentStep > 1) {
        this.currentStep--
      } else {
        this.$router.back()
      }
    },

    async sendVerificationEmail () {
      if (this.sending) return

      this.sending = true

      try {
        // 调用发送忘记密码验证码接口
        const response = await api.sendForgetSms({ phoneNum: this.email })

        if (response && response.code === 200) {
          Toast.success('验证码已发送到您的邮箱')
          this.currentStep = 2
          this.startResendTimer()
        } else {
          const errorMsg = getResponseMsg(response, '发送失败，请重试')
          Toast.fail(errorMsg)
        }
      } catch (error) {
        const errorMsg = getResponseMsg(error.response, '网络错误，请检查网络连接')
        Toast.fail(errorMsg)
      } finally {
        this.sending = false
      }
    },

    handleCodeInput (index, event) {
      const value = event.target.value

      // 只允许数字
      if (!/^\d*$/.test(value)) {
        this.verificationCode[index] = ''
        return
      }

      this.verificationCode.splice(index, 1, value)

      // 自动跳转到下一个输入框
      if (value && index < 5) {
        this.$nextTick(() => {
          const nextInput = this.$refs['codeInput' + (index + 1)]
          if (nextInput && nextInput[0]) {
            nextInput[0].focus()
          }
        })
      }

      // 验证码输入完成后自动验证
      if (this.fullVerificationCode.length === 6) {
        this.verifyCode()
      }
    },

    handleKeyDown (index, event) {
      // 处理退格键
      if (event.key === 'Backspace' && !this.verificationCode[index] && index > 0) {
        const prevInput = this.$refs['codeInput' + (index - 1)]
        if (prevInput && prevInput[0]) {
          prevInput[0].focus()
        }
      }
    },

    async verifyCode () {
      if (this.fullVerificationCode.length !== 6) {
        Toast.fail('请输入完整的验证码')
        return
      }

      try {
        // 这里可以添加验证码验证逻辑
        // 由于API限制，我们模拟验证成功
        Toast.success('验证码验证成功')
        this.currentStep = 3
      } catch (error) {
        Toast.fail('验证码错误，请重新输入')
        this.clearVerificationCode()
      }
    },

    clearVerificationCode () {
      this.verificationCode = ['', '', '', '', '', '']
      this.$nextTick(() => {
        const firstInput = this.$refs['codeInput0']
        if (firstInput && firstInput[0]) {
          firstInput[0].focus()
        }
      })
    },

    startResendTimer () {
      this.canResend = false
      this.resendTimer = 60

      this.timer = setInterval(() => {
        this.resendTimer--
        if (this.resendTimer <= 0) {
          this.canResend = true
          clearInterval(this.timer)
          this.timer = null
        }
      }, 1000)
    },

    async resendCode () {
      if (!this.canResend) return
      await this.sendVerificationEmail()
    },

    toggleNewPassword () {
      this.showNewPassword = !this.showNewPassword
    },

    toggleConfirmPassword () {
      this.showConfirmPassword = !this.showConfirmPassword
    },

    async resetPassword () {
      if (!this.canConfirm) return

      try {
        const response = await api.forgetPas({
          phone: this.email,
          password: this.newPassword,
          code: this.fullVerificationCode
        })

        if (response && response.code === 200) {
          Toast.success('密码重置成功')
          setTimeout(() => {
            this.$router.replace('/futu-login')
          }, 1500)
        } else {
          const errorMsg = getResponseMsg(response, '密码重置失败，请重试')
          Toast.fail(errorMsg)
        }
      } catch (error) {
        const errorMsg = getResponseMsg(error.response, '网络错误，请检查网络连接')
        Toast.fail(errorMsg)
      }
    }
  }
}
</script>

<style scoped>
.futu-forget {
  min-height: 100vh;
  background: #fff;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  height: 44px;
  border-bottom: 1px solid #f0f0f0;
}

.nav-left, .nav-right {
  width: 60px;
}

.nav-center {
  flex: 1;
  text-align: center;
}

.nav-title {
  font-size: 17px;
  font-weight: 600;
  color: #000;
}

.back-arrow {
  font-size: 24px;
  color: #007AFF;
  font-weight: 300;
  cursor: pointer;
}

.close-btn {
  font-size: 18px;
  color: #007AFF;
  cursor: pointer;
  text-align: right;
  display: block;
}

/* 步骤内容 */
.forget-step {
  padding: 40px 20px;
}

.page-title {
  font-size: 34px;
  font-weight: bold;
  margin-bottom: 40px;
  color: #000;
}

/* 邮箱显示 */
.email-display {
  margin-bottom: 40px;
}

.email-input {
  width: 100%;
  padding: 16px 0;
  border: none;
  border-bottom: 1px solid #E5E5E7;
  font-size: 16px;
  background: transparent;
  outline: none;
  color: #000;
}

/* 发送验证码按钮 */
.send-code-button {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  background: #000;
  cursor: pointer;
  transition: all 0.2s;
}

.send-code-button:hover:not(:disabled) {
  background: #333;
}

.send-code-button:disabled {
  background: #C7C7CC;
  cursor: not-allowed;
}

/* 验证信息 */
.verification-info {
  font-size: 16px;
  color: #666;
  margin-bottom: 40px;
  line-height: 1.5;
}

.email-highlight {
  color: #FF9500;
  font-weight: 500;
}

/* 验证码输入 */
.verification-code-container {
  margin-bottom: 30px;
}

.code-inputs {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 20px;
}

.code-input {
  width: 45px;
  height: 55px;
  border: 1px solid #E5E5E7;
  border-radius: 8px;
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  outline: none;
  transition: border-color 0.2s;
}

.code-input:focus {
  border-color: #007AFF;
}

/* 重发区域 */
.resend-section {
  text-align: center;
  margin-bottom: 40px;
}

.resend-timer {
  font-size: 14px;
  color: #999;
}

.resend-link {
  font-size: 14px;
  color: #007AFF;
  cursor: pointer;
  text-decoration: underline;
}

/* 密码输入 */
.password-container {
  margin-bottom: 40px;
}

.password-input-group {
  position: relative;
  margin-bottom: 20px;
}

.password-input {
  width: 100%;
  padding: 16px 40px 16px 0;
  border: none;
  border-bottom: 1px solid #E5E5E7;
  font-size: 16px;
  background: transparent;
  outline: none;
}

.password-input:focus {
  border-bottom-color: #007AFF;
}

.password-toggle {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  font-size: 18px;
}

/* 确认按钮 */
.confirm-button {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  background: #000;
  cursor: pointer;
  transition: all 0.2s;
}

.confirm-button:hover:not(:disabled) {
  background: #333;
}

.confirm-button:disabled {
  background: #C7C7CC;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .forget-step {
    padding: 30px 16px;
  }

  .page-title {
    font-size: 28px;
    margin-bottom: 30px;
  }

  .code-input {
    width: 40px;
    height: 50px;
    font-size: 20px;
  }

  .code-inputs {
    gap: 8px;
  }
}
</style>
