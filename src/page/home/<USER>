<template>
  <div class="wrapper">
    <div class="header">
      <mt-header :title="$t('jy220')">
        <router-link to="/user" slot="left">
          <mt-button icon="back">{{ $t("jy74") }}</mt-button>
        </router-link>
        <!-- <mt-button icon="more" slot="right"></mt-button> -->
      </mt-header>
    </div>
    <div style="margin:12px 20px;">
      <h1>{{ $t("jy221") }}</h1>
      <p></p>
      <p>{{ $t("jy222") }}</p>
      <p>{{ $t("jy223") }}</p>
      <p>{{ $t("jy224") }}</p>
      <p>
        1，{{ $t("jy225") }}
      </p>
    </div>
    <div class="agree-footer text-center">
      <div class="agree">
        <p @click="isAgree">
          <i
            :class="
              agree
                ? 'glyphicon glyphicon glyphicon-ok-sign red'
                : 'glyphicon glyphicon-ok-circle'
            "
          ></i>
          {{ $t("jy226") }}
          <a href="#">《{{ $t("jy227") }}》</a>
        </p>
      </div>
      <div class="btn-box">
        <mt-button
          :class="agree ? 'btn btn-red' : 'btn btn-default'"
          size="small"
          @click="toBuy()"
          >{{ $t("hj161") }}</mt-button
        >
        <mt-button class="btn btn-cancel" size="small" @click="toBuy"
          >{{ $t("hj106") }}</mt-button
        >
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      agree: false
    }
  },
  methods: {
    isAgree () {
      let i = false
      let j = true
      this.agree = this.agree ? i : j
    },
    toBuy () {
      this.$router.push('/buy')
    }
  }
}
</script>
<style lang="less" scoped>
h1 {
  font-size: 0.25rem;
  text-align: center;
}

h4 {
  font-size: 13px;
  line-height: 25px;
  margin: 25px 0;
}

p {
  font-size: 12px;
  line-height: 25px;
  text-indent: 2em;
}

.agree-footer {
  .agree {
    padding: 0.2rem 0;
  }

  a {
    color: #177be1;
  }
}
</style>
