<template>
  <div class="kline-chart-wrapper">
    <!-- 股票信息头部 -->
    <div class="stock-header">
      <div class="stock-info">
        <div class="stock-name">{{ stockInfo.symbol }} {{ stockInfo.name }}</div>
        <div class="stock-time">{{ formatTime(stockInfo.timestamp) }}</div>
      </div>
      <div class="stock-price">
        <div class="price" :class="priceClass">{{ stockInfo.price }}</div>
        <div class="change-info">
          <span class="change-amount" :class="priceClass">
            {{ stockInfo.change > 0 ? '+' : '' }}{{ parseFloat(stockInfo.change).toFixed(2) }}
          </span>
          <span class="change-percent" :class="priceClass">
            {{ stockInfo.changePercent > 0 ? '+' : '' }}{{ stockInfo.changePercent }}%
          </span>
        </div>
      </div>
    </div>

    <!-- 时间周期切换已移到上方富途风格的控件中 -->

    <!-- 技术指标选择 -->
    <div class="indicator-selector">
      <div class="indicator-tabs">
        <span
          v-for="indicator in indicatorOptions"
          :key="indicator.id"
          :class="['indicator-tab', { active: activeIndicators.includes(indicator.id) }]"
          @click="toggleIndicator(indicator.id)"
        >
          {{ indicator.name }}
        </span>
      </div>
    </div>

    <!-- K线图容器 -->
    <div class="chart-container">
      <div ref="klineChart" class="kline-chart"></div>
      <div class="loading-overlay" v-if="loading">
        <van-loading type="spinner" />
        <span>加载中...</span>
      </div>
    </div>

    <!-- 实时数据状态 -->
    <div class="realtime-status">
      <div class="status-item">
        <van-icon :name="wsConnected ? 'success' : 'fail'" />
        <span>{{ wsConnected ? '实时数据' : '数据延迟' }}</span>
      </div>
      <div class="last-update">
        最后更新: {{ formatTime(lastUpdateTime) }}
      </div>
    </div>

    <!-- 日期选择弹窗 -->
    <van-popup v-model="showDatePicker" position="bottom" round>
      <div class="date-picker-content">
        <div class="date-picker-header">
          <span @click="showDatePicker = false">取消</span>
          <span class="title">选择时间范围</span>
          <span @click="confirmDateRange" class="confirm">确定</span>
        </div>
        <div class="quick-selects">
          <span
            v-for="quick in quickDateOptions"
            :key="quick.id"
            :class="['quick-select', { active: selectedQuickDate === quick.id }]"
            @click="selectQuickDate(quick)"
          >
            {{ quick.name }}
          </span>
        </div>
        <div class="custom-date-range">
          <van-datetime-picker
            v-model="startDate"
            type="date"
            title="开始日期"
            :max-date="endDate"
          />
          <van-datetime-picker
            v-model="endDate"
            type="date"
            title="结束日期"
            :min-date="startDate"
            :max-date="new Date()"
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { init, dispose } from 'klinecharts'
import dayjs from 'dayjs'
import {
  subscribePolygonKLine,
  getPolygonKLineData,
  disconnectPolygonKLine,
  connectPolygonKLine
} from '@/services/polygonKLineService'
import { Toast } from 'mint-ui'

export default {
  name: 'KLineChartComponent',
  props: {
    symbol: {
      type: String,
      required: true,
      default: 'AAPL'
    },
    name: {
      type: String,
      default: ''
    },
    // 外部控制的周期
    activePeriod: {
      type: String,
      default: '5min'
    }
  },
  data() {
    return {
      chart: null,
      loading: false,
      wsConnected: false,
      lastUpdateTime: null,
      showDatePicker: false,
      selectedQuickDate: '1Y',

      // 股票信息
      stockInfo: {
        symbol: '',
        name: '',
        price: 0,
        change: 0,
        changePercent: 0,
        timestamp: Date.now()
      },

      // 当前周期
      currentPeriod: this.activePeriod,

      // 周期选项
      periodOptions: [
        { id: '1min', name: '1分', multiplier: 1, timespan: 'minute' },
        { id: '5min', name: '5分', multiplier: 5, timespan: 'minute' },
        { id: '15min', name: '15分', multiplier: 15, timespan: 'minute' },
        { id: '30min', name: '30分', multiplier: 30, timespan: 'minute' },
        { id: '1hour', name: '1时', multiplier: 1, timespan: 'hour' },
        { id: 'day', name: '日K', multiplier: 1, timespan: 'day' },
        { id: 'week', name: '周K', multiplier: 1, timespan: 'week' },
        { id: 'month', name: '月K', multiplier: 1, timespan: 'month' }
      ],

      // 技术指标选项
      indicatorOptions: [
        { id: 'MA', name: 'MA' },
        { id: 'BOLL', name: 'BOLL' },
        { id: 'MACD', name: 'MACD' },
        { id: 'KDJ', name: 'KDJ' },
        { id: 'RSI', name: 'RSI' },
        { id: 'VOL', name: '成交量' }
      ],

      // 当前激活的技术指标
      activeIndicators: ['MA', 'VOL'],

      // 日期范围 - 根据周期动态计算
      startDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 默认5天前
      endDate: new Date(),

      // 快速日期选项
      quickDateOptions: [
        { id: '1D', name: '1天', days: 1 },
        { id: '5D', name: '5天', days: 5 },
        { id: '1M', name: '1月', days: 30 },
        { id: '3M', name: '3月', days: 90 },
        { id: '6M', name: '6月', days: 180 },
        { id: '1Y', name: '1年', days: 365 },
        { id: '2Y', name: '2年', days: 730 },
        { id: 'ALL', name: '全部', days: 3650 }
      ],

      // WebSocket订阅
      realtimeSubscription: null,

      // K线实时聚合状态
      currentKlineBar: null, // 当前活跃的K线柱
      klineBarStartTime: null, // 当前K线柱的开始时间
      periodConfig: null, // 当前周期配置
      tickBuffer: [], // tick数据缓冲区
      lastChartUpdateTime: null, // 最后一次图表更新时间
      forceUpdateTimer: null // 强制更新定时器
    }
  },
  computed: {
    priceClass() {
      if (this.stockInfo.change > 0) return 'up'
      if (this.stockInfo.change < 0) return 'down'
      return ''
    }
  },
  watch: {
    symbol: {
      immediate: true,
      handler(newSymbol) {
        if (newSymbol) {
          this.stockInfo.symbol = newSymbol
          this.stockInfo.name = this.name || newSymbol
          this.initChart()
          this.loadKLineData()
          this.subscribeRealtimeData()
        }
      }
    },

    // 监听外部传入的周期变化
    activePeriod(newPeriod) {
      if (newPeriod && newPeriod !== this.currentPeriod) {
        this.currentPeriod = newPeriod

        // 重置K线聚合状态
        this.resetKlineAggregationState()

        // 找到对应的周期配置并更新时间范围
        const periodConfig = this.periodOptions.find(p => p.id === newPeriod)
        if (periodConfig) {
          this.updateDateRangeForPeriod(periodConfig)
        }

        this.loadKLineData()
      }
    }
  },
  mounted() {
    this.initChart()
    // 初始化时设置正确的时间范围
    const initialPeriod = this.periodOptions.find(p => p.id === this.currentPeriod)
    if (initialPeriod) {
      this.updateDateRangeForPeriod(initialPeriod)
    }
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    // 初始化图表
    initChart() {
      if (this.chart) {
        dispose(this.chart)
      }

      const chartDom = this.$refs.klineChart
      if (!chartDom) return

      try {
        this.chart = init(chartDom)

        if (this.chart) {
          // 配置图表样式
          this.configureChart()
        }
      } catch (error) {
        console.error('❌ [KLineChart] 图表初始化失败:', error)
        Toast('图表初始化失败')
      }
    },

    // 配置图表样式和选项
    configureChart() {
      if (!this.chart) return

      // 设置图表样式
      this.chart.setStyles({
        grid: {
          show: true,
          horizontal: {
            show: true,
            color: '#E8E8E8'
          },
          vertical: {
            show: true,
            color: '#E8E8E8'
          }
        },
        candle: {
          type: 'candle_solid',
          bar: {
            upColor: '#26A69A',
            downColor: '#EF5350',
            noChangeColor: '#888888'
          },
          tooltip: {
            showRule: 'always',
            showType: 'standard'
          }
        },
        technicalIndicator: {
          margin: {
            top: 0.2,
            bottom: 0.1
          },
          bar: {
            upColor: '#26A69A',
            downColor: '#EF5350',
            noChangeColor: '#888888'
          },
          line: {
            colors: ['#FF9800', '#9C27B0', '#2196F3', '#E91E63', '#4CAF50']
          }
        },
        xAxis: {
          show: true,
          tickText: {
            show: true,
            color: '#666666',
            size: 12
          },
          // 确保时间轴正确显示
          type: 'time'
        }
      })

      // 添加默认的技术指标
      this.updateIndicators()
    },

        // 加载K线数据
    async loadKLineData() {
      if (!this.chart || !this.stockInfo.symbol) return

      this.loading = true

      try {
        const period = this.periodOptions.find(p => p.id === this.currentPeriod)
        if (!period) return

        const options = {
          multiplier: period.multiplier,
          timespan: period.timespan,
          from: dayjs(this.startDate).format('YYYY-MM-DD'),
          to: dayjs(this.endDate).format('YYYY-MM-DD'),
          limit: 5000
        }

        // 使用新的Polygon K线数据服务
        const klineData = await getPolygonKLineData(this.stockInfo.symbol, options)

        if (klineData && klineData.length > 0) {
          // 验证和处理时间戳数据
          const processedData = this.processKlineData(klineData, period)

          // 应用数据到图表
          this.chart.applyNewData(processedData)

          // 更新股票价格信息
          this.updateStockInfoFromKlineData(processedData)

        } else {
          console.warn('⚠️ [KLineChart] 未获取到K线数据')
        }
      } catch (error) {
        console.error('❌ [KLineChart] 加载K线数据失败:', error)
        Toast('加载K线数据失败')
      } finally {
        this.loading = false
      }
    },



    // 从K线数据更新股票信息
    updateStockInfoFromKlineData(klineData) {
      if (!klineData || klineData.length === 0) return

      const latest = klineData[klineData.length - 1]
      const previous = klineData.length > 1 ? klineData[klineData.length - 2] : latest

      this.stockInfo.price = parseFloat(latest.close)
      this.stockInfo.change = parseFloat(latest.close) - parseFloat(previous.close)
      this.stockInfo.changePercent = ((this.stockInfo.change / parseFloat(previous.close)) * 100).toFixed(2)
      this.stockInfo.timestamp = latest.timestamp

      // 触发价格更新事件
      this.$emit('priceUpdate', {
        price: this.stockInfo.price,
        change: this.stockInfo.change,
        changePercent: this.stockInfo.changePercent,
        timestamp: this.stockInfo.timestamp
      })
    },

        // 订阅实时数据
    async subscribeRealtimeData() {
      if (!this.stockInfo.symbol) return

      try {
        // 取消之前的订阅
        if (this.realtimeSubscription) {
          this.realtimeSubscription()
        }

        // 重置K线聚合状态
        this.resetKlineAggregationState()

        // 使用新的Polygon K线WebSocket服务
        this.realtimeSubscription = await subscribePolygonKLine(this.stockInfo.symbol, (data) => {
          this.handleRealtimeData(data)
        }, {
          period: this.currentPeriod
        })

        this.wsConnected = true
      } catch (error) {
        console.error('❌ [KLineChart] 实时数据订阅失败:', error)
        this.wsConnected = false
      }
    },

    // 处理K线数据，确保时间戳格式正确
    processKlineData(klineData, period) {
      return klineData.map(item => {
        // 确保时间戳是数字类型且具有正确的精度
        let timestamp = item.timestamp

        // 如果时间戳是字符串，尝试解析
        if (typeof timestamp === 'string') {
          timestamp = new Date(timestamp).getTime()
        }

        // 确保时间戳是有效的
        if (!timestamp || isNaN(timestamp)) {
          console.warn('⚠️ [KLineChart] 无效的时间戳:', item.timestamp)
          timestamp = Date.now()
        }

        // 根据周期类型，只对历史数据进行必要的对齐
        // 但保留足够的时间精度以便图表正确显示
        const processedTimestamp = this.alignTimestamp(timestamp, period)

        return {
          ...item,
          timestamp: processedTimestamp
        }
      })
    },

    // 对齐时间戳（轻量级处理，保留更多时间信息）
    alignTimestamp(timestamp, period) {
      const date = new Date(timestamp)

      // 只对特定周期进行轻微对齐，保留大部分时间信息
      switch (period.timespan) {
        case 'minute':
          // 分钟数据：保留到分钟精度
          if (period.multiplier >= 30) {
            // 30分钟及以上，对齐到整半小时或整小时
            const minutes = Math.floor(date.getMinutes() / period.multiplier) * period.multiplier
            date.setMinutes(minutes, 0, 0)
          } else {
            // 小于30分钟，只清零秒
            date.setSeconds(0, 0)
          }
          break
        case 'hour':
          // 小时数据：对齐到整小时
          date.setMinutes(0, 0, 0)
          break
        case 'day':
          // 日数据：设置为市场开盘时间，但保留日期信息
          date.setHours(9, 30, 0, 0)
          break
        default:
          // 其他情况保留原始时间戳
          break
      }

      return date.getTime()
    },

    // 处理实时数据 - 智能K线聚合
    handleRealtimeData(data) {
      try {
        if (!data) return
        // 兼容不同的数据格式
        let tickData = null

        // 检查数据格式1：直接的tick数据 {price, timestamp, size}
        if (data.price !== undefined) {
          tickData = {
            timestamp: data.timestamp || Date.now(),
            price: parseFloat(data.price),
            volume: parseInt(data.size || data.volume || 0)
          }
        }
        // 检查数据格式2：K线柱格式 {open, high, low, close, volume, timestamp}
        else if (data.close !== undefined) {
          tickData = {
            timestamp: data.timestamp || Date.now(),
            price: parseFloat(data.close), // 使用收盘价作为当前价格
            volume: parseInt(data.volume || 0)
          }
        }
        // 检查数据格式3：包装格式 {data: {...}}
        else if (data.data && (data.data.price !== undefined || data.data.close !== undefined)) {
          const innerData = data.data
          tickData = {
            timestamp: innerData.timestamp || data.timestamp || Date.now(),
            price: parseFloat(innerData.price || innerData.close),
            volume: parseInt(innerData.size || innerData.volume || 0)
          }
        }

        if (!tickData || !tickData.price) {
          console.warn('⚠️ [KLineChart] 无法解析实时数据格式:', data)
          return
        }

        // 先更新股票信息显示（确保价格信息能正常更新）
        this.updateStockInfoFromTick(tickData)

        // 将tick数据聚合到K线柱中
        const aggregatedKlineBar = this.aggregateTickToKline(tickData)

        // 如果有聚合后的K线柱数据，更新图表
        if (aggregatedKlineBar) {
          this.updateChartWithKlineBar(aggregatedKlineBar)
        } else {
          console.warn('⚠️ [KLineChart] K线聚合失败，但价格信息已更新')
        }

      } catch (error) {
        console.error('❌ [KLineChart] 处理实时数据失败:', error, data)
      }
    },

    // 将tick数据聚合到K线柱
    aggregateTickToKline(tickData) {
      try {

        // 获取当前周期配置
        if (!this.periodConfig) {
          this.periodConfig = this.periodOptions.find(p => p.id === this.currentPeriod)
          if (!this.periodConfig) {
            console.warn('⚠️ [KLineChart] 无法找到周期配置:', this.currentPeriod)
            return null
          }
        }

        // 计算当前tick应该属于的K线柱时间窗口
        const klineBarTime = this.calculateKlineBarTime(tickData.timestamp, this.periodConfig)


        // 检查是否需要创建新的K线柱
        if (!this.currentKlineBar || this.klineBarStartTime !== klineBarTime) {
          // 如果有之前的K线柱，先处理完成
          if (this.currentKlineBar) {
            console.log('📊 [KLineChart] 完成K线柱:', {
              time: new Date(this.klineBarStartTime).toLocaleString(),
              bar: this.currentKlineBar
            })
          }

          // 创建新的K线柱
          this.createNewKlineBar(tickData, klineBarTime)
          return this.currentKlineBar
        } else {
          // 更新现有K线柱
          this.updateCurrentKlineBar(tickData)
          return this.currentKlineBar
        }
      } catch (error) {
        console.error('❌ [KLineChart] K线聚合过程出错:', error)
        return null
      }
    },

    // 计算K线柱应该属于的时间窗口
    calculateKlineBarTime(timestamp, periodConfig) {
      const date = new Date(timestamp)

      switch (periodConfig.timespan) {
        case 'minute':
          // 分钟级：对齐到指定分钟间隔
          const totalMinutes = date.getHours() * 60 + date.getMinutes()
          const alignedMinutes = Math.floor(totalMinutes / periodConfig.multiplier) * periodConfig.multiplier
          const alignedHours = Math.floor(alignedMinutes / 60)
          const remainingMinutes = alignedMinutes % 60
          date.setHours(alignedHours, remainingMinutes, 0, 0)
          break

        case 'hour':
          // 小时级：对齐到指定小时间隔
          const hourAligned = Math.floor(date.getHours() / periodConfig.multiplier) * periodConfig.multiplier
          date.setHours(hourAligned, 0, 0, 0)
          break

        case 'day':
          // 日级：对齐到当天开始
          date.setHours(0, 0, 0, 0)
          break

        case 'week':
          // 周级：对齐到本周开始（周一）
          const dayOfWeek = date.getDay()
          const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1
          date.setDate(date.getDate() - daysToMonday)
          date.setHours(0, 0, 0, 0)
          break

        case 'month':
          // 月级：对齐到本月开始
          date.setDate(1)
          date.setHours(0, 0, 0, 0)
          break

        default:
          console.warn('⚠️ [KLineChart] 未知的时间周期:', periodConfig.timespan)
          return timestamp
      }

      return date.getTime()
    },

    // 创建新的K线柱
    createNewKlineBar(tickData, klineBarTime) {
      this.klineBarStartTime = klineBarTime
      this.currentKlineBar = {
        timestamp: klineBarTime,
        open: tickData.price,
        high: tickData.price,
        low: tickData.price,
        close: tickData.price,
        volume: tickData.volume
      }

      console.log('🆕 [KLineChart] 创建新K线柱:', {
        time: new Date(klineBarTime).toLocaleString(),
        price: tickData.price,
        period: this.periodConfig.name
      })
    },

        // 更新现有K线柱
    updateCurrentKlineBar(tickData) {
      if (!this.currentKlineBar) return

      const oldValues = {
        high: this.currentKlineBar.high,
        low: this.currentKlineBar.low,
        close: this.currentKlineBar.close,
        volume: this.currentKlineBar.volume
      }

      // 更新高低价
      this.currentKlineBar.high = Math.max(this.currentKlineBar.high, tickData.price)
      this.currentKlineBar.low = Math.min(this.currentKlineBar.low, tickData.price)

      // 更新收盘价（当前价格）
      this.currentKlineBar.close = tickData.price

      // 累计成交量
      this.currentKlineBar.volume += tickData.volume
    },

        // 更新图表with聚合的K线柱
    updateChartWithKlineBar(klineBar) {
      if (!this.chart || !klineBar) return

      try {

                 // 根据时间级别采用不同的更新策略
         const needsSpecialHandling = this.periodConfig && (
           ['day', 'week', 'month'].includes(this.periodConfig.timespan) ||
           (this.periodConfig.timespan === 'minute' && this.periodConfig.multiplier >= 15) ||
           (this.periodConfig.timespan === 'hour')
         )

         if (needsSpecialHandling) {


           // 获取当前图表数据
           const currentData = this.chart.getDataList()

           if (currentData && currentData.length > 0) {
             // 更新最后一根K线柱
             const lastIndex = currentData.length - 1
             const lastBar = currentData[lastIndex]

             // 检查时间戳是否匹配，如果匹配则更新，否则添加新柱
             if (lastBar && lastBar.timestamp === klineBar.timestamp) {
               // 更新现有K线柱 - 使用两种方法确保更新成功
               try {
                 // 方法1：直接更新
                 this.chart.updateData(klineBar)
               } catch (error) {
                 // 方法2：替换整个数据集
                 const updatedData = [...currentData]
                 updatedData[lastIndex] = { ...klineBar }
                 this.chart.applyNewData(updatedData)
               }
             } else {
               this.chart.applyNewData([...currentData, klineBar])
             }
           } else {
             this.chart.applyNewData([klineBar])
           }
         } else {
           this.chart.updateData(klineBar)
         }

         // 记录最后更新时间
         this.lastChartUpdateTime = Date.now()


      } catch (error) {
        console.error('❌ [KLineChart] 图表更新失败:', error)

        // 备用方案：强制重新应用数据
        try {
          const currentData = this.chart.getDataList()
          if (currentData && currentData.length > 0) {
            const updatedData = [...currentData]
            const lastIndex = updatedData.length - 1

            // 如果最后一根K线的时间戳匹配，则更新它
            if (updatedData[lastIndex] && updatedData[lastIndex].timestamp === klineBar.timestamp) {
              updatedData[lastIndex] = { ...klineBar }
            } else {
              updatedData.push(klineBar)
            }

            this.chart.applyNewData(updatedData)
          }
        } catch (backupError) {
          console.error('❌ [KLineChart] 备用方案也失败:', backupError)
        }
      }
    },

    // 从tick数据更新股票信息显示
    updateStockInfoFromTick(tickData) {

      this.stockInfo.price = tickData.price
      this.stockInfo.timestamp = tickData.timestamp
      this.lastUpdateTime = tickData.timestamp

      // 计算涨跌幅（需要获取前一个价格来计算）
      this.calculatePriceChange(tickData.price)

      // 触发价格更新事件
      this.$emit('priceUpdate', {
        price: this.stockInfo.price,
        change: this.stockInfo.change,
        changePercent: this.stockInfo.changePercent,
        timestamp: this.stockInfo.timestamp
      })
    },

    // 重置K线聚合状态
    resetKlineAggregationState() {
      this.currentKlineBar = null
      this.klineBarStartTime = null
      this.periodConfig = null
      this.tickBuffer = []
      this.lastChartUpdateTime = null

      // 清除强制更新定时器
      if (this.forceUpdateTimer) {
        clearInterval(this.forceUpdateTimer)
        this.forceUpdateTimer = null
      }

      // 为大时间级别设置强制更新定时器
      this.setupForceUpdateTimer()
    },

    // 设置强制更新定时器（针对大时间级别）
    setupForceUpdateTimer() {
      // 清除现有定时器
      if (this.forceUpdateTimer) {
        clearInterval(this.forceUpdateTimer)
      }

      const currentPeriodConfig = this.periodOptions.find(p => p.id === this.currentPeriod)

            // 为需要特殊处理的时间级别设置强制更新
      const needsForceUpdate = currentPeriodConfig && (
        ['day', 'week', 'month'].includes(currentPeriodConfig.timespan) ||
        (currentPeriodConfig.timespan === 'minute' && currentPeriodConfig.multiplier >= 15) ||
        (currentPeriodConfig.timespan === 'hour')
      )

      if (needsForceUpdate) {

        this.forceUpdateTimer = setInterval(() => {
          if (this.currentKlineBar && this.chart) {

                         try {
               // 对于15分钟及以上的分钟级别和小时级别，使用更强制的更新方法
               if ((currentPeriodConfig.timespan === 'minute' && currentPeriodConfig.multiplier >= 15) ||
                   currentPeriodConfig.timespan === 'hour') {
                 const currentData = this.chart.getDataList()
                 if (currentData && currentData.length > 0) {
                   const updatedData = [...currentData]
                   const lastIndex = updatedData.length - 1
                   if (updatedData[lastIndex] && updatedData[lastIndex].timestamp === this.currentKlineBar.timestamp) {
                     updatedData[lastIndex] = { ...this.currentKlineBar }
                     this.chart.applyNewData(updatedData)
                   }
                 }
               } else {
                 this.chart.updateData(this.currentKlineBar)
               }
            } catch (error) {
              console.error('❌ [KLineChart] 强制更新失败:', error)
            }
          }
        }, 3000) // 每3秒强制更新一次，对于15分钟级别更频繁
      }
    },

    // 计算价格变化
    calculatePriceChange(currentPrice) {
      try {
        // 获取图表的历史数据来计算涨跌幅
        if (this.chart) {
          const chartData = this.chart.getDataList()
          if (chartData && chartData.length > 1) {
            const previousClose = chartData[chartData.length - 2].close
            this.stockInfo.change = parseFloat((currentPrice - previousClose).toFixed(2))
            this.stockInfo.changePercent = parseFloat(((this.stockInfo.change / previousClose) * 100).toFixed(2))
          }
        }
      } catch (error) {
        console.error('❌ [KLineChart] 计算价格变化失败:', error)
        // 设置默认值
        this.stockInfo.change = 0
        this.stockInfo.changePercent = 0
      }
    },



    // 切换周期
    changePeriod(period) {
      if (this.currentPeriod === period.id) return

      this.currentPeriod = period.id
      // 根据周期更新时间范围
      this.updateDateRangeForPeriod(period)

      // 重新加载数据
      this.loadKLineData()
    },

    // 根据周期更新时间范围
    updateDateRangeForPeriod(period) {
      const now = new Date()
      this.endDate = new Date(now)

      switch (period.timespan) {
        case 'minute':
          if (period.multiplier <= 5) {
            // 1分钟和5分钟：获取最近3天
            this.startDate = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)
          } else if (period.multiplier <= 15) {
            // 15分钟：获取最近5天
            this.startDate = new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000)
          } else {
            // 30分钟：获取最近7天
            this.startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          }
          break
        case 'hour':
          // 1小时：获取最近30天
          this.startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case 'day':
          // 日线：获取最近1年
          this.startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        case 'week':
          // 周线：获取最近2年
          this.startDate = new Date(now.getTime() - 2 * 365 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          // 月线：获取最近5年
          this.startDate = new Date(now.getTime() - 5 * 365 * 24 * 60 * 60 * 1000)
          break
        default:
          // 默认：获取最近5天
          this.startDate = new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000)
      }
    },

    // 切换技术指标
    toggleIndicator(indicatorId) {
      const index = this.activeIndicators.indexOf(indicatorId)

      if (index > -1) {
        // 移除指标
        this.activeIndicators.splice(index, 1)
      } else {
        // 添加指标
        this.activeIndicators.push(indicatorId)
      }

      this.updateIndicators()
    },

    // 更新技术指标
    updateIndicators() {
      if (!this.chart) return

      // 清除所有现有指标
      this.chart.removeIndicator()

      // 添加选中的指标
      this.activeIndicators.forEach(indicatorId => {
        try {
          this.chart.createIndicator({
            name: indicatorId,
            calcParams: this.getIndicatorParams(indicatorId)
          })
        } catch (error) {
          console.error(`❌ [KLineChart] 添加指标${indicatorId}失败:`, error)
        }
      })
    },

    // 获取指标参数
    getIndicatorParams(indicatorId) {
      const params = {
        MA: [5, 10, 20, 60],
        BOLL: [20, 2],
        MACD: [12, 26, 9],
        KDJ: [9, 3, 3],
        RSI: [14],
        VOL: [5, 10, 20]
      }
      return params[indicatorId] || []
    },

    // 选择快速日期
    selectQuickDate(quick) {
      this.selectedQuickDate = quick.id

      if (quick.id === 'ALL') {
        this.startDate = new Date(Date.now() - 10 * 365 * 24 * 60 * 60 * 1000) // 10年前
      } else {
        this.startDate = new Date(Date.now() - quick.days * 24 * 60 * 60 * 1000)
      }

      this.endDate = new Date()
    },

    // 确认日期范围
    confirmDateRange() {
      this.showDatePicker = false
      this.loadKLineData()
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return ''
      return dayjs(timestamp).format('MM-DD HH:mm:ss')
    },

    // 格式化日期范围
    formatDateRange() {
      const start = dayjs(this.startDate).format('MM-DD')
      const end = dayjs(this.endDate).format('MM-DD')
      return `${start} ~ ${end}`
    },

    // 清理资源
    cleanup() {

      // 清理图表
      if (this.chart) {
        dispose(this.chart)
        this.chart = null
      }

      // 取消实时数据订阅
      if (this.realtimeSubscription) {
        this.realtimeSubscription()
        this.realtimeSubscription = null
      }

      // 重置K线聚合状态
      this.resetKlineAggregationState()

      // 清除强制更新定时器
      if (this.forceUpdateTimer) {
        clearInterval(this.forceUpdateTimer)
        this.forceUpdateTimer = null
      }

      // 断开Polygon WebSocket连接（如果是最后一个使用者）
      disconnectPolygonKLine()

      this.wsConnected = false
    }
  }
}
</script>

<style scoped>
.kline-chart-wrapper {
  background: #fff;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 股票信息头部 */
.stock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.stock-info {
  flex: 1;
}

.stock-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stock-time {
  font-size: 12px;
  color: #999;
}

.stock-price {
  text-align: right;
}

.price {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.price.up {
  color: #f56c6c;
}

.price.down {
  color: #67c23a;
}

.change-info {
  display: flex;
  gap: 8px;
  font-size: 14px;
}

.change-amount, .change-percent {
  font-weight: 500;
}

.change-amount.up, .change-percent.up {
  color: #f56c6c;
}

.change-amount.down, .change-percent.down {
  color: #67c23a;
}

/* 周期选择器样式已移到父页面 */

/* 指标选择器 */
.indicator-selector {
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.indicator-tabs {
  display: flex;
  gap: 12px;
  overflow-x: auto;
}

.indicator-tab {
  padding: 4px 8px;
  font-size: 12px;
  color: #666;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.3s;
}

.indicator-tab.active {
  background: #409eff;
  color: #fff;
  border-color: #409eff;
}

/* 图表容器 */
.chart-container {
  flex: 1;
  position: relative;
  margin: 0 20px;
}

.kline-chart {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
}

/* 实时状态 */
.realtime-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  background: #f9f9f9;
  font-size: 12px;
  color: #666;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 日期选择弹窗 */
.date-picker-content {
  padding: 20px;
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.date-picker-header .title {
  font-size: 16px;
  font-weight: 500;
}

.date-picker-header .confirm {
  color: #409eff;
}

.quick-selects {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.quick-select {
  padding: 6px 12px;
  font-size: 14px;
  color: #666;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.quick-select.active {
  background: #409eff;
  color: #fff;
}

.custom-date-range {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}
</style>
