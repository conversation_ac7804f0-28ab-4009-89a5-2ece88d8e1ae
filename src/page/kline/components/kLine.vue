<template>
  <div class="kline-container">
    <!-- 股票信息头部 -->
    <div class="stock-header">
      <div class="stock-info">
        <span class="stock-symbol">{{ stockSymbol }}</span>
        <span class="stock-name">{{ stockName }}</span>
      </div>
      <div class="price-info">
        <span class="current-price">${{ currentPrice }}</span>
        <span class="price-change" :class="priceChangeClass">
          {{ priceChange > 0 ? '+' : '' }}{{ priceChange.toFixed(2) }} ({{ priceChangePercent.toFixed(2) }}%)
        </span>
      </div>
    </div>

    <!-- K线图容器 -->
    <div class="chart-container" ref="chartContainer">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">数据加载中...</div>
      </div>
      <div ref="klineChart" style="width: 100%; height: 400px;"></div>
    </div>

    <!-- 周期选择器已移到上方富途风格的控件中 -->
  </div>
</template>

<script>
import { init, dispose } from 'klinecharts'
import AggregatesService from '@/services/aggregatesService'

export default {
  name: 'KLineComponent',
  props: {
    symbol: {
      type: String,
      default: 'AAPL'
    },
    name: {
      type: String,
      default: ''
    },
    // 外部控制的周期
    activePeriod: {
      type: String,
      default: 'minute_5'
    }
  },
  data() {
    return {
      stockSymbol: this.symbol,
      stockName: this.name,
      currentPrice: 0,
      priceChange: 0,
      priceChangePercent: 0,
      previousClose: 0,

      chart: null,
      selectedPeriod: this.activePeriod, // 使用外部传入的周期

      // 支持的周期
      periods: [
        { key: 'minute_1', label: '1分钟', timespan: 'minute', multiplier: 1 },
        { key: 'minute_5', label: '5分钟', timespan: 'minute', multiplier: 5 },
        { key: 'minute_15', label: '15分钟', timespan: 'minute', multiplier: 15 },
        { key: 'minute_30', label: '30分钟', timespan: 'minute', multiplier: 30 },
        { key: 'hour_1', label: '1小时', timespan: 'hour', multiplier: 1 },
        { key: 'day', label: '日K', timespan: 'day', multiplier: 1 },
        { key: 'week', label: '周K', timespan: 'week', multiplier: 1 },
        { key: 'month', label: '月K', timespan: 'month', multiplier: 1 }
      ],

      isLoading: false
    }
  },

  computed: {
    priceChangeClass() {
      return this.priceChange > 0 ? 'positive' : this.priceChange < 0 ? 'negative' : 'neutral'
    },

    currentPeriodConfig() {
      return this.periods.find(p => p.key === this.selectedPeriod)
    }
  },

  watch: {
    symbol(newSymbol) {
      if (newSymbol && newSymbol !== this.stockSymbol) {
        this.stockSymbol = newSymbol
        this.loadData()
      }
    },

    name(newName) {
      if (newName && newName !== this.stockName) {
        this.stockName = newName
      }
    },

    // 监听外部传入的周期变化
    activePeriod(newPeriod) {
      if (newPeriod && newPeriod !== this.selectedPeriod) {
        console.log('🔄 [KLine] 外部周期切换:', newPeriod)
        this.selectedPeriod = newPeriod
        this.loadRealData()
      }
    }
  },

  async mounted() {
    await this.$nextTick()
    this.initChart()
    this.loadData()
  },

  beforeDestroy() {
    this.cleanup()
  },

  methods: {
    // 初始化图表
    initChart() {
      try {
        if (this.$refs.klineChart) {
          this.chart = init(this.$refs.klineChart)
          console.log('✅ K线图初始化成功')
        }
      } catch (error) {
        console.error('❌ K线图初始化失败:', error)
      }
    },

    // 加载数据
    async loadData() {
      if (!this.chart || !this.stockSymbol) {
        console.warn('⚠️ 图表未初始化或股票代码为空')
        return
      }

      try {
        // 先生成测试数据快速显示，避免空白
        const testData = this.generateTestData()
        console.log('📊 使用测试数据:', testData.length, '条')

        if (testData.length > 0) {
          this.chart.applyNewData(testData)
          console.log('✅ 测试数据加载成功')
        }

        // 获取前收盘价
        await this.loadPreviousClose()

        // 加载真实数据替换测试数据
        await this.loadRealData()

      } catch (error) {
        console.error('❌ 加载数据失败:', error)
      }
    },

    // 获取前收盘价
    async loadPreviousClose() {
      try {
        const response = await AggregatesService.getPreviousClose({
          ticker: this.stockSymbol,
          adjusted: true
        })

        if (response.success && response.data && response.data.results && response.data.results[0]) {
          this.previousClose = response.data.results[0].c
          console.log(`✅ 获取前收盘价成功: ${this.previousClose}`)
        }
      } catch (error) {
        console.error('❌ 获取前收盘价失败:', error)
      }
    },

    // 生成测试数据
    generateTestData() {
      const data = []
      const now = Date.now()
      let price = 100

      for (let i = 30; i >= 0; i--) {
        const timestamp = now - i * 24 * 60 * 60 * 1000
        const change = (Math.random() - 0.5) * 4
        const open = price
        const close = price + change
        const high = Math.max(open, close) + Math.random() * 2
        const low = Math.min(open, close) - Math.random() * 2

        data.push({
          timestamp,
          open,
          high,
          low,
          close,
          volume: Math.floor(Math.random() * 1000000)
        })

        price = close
      }

      return data
    },

    // 加载真实数据
    async loadRealData() {
      if (!this.chart || !this.currentPeriodConfig) {
        console.warn('⚠️ 图表未初始化或周期配置为空')
        console.log('📊 当前图表状态:', this.chart ? '已初始化' : '未初始化')
        console.log('📊 当前周期配置:', this.currentPeriodConfig)
        console.log('📊 当前选中周期:', this.selectedPeriod)
        return
      }

      this.isLoading = true

      try {
        const endDate = new Date()
        const startDate = new Date()

        // 🔧 根据周期类型设置不同的历史数据范围
        console.log('🔧 [DEBUG] 开始设置时间范围，当前周期配置:', this.currentPeriodConfig)

        switch (this.currentPeriodConfig.timespan) {
          case 'minute':
            // 对于分钟级数据，根据具体的multiplier来设置不同的时间范围
            if (this.currentPeriodConfig.multiplier <= 5) {
              startDate.setDate(endDate.getDate() - 2) // 5分钟及以下用2天
              console.log('🔧 [DEBUG] 5分钟K线，使用2天时间范围')
            } else if (this.currentPeriodConfig.multiplier <= 15) {
              startDate.setDate(endDate.getDate() - 3) // 15分钟用3天
              console.log('🔧 [DEBUG] 15分钟K线，使用3天时间范围')
            } else {
              startDate.setDate(endDate.getDate() - 5) // 30分钟用5天
              console.log('🔧 [DEBUG] 30分钟K线，使用5天时间范围')
            }
            break
          case 'hour':
            startDate.setDate(endDate.getDate() - 30) // 30天
            console.log('🔧 [DEBUG] 小时K线，使用30天时间范围')
            break
          case 'day':
            startDate.setFullYear(endDate.getFullYear() - 1) // 1年
            console.log('🔧 [DEBUG] 日K线，使用1年时间范围')
            break
          case 'week':
          case 'month':
            startDate.setFullYear(endDate.getFullYear() - 2) // 2年
            console.log('🔧 [DEBUG] 周/月K线，使用2年时间范围')
            break
          default:
            startDate.setDate(endDate.getDate() - 2) // 默认2天
            console.log('🔧 [DEBUG] 未知周期，使用默认2天时间范围')
        }

        console.log(`📊 加载真实数据 ${this.stockSymbol} - ${this.currentPeriodConfig.label}`)
        console.log(`📊 周期配置:`, this.currentPeriodConfig)
        console.log(`📊 时间单位: ${this.currentPeriodConfig.timespan}, 倍数: ${this.currentPeriodConfig.multiplier}`)
        console.log(`📊 日期范围: ${startDate.toISOString().split('T')[0]} 到 ${endDate.toISOString().split('T')[0]}`)

        const response = await AggregatesService.getAggregates({
          ticker: this.stockSymbol,
          multiplier: this.currentPeriodConfig.multiplier,
          timespan: this.currentPeriodConfig.timespan,
          from: startDate.toISOString().split('T')[0],
          to: endDate.toISOString().split('T')[0],
          adjusted: true,
          sort: 'asc',
          limit: 5000
        })

        console.log('📊 API响应:', response)
        if (response.data && response.data && response.data.length > 0) {
          const klineData = response.data.map(item => ({
            timestamp: item.timestamp,
            open: item.open,
            high: item.high,
            low: item.low,
            close: item.close,
            volume: item.volume
          }))

          // 验证数据格式
          const isValidData = klineData.every(item =>
            typeof item.timestamp === 'number' &&
            typeof item.open === 'number' &&
            typeof item.high === 'number' &&
            typeof item.low === 'number' &&
            typeof item.close === 'number' &&
            typeof item.volume === 'number'
          )

          console.log('📊 数据格式验证:', isValidData ? '通过' : '失败')
          console.log('📊 真实数据:', klineData.length, '条')
          console.log('📊 数据范围:', new Date(klineData[0].timestamp).toLocaleDateString(), '-', new Date(klineData[klineData.length - 1].timestamp).toLocaleDateString())
          console.log('📊 第一条数据:', klineData[0])
          console.log('📊 最后一条数据:', klineData[klineData.length - 1])
          console.log('📊 图表实例:', this.chart ? '存在' : '不存在')

          // 重要：使用applyNewData完全替换图表数据
          if (this.chart && isValidData) {
            try {
              this.chart.applyNewData(klineData)
              console.log('✅ 图表数据更新成功')
            } catch (error) {
              console.error('❌ 图表数据更新失败:', error)
            }
          } else {
            if (!this.chart) {
              console.error('❌ 图表实例不存在，无法更新数据')
            }
            if (!isValidData) {
              console.error('❌ 数据格式不正确，无法更新图表')
            }
          }

          // 更新价格信息
          const lastBar = klineData[klineData.length - 1]
          this.updatePriceInfo(lastBar.close)
          console.log('✅ 真实数据加载并更新图表成功')
        } else {
          console.warn('⚠️ 没有获取到数据，继续使用当前数据')
          console.log('📊 响应详情:', response)
        }
      } catch (error) {
        console.error('❌ 加载真实数据失败:', error)
      } finally {
        this.isLoading = false
      }
    },

    // 切换周期
    async changePeriod(periodKey) {
      if (this.isLoading || periodKey === this.selectedPeriod) {
        console.log('🔄 正在加载中或周期相同，跳过')
        return
      }

      this.selectedPeriod = periodKey
      console.log('🔄 切换周期:', this.currentPeriodConfig.label)

      // 切换周期时重新加载数据
      await this.loadRealData()
    },

    // 更新价格信息
    updatePriceInfo(newPrice) {
      const oldPrice = this.currentPrice
      this.currentPrice = newPrice
      this.priceChange = newPrice - this.previousClose
      this.priceChangePercent = this.previousClose > 0 ? (this.priceChange / this.previousClose) * 100 : 0
    },

    // 清理资源
    cleanup() {
      if (this.chart) {
        dispose(this.chart)
        this.chart = null
      }
      console.log('🧹 K线组件资源清理完成')
    }
  }
}
</script>

<style scoped>
.kline-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  color: #333333;
}

.stock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.stock-info {
  display: flex;
  flex-direction: column;
}

.stock-symbol {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.stock-name {
  font-size: 14px;
  color: #666666;
  margin-top: 2px;
}

.price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.current-price {
  font-size: 20px;
  font-weight: bold;
  color: #333333;
}

.price-change {
  font-size: 14px;
  margin-top: 2px;
}

.price-change.positive {
  color: #26A69A;
}

.price-change.negative {
  color: #EF5350;
}

.price-change.neutral {
  color: #888888;
}

.chart-container {
  flex: 1;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 10px;
  min-height: 400px;
  position: relative; /* Added for loading overlay positioning */
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10; /* Ensure it's above other content */
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.loading-text {
  font-size: 16px;
  color: #555;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 周期选择器样式已移到父页面 */
</style>
