<template>
  <div class="futu-stock-detail">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <div class="nav-left">
        <van-icon name="arrow-left" @click="$router.back()" />
      </div>
      <div class="nav-center">
        <div class="ai-button">
          <van-icon name="robot" />
          <span>AI</span>
          <van-icon name="arrow" />
        </div>
      </div>
      <div class="nav-right">
        <van-icon name="search" />
        <van-icon name="like-o" />
      </div>
    </div>

    <!-- Tab栏 -->
    <div class="main-tabs">
      <div class="tab-item active">图表</div>
      <div class="tab-item">期权</div>
      <div class="tab-item">ETF</div>
      <div class="tab-item">评论</div>
      <div class="tab-item">资讯</div>
      <div class="tab-item">分析</div>
      <div class="tab-item">公司</div>
    </div>

    <!-- 股票信息区域 -->
    <div class="stock-info">
      <div class="stock-header">
        <div class="stock-name">
          <h2>{{ stockData.symbol }} {{ stockData.name }}</h2>
          <p class="market-time">收盘价 {{ stockData.closeTime }} ({{ stockData.market }})</p>
        </div>
        <div class="market-icons">
          <div class="icon-us">🇺🇸</div>
          <div class="icon-realtime">📊</div>
          <div class="icon-24h">24</div>
          <div class="icon-history">📈</div>
          <div class="icon-alert">⚡</div>
          <div class="icon-more">📋</div>
        </div>
      </div>

      <div class="price-section">
        <div class="main-price">
          <span class="price" :class="{ 'up': stockData.change > 0, 'down': stockData.change < 0 }">
            {{ stockData.price }}
          </span>
          <span class="change-icon">↑</span>
        </div>
        <div class="change-info">
          <span class="change-amount">{{ stockData.change > 0 ? '+' : '' }}{{ stockData.change }}</span>
          <span class="change-percent">{{ stockData.changePercent > 0 ? '+' : '' }}{{ stockData.changePercent }}%</span>
        </div>
      </div>

      <div class="stock-metrics">
        <!-- 第一行 -->
        <div class="metric-row">
          <div class="metric-item">
            <span class="label">最 高</span>
            <span class="value red">145.000</span>
          </div>
          <div class="metric-item">
            <span class="label">今 开</span>
            <span class="value green">141.970</span>
          </div>
        </div>
        <!-- 第二行 -->
        <div class="metric-row">
          <div class="metric-item">
            <span class="label">最 低</span>
            <span class="value green">141.850</span>
          </div>
          <div class="metric-item">
            <span class="label">昨 收</span>
            <span class="value">142.830</span>
          </div>
        </div>
        <!-- 第三行 -->
        <div class="metric-row">
          <div class="metric-item">
            <span class="label">成交额</span>
            <span class="value">234.2亿</span>
          </div>
          <div class="metric-item">
            <span class="label">市盈率TTM</span>
            <span class="value">46.77</span>
          </div>
          <div class="metric-item">
            <span class="label">总市值</span>
            <span class="value">3.54万亿</span>
          </div>
        </div>
        <!-- 第四行 -->
        <div class="metric-row">
          <div class="metric-item">
            <span class="label">成交量</span>
            <span class="value">1.62亿股</span>
          </div>
          <div class="metric-item">
            <span class="label">市盈率(静)</span>
            <span class="value">49.32</span>
          </div>
          <div class="metric-item">
            <span class="label">总股本</span>
            <span class="value">244.00亿</span>
          </div>
        </div>
        <!-- 第五行 -->
        <div class="metric-row">
          <div class="metric-item">
            <span class="label">换手率</span>
            <span class="value">0.69%</span>
          </div>
          <div class="metric-item">
            <span class="label">市净率</span>
            <span class="value">42.20</span>
          </div>
          <div class="metric-item">
            <span class="label">流通值</span>
            <span class="value">3.41万亿</span>
          </div>
        </div>
        <!-- 第六行 -->
        <div class="metric-row">
          <div class="metric-item">
            <span class="label">52周最高</span>
            <span class="value">153.105</span>
          </div>
          <div class="metric-item">
            <span class="label">委 比</span>
            <span class="value">0.00%</span>
          </div>
          <div class="metric-item">
            <span class="label">流通股</span>
            <span class="value">235.02亿</span>
          </div>
        </div>
        <!-- 第七行 -->
        <div class="metric-row">
          <div class="metric-item">
            <span class="label">52周最低</span>
            <span class="value">86.614</span>
          </div>
          <div class="metric-item">
            <span class="label">量 比</span>
            <span class="value">0.90</span>
          </div>
          <div class="metric-item">
            <span class="label">振 幅</span>
            <span class="value">2.20%</span>
          </div>
        </div>
        <!-- 第八行 -->
        <div class="metric-row">
          <div class="metric-item">
            <span class="label">历史最高</span>
            <span class="value">153.105</span>
          </div>
          <div class="metric-item">
            <span class="label">股息TTM</span>
            <span class="value">0.040</span>
          </div>
          <div class="metric-item">
            <span class="label">平均价</span>
            <span class="value">144.246</span>
          </div>
        </div>
        <!-- 第九行 -->
        <div class="metric-row">
          <div class="metric-item">
            <span class="label">历史最低</span>
            <span class="value">0.031</span>
          </div>
          <div class="metric-item">
            <span class="label">股息率TTM</span>
            <span class="value">0.03%</span>
          </div>
          <div class="metric-item">
            <span class="label">每 手</span>
            <span class="value">1股</span>
          </div>
        </div>
      </div>

      <!-- 盘前数据 -->
      <div class="premarket-info">
        <div class="premarket-icon">⏰</div>
        <span class="premarket-label">盘前</span>
        <span class="premarket-price green">143.050</span>
        <span class="premarket-change green">-1.950</span>
        <span class="premarket-percent green">-1.34%</span>
        <span class="premarket-time">08:12 (美东)</span>
        <div class="premarket-arrow">⌄</div>
      </div>

      <!-- 财报提醒 -->
      <div class="earnings-alert">
        <div class="earnings-icon">⏰</div>
        <span>2025/08/27 (美东) 盘后发布财报</span>
        <div class="earnings-calendar">📅</div>
        <div class="earnings-expand">⌄</div>
      </div>

      <!-- AI资讯推荐 -->
      <div class="ai-news-section">
        <div class="ai-news-icon">📰</div>
        <span class="ai-news-text">下一波AI催化剂来了？大摩详解英伟达GTC三大亮点：欧洲投资...</span>
      </div>
    </div>


    <!-- MA指标信息 -->
    <div class="ma-indicators">
      <span class="ma-item">MA</span>
      <span class="ma-value ma5">MA20:137.860</span>
      <span class="ma-value ma10">MA250:126.194</span>
    </div>

    <!-- K线图区域 -->
    <div class="chart-container">
      <KLineComponent
        :symbol="stockSymbol"
        :name="stockName"
      />
    </div>

    <!-- 买卖盘 -->
    <div class="order-book">
      <div class="order-header">
        <div class="order-title">买盘</div>
        <div class="order-title">卖盘</div>
        <div class="order-icon">🏛️</div>
      </div>
      <div class="order-content">
        <div class="order-left">
          <span class="order-name">道琼斯</span>
        </div>
        <div class="order-right">
          <span class="order-price">42967.62</span>
          <span class="order-change red">+101.85</span>
          <span class="order-percent red">+0.24%</span>
          <div class="order-trend">⌄</div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="bottom-actions">
      <div class="action-buttons">
        <button class="action-btn primary" @click="goBuy()">交易</button>
        <button class="action-btn secondary">模拟交易</button>
      </div>
      <div class="action-icons">
        <van-icon name="bell-o" />
        <van-icon name="share-o" />
        <van-icon name="ellipsis" />
      </div>
    </div>
  </div>
</template>

<script>
import KLineComponent from './components/kLine.vue'
import * as api from '@/axios/api'
import { Toast } from 'mint-ui'

export default {
  name: 'futu-stock-detail',
  components: {
    KLineComponent
  },
  data () {
    return {
      stockSymbol: 'NVDA', // 当前股票代码
      stockName: '英伟达', // 股票名称
      currentTime: '19:54',
      batteryLevel: 61,

      stockData: {
        symbol: 'NVDA',
        name: '英伟达',
        price: '145.000',
        change: 2.170,
        changePercent: 1.52,
        high: '145.000',
        low: '141.850',
        open: '141.970',
        prevClose: '142.830',
        volume: '234.2亿',
        pe: '46.77',
        marketCap: '3.54万亿',
        closeTime: '06/12 16:00:00',
        market: '美东',
        preMarket: {
          price: '143.050',
          change: '-1.950',
          changePercent: '-1.34',
          time: '07:54 (美东)'
        },
        type: 'US'
      },
      singDetails: {},
      isOptionOpt: false
    }
  },
  created () {
    this.initTime()
    this.parseRouteParams()
    this.getSingDetails()
  },
  beforeDestroy() {
    if (this.timeTimer) {
      clearInterval(this.timeTimer)
    }
  },
  methods: {
    // 初始化时间显示
    initTime() {
      this.updateTime()
      this.timeTimer = setInterval(this.updateTime, 1000)
    },

    // 更新时间显示
    updateTime() {
      const now = new Date()
      this.currentTime = now.toTimeString().substr(0, 5)

      // 模拟电池电量变化
      if (Math.random() < 0.01) { // 1%概率变化
        this.batteryLevel = Math.max(20, Math.min(100, this.batteryLevel + (Math.random() > 0.5 ? 1 : -1)))
      }
    },

    // 解析路由参数
    parseRouteParams() {
      const { query } = this.$route

      if (query.symbol || query.code) {
        this.stockSymbol = (query.symbol || query.code).toUpperCase()
        this.stockName = query.name || this.stockSymbol
        this.stockData.symbol = this.stockSymbol
        this.stockData.name = this.stockName
        this.stockData.type = query.type || 'US'

        console.log('📊 [StockDetail] 股票参数:', {
          symbol: this.stockSymbol,
          name: this.stockName,
          type: this.stockData.type
        })
      } else {
        // 使用默认参数
        console.log('📊 [StockDetail] 使用默认股票: NVDA')
      }

      this.kLineDetails = query
    },

        // 处理价格更新
    handlePriceUpdate(priceData) {
      this.stockData = {
        ...this.stockData,
        ...priceData
      }

      console.log('💰 [StockDetail] 价格更新:', priceData)
    },

    goBuy () {
      this.$router.push({
        path: '/buyStocks',
        query: {
          name: this.stockData.name,
          code: this.stockData.symbol,
          m: this.stockData.price,
          id: this.stockData.symbol
        }
      })
    },
    async getSingDetails () {
      try {
        // 这里可以调用API获取股票详细信息
        // const response = await api.getStockDetail({
        //   symbol: this.stockSymbol,
        //   type: this.stockData.type
        // })

        // 使用模拟数据
        this.singDetails = {
          name: this.stockData.name,
          gid: this.stockData.symbol,
          nowPrice: this.stockData.price,
          hcrate: this.stockData.changePercent,
          open_px: this.stockData.open,
          today_max: this.stockData.high,
          today_min: this.stockData.low,
          preclose_px: this.stockData.prevClose,
          business_amount: '2342000000',
          business_balance: '354000000000',
          type: this.stockData.type
        }

        console.log('📈 [StockDetail] 股票详情:', this.singDetails)
      } catch (error) {
        console.error('❌ [StockDetail] 获取股票详情失败:', error)
        Toast('获取股票详情失败')
      }
    }
  }
}
</script>

<style scoped>
.futu-stock-detail {
  background: #ffffff;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 通用颜色类 */
.red {
  color: #ff4444 !important;
}

.green {
  color: #00aa44 !important;
}

/* 状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  background: #000;
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signal-bars {
  display: flex;
  gap: 2px;
}

.signal-bars .bar {
  width: 3px;
  height: 12px;
  background: #666;
  border-radius: 1px;
}

.signal-bars .bar.active {
  background: white;
}

.battery {
  display: flex;
  align-items: center;
  gap: 4px;
}

.battery-icon {
  width: 20px;
  height: 10px;
  border: 1px solid white;
  border-radius: 2px;
  position: relative;
}

.battery-icon::after {
  content: '';
  position: absolute;
  right: -3px;
  top: 3px;
  width: 2px;
  height: 4px;
  background: white;
  border-radius: 0 1px 1px 0;
}

/* 顶部导航栏 */
.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.nav-left, .nav-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.ai-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #000;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
}

/* Tab栏 */
.main-tabs {
  display: flex;
  padding: 0 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  padding: 12px 16px;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  color: #000;
  border-bottom-color: #ff4444;
}

/* 股票信息区域 */
.stock-info {
  padding: 20px;
  background: white;
}

.stock-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.stock-name h2 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #000;
}

.market-time {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.market-icons {
  display: flex;
  gap: 6px;
}

.market-icons > div {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 500;
}

.icon-us {
  font-size: 14px;
}

.icon-realtime {
  background: #007AFF;
  color: white;
  font-size: 8px;
}

.icon-24h {
  background: #34C759;
  color: white;
  font-size: 8px;
}

.icon-history {
  background: #FF9500;
  color: white;
  font-size: 8px;
}

.icon-alert {
  background: #FF3B30;
  color: white;
  font-size: 8px;
}

.icon-more {
  background: #8E8E93;
  color: white;
  font-size: 8px;
}

.price-section {
  margin-bottom: 20px;
}

.main-price {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.price {
  font-size: 32px;
  font-weight: 600;
  color: #ff4444;
}

.price.up {
  color: #ff4444;
}

.price.down {
  color: #00aa44;
}

.change-icon {
  font-size: 16px;
  color: #ff4444;
}

.change-info {
  display: flex;
  gap: 12px;
}

.change-amount, .change-percent {
  font-size: 14px;
  color: #ff4444;
}

.stock-metrics {
  margin-bottom: 16px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  flex: 1;
  padding: 0 4px;
  min-width: 0;
}

.metric-item .label {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
}

.metric-item .value {
  font-size: 13px;
  color: #000;
  font-weight: 500;
  text-align: right;
  margin-left: 8px;
}

.metric-item .value.green {
  color: #00aa44;
}

.metric-item .value.red {
  color: #ff4444;
}

.premarket-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  font-size: 14px;
}

.premarket-icon {
  font-size: 16px;
}

.premarket-label {
  color: #666;
}

.premarket-price {
  color: #00aa44;
  font-weight: 500;
}

.premarket-price.green {
  color: #00aa44;
}

.premarket-change, .premarket-percent {
  color: #00aa44;
}

.premarket-change.green, .premarket-percent.green {
  color: #00aa44;
}

.premarket-time {
  color: #666;
  margin-left: auto;
}

.premarket-arrow {
  color: #666;
  font-size: 16px;
}

.earnings-alert {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  font-size: 14px;
  color: #0066cc;
}

.earnings-icon {
  font-size: 16px;
}

.earnings-calendar {
  margin-left: auto;
  font-size: 16px;
}

.earnings-expand {
  color: #666;
  font-size: 16px;
}

.ai-news-section {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  font-size: 14px;
  color: #666;
}

.ai-news-icon {
  font-size: 16px;
  color: #ff4444;
}

.ai-news-text {
  flex: 1;
  line-height: 1.4;
}

/* K线图控制栏 */
.chart-controls {
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.time-controls {
  display: flex;
  gap: 16px;
  overflow-x: auto;
}

.control-btn {
  padding: 6px 12px;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  border-radius: 4px;
}

.control-btn.active {
  background: #f0f0f0;
  color: #000;
  font-weight: 600;
}

.control-btn.chart-icon {
  color: #666;
  font-size: 16px;
}

.control-btn.more-btn {
  color: #999;
  font-weight: bold;
}

/* MA指标信息 */
.ma-indicators {
  padding: 8px 20px;
  background: white;
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.ma-item {
  color: #000;
  font-weight: 500;
}

.ma-value {
  color: #666;
}

.ma-value.ma5 {
  color: #ff6600;
}

.ma-value.ma10 {
  color: #9966ff;
}

/* K线图区域 */
.chart-container {
  flex: 1;
  background: white;
  margin: 0;
  position: relative;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.chart-volume {
  position: absolute;
  bottom: 10px;
  left: 10px;
  display: flex;
  gap: 8px;
  font-size: 12px;
  align-items: center;
}

.chart-volume .volume-label {
  color: #666;
  font-weight: 500;
}

.chart-volume .volume-value {
  color: #ff4444;
  font-weight: 500;
}

.chart-volume .volume-value.red {
  color: #ff4444;
}

.chart-volume .volume-shares {
  color: #666;
}

/* 技术指标栏 */
.tech-indicators {
  padding: 12px 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.indicator-tabs {
  display: flex;
  gap: 16px;
  overflow-x: auto;
}

.indicator-tab {
  padding: 6px 12px;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  border-radius: 4px;
}

.indicator-tab.active {
  background: #f0f0f0;
  color: #ff6600;
}

/* 买卖盘 */
.order-book {
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.order-title {
  font-size: 16px;
  font-weight: 500;
  color: #000;
}

.order-icon {
  font-size: 20px;
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-left {
  display: flex;
  align-items: center;
}

.order-name {
  font-size: 14px;
  color: #000;
  font-weight: 500;
}

.order-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-price {
  font-size: 16px;
  font-weight: 600;
  color: #000;
}

.order-change {
  font-size: 14px;
  color: #ff4444;
  font-weight: 500;
}

.order-change.red {
  color: #ff4444;
}

.order-percent {
  font-size: 14px;
  color: #ff4444;
  font-weight: 500;
}

.order-percent.red {
  color: #ff4444;
}

.order-trend {
  font-size: 16px;
  color: #666;
}

/* 底部操作按钮 */
.bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 12px 24px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
}

.action-btn.primary {
  background: #ff6600;
  color: white;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #000;
}

.action-icons {
  display: flex;
  gap: 20px;
  color: #666;
  font-size: 20px;
}
</style>
