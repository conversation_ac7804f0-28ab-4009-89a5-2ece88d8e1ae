<template>
  <div class="test-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <van-icon name="arrow-left" @click="$router.back()" />
        <span class="title">KLineCharts美股测试</span>
      </div>
      <div class="header-right">
        <van-button size="small" @click="switchStock">切换股票</van-button>
      </div>
    </div>

    <!-- 股票选择器 -->
    <div class="stock-selector">
      <van-field
        v-model="stockSymbol"
        label="股票代码"
        placeholder="请输入美股代码，如AAPL"
        @change="onStockChange"
      />
      <van-field
        v-model="stockName"
        label="股票名称"
        placeholder="请输入股票名称"
      />
    </div>

    <!-- K线图组件 -->
    <div class="chart-section">
      <KLineComponent
        :symbol="currentSymbol"
        :name="currentName"
      />
    </div>

    <!-- 实时信息显示 -->
    <div class="info-section">
      <div class="info-card">
        <div class="info-title">实时信息</div>
        <div class="info-content">
          <div class="info-item">
            <span class="label">当前价格:</span>
            <span class="value" :class="getPriceClass()">{{ latestPrice }}</span>
          </div>
          <div class="info-item">
            <span class="label">涨跌额:</span>
            <span class="value" :class="getPriceClass()">{{ latestChange }}</span>
          </div>
          <div class="info-item">
            <span class="label">涨跌幅:</span>
            <span class="value" :class="getPriceClass()">{{ latestChangePercent }}%</span>
          </div>
          <div class="info-item">
            <span class="label">更新时间:</span>
            <span class="value">{{ formatTime(lastUpdateTime) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试按钮区域 -->
    <div class="actions-section">
      <van-button type="primary" block @click="refreshData">刷新数据</van-button>
      <van-button type="info" block @click="toggleTheme">切换主题</van-button>
    </div>
  </div>
</template>

<script>
import KLineComponent from './components/kLine.vue'
import dayjs from 'dayjs'

export default {
  name: 'KlineTestPage',
  components: {
    KLineComponent
  },
  data() {
    return {
      stockSymbol: 'AAPL',
      stockName: '苹果公司',
      currentSymbol: 'AAPL',
      currentName: '苹果公司',
      latestPrice: 0,
      latestChange: 0,
      latestChangePercent: 0,
      lastUpdateTime: null,
      isDarkTheme: false,

      // 预定义的股票列表
      stockList: [
        { symbol: 'AAPL', name: '苹果公司' },
        { symbol: 'MSFT', name: '微软' },
        { symbol: 'GOOGL', name: '谷歌' },
        { symbol: 'AMZN', name: '亚马逊' },
        { symbol: 'TSLA', name: '特斯拉' },
        { symbol: 'NVDA', name: '英伟达' },
        { symbol: 'META', name: 'Meta' },
        { symbol: 'NFLX', name: '奈飞' }
      ],
      currentStockIndex: 0
    }
  },
  created() {
    // 从路由参数获取股票信息
    const { query } = this.$route
    if (query.symbol) {
      this.stockSymbol = query.symbol.toUpperCase()
      this.currentSymbol = this.stockSymbol
    }
    if (query.name) {
      this.stockName = query.name
      this.currentName = this.stockName
    }
  },
  methods: {
    // 股票代码变化
    onStockChange() {
      this.stockSymbol = this.stockSymbol.toUpperCase()
    },

    // 切换股票
    switchStock() {
      this.currentStockIndex = (this.currentStockIndex + 1) % this.stockList.length
      const stock = this.stockList[this.currentStockIndex]

      this.stockSymbol = stock.symbol
      this.stockName = stock.name
      this.currentSymbol = stock.symbol
      this.currentName = stock.name

      console.log('🔄 [TestPage] 切换到股票:', stock)
    },

    // 处理价格更新
    handlePriceUpdate(priceData) {
      this.latestPrice = priceData.price || 0
      this.latestChange = priceData.change || 0
      this.latestChangePercent = priceData.changePercent || 0
      this.lastUpdateTime = priceData.timestamp || Date.now()

      console.log('💰 [TestPage] 价格更新:', priceData)
    },

    // 获取价格颜色类
    getPriceClass() {
      if (this.latestChange > 0) return 'price-up'
      if (this.latestChange < 0) return 'price-down'
      return ''
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return ''
      return dayjs(timestamp).format('HH:mm:ss')
    },

    // 刷新数据
    refreshData() {
      this.currentSymbol = this.stockSymbol
      this.currentName = this.stockName

      console.log('🔄 [TestPage] 刷新数据:', {
        symbol: this.currentSymbol,
        name: this.currentName
      })
    },

    // 切换主题
    toggleTheme() {
      this.isDarkTheme = !this.isDarkTheme
      document.body.classList.toggle('dark-theme', this.isDarkTheme)

      console.log('🎨 [TestPage] 切换主题:', this.isDarkTheme ? '暗色' : '亮色')
    }
  }
}
</script>

<style scoped>
.test-page {
  background: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 股票选择器 */
.stock-selector {
  background: #fff;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

/* 图表区域 */
.chart-section {
  flex: 1;
  background: #fff;
  margin: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-height: 500px;
}

/* 信息区域 */
.info-section {
  padding: 0 8px;
}

.info-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.info-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item .label {
  color: #666;
  font-size: 14px;
}

.info-item .value {
  font-weight: 500;
  font-size: 14px;
}

.value.price-up {
  color: #f56c6c;
}

.value.price-down {
  color: #67c23a;
}

/* 操作按钮区域 */
.actions-section {
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 暗色主题 */
.dark-theme .test-page {
  background: #1a1a1a;
  color: #fff;
}

.dark-theme .page-header,
.dark-theme .stock-selector,
.dark-theme .chart-section,
.dark-theme .info-card {
  background: #2d2d2d;
  border-color: #404040;
  color: #fff;
}

.dark-theme .title,
.dark-theme .info-title {
  color: #fff;
}

.dark-theme .info-item .label {
  color: #ccc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 12px 16px;
  }

  .stock-selector {
    padding: 12px 16px;
  }

  .chart-section {
    margin: 4px;
    min-height: 400px;
  }

  .info-content {
    grid-template-columns: 1fr;
  }

  .actions-section {
    padding: 12px 4px;
  }
}
</style>
