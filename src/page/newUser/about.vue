<template>
  <div class="bijnm">
    <div class="headf">
      <div>
        <h2>
          <span class="hbnh"><a class="fan" @click="$router.back()"></a></span>
          {{ title }}
        </h2>
      </div>
    </div>
    <div class="hezi" v-show="type == '1'">
      <img src="~@/assets/imgRed/logo.png" />
      <h6>{{ $t("jy321") }}</h6>
      <p>{{ $t("jy320") }}：V1.1.2</p>
    </div>
    <div v-show="type != '1'" class="hezi" style="line-height: 0.5rem;">
      <div v-if="type == 2" v-html="userInfo.companyInfo"></div>
      <div v-else-if="type == 3" v-html="userInfo.certImg1"></div>
      <div v-else-if="type == 4" v-html="userInfo.certImg2"></div>
      <div v-else-if="type == 5" v-html="userInfo.tradeAgreeText"></div>
      <div v-else-if="type == 6">{{ $t("jy319") }}</div>
      <div v-else></div>
    </div>
  </div>
</template>
<script>
import * as api from '@/axios/api'
export default {
  name: 'about',
  created () {
    var that = this
    // 接收页面传值
    if (this.$route.query.e) {
      switch (this.$route.query.e) {
        case '1':
          this.title = this.$t('jy318')
          break
        case '2':
          this.title = this.$t('jy317')
          break
        case '3':
          this.title = this.$t('jy316')
          break
        case '4':
          this.title = this.$t('jy314')
          break
        case '5':
          this.title = this.$t('jy192')
          break
        case '6':
          this.title = this.$t('jy315')
          break
        default:
          break
      }
      this.type = this.$route.query.e
    }
  },
  data () {
    return {
      title: this.$t('jy314'),
      type: 0,
      userInfo: {}
    }
  },
  mounted () {
    this.getUserInfo()
  },
  methods: {
    async getUserInfo () {
      // 获取用户信息
      let data = await api.getInfoSite()
      if (data.code === 200) {
        this.userInfo = data.data
      } else {
      }
    }
  }
}
</script>
<style scoped>
.bijnm {
  background: #fff;
  min-height: 100vh;
}

.headf {
  width: 100%;
  height: 1.1748rem;
  background: linear-gradient(-55deg, rgb(241, 22, 20), rgb(240, 40, 37));
}

h2 {
  text-align: center;
  height: 1.2549rem;
  width: 100%;
  position: relative;
  line-height: 1.2549rem;
  font-size: 0.4806rem;
  color: #fff;
  background: transparent;
  font-weight: 500;
  z-index: 3;
}

.hbnh {
  position: absolute;
  left: 0.4005rem;
  font-size: 0.4272rem;
  font-weight: 500;
}

.fan {
  width: 0.2403rem;
  height: 0.4272rem;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAAAXNSR0IArs4c6QAAAVdJREFUSEut1usqBVEYxvH/c1MuRJJDkhwTEpIkJUlyCEk++CJJckpycw49Gs3Wa+y9Z82ePd/Xr5n38KwRNR7bE0APMK1OHdtTwAWQGZcdQbZngPMc+QImK0O2Z4GzgIxLuqoE2Z4DTgMyJuk6K08yZHseOAnIqKSbRo2TINsLwHFARiTdxkaVQrYXgaOADEu6K3a7LWR7CTjMD2XdaYq0rZHtFWA/Rz6BIUn3reau6RvZXgX28kMfOfLQbnj/QbbXgN380HuOPJZtwB/I9jqwE5ABSc9lyJ8a2d4AtgPSL+klBfmFbG8CWwHpk/SaivxAtjMgg7Inq0mvpLcqSHehTOvKpzU+oSvFDlj99gesOJCDkp7Kip+yIknT3XL7C0tbum9lMbIMHKQkQEqwJWVSKZTPWTElq0dt6GYxt6uHf8DqX0cBq39BBqz+lR2w+j8RAfv9rfkGqF24CUdT9E4AAAAASUVORK5CYII=)
    no-repeat 50%;
  background-size: 100%;
  display: inline-block;
  margin-right: 0.1335rem;
  vertical-align: middle;
  margin-top: -0.0534rem;
}

.hezi {
  width: 9.2115rem;
  border-bottom: 0.0267rem solid #e0e0e0;
  margin: 0 auto;
  margin-top: 1.1748rem;
  padding-bottom: 0.534rem;
}

.hezi img {
  width: 5rem;
}
</style>
