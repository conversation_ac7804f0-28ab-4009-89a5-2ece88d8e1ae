<template>
    <div class="bijnm">
        <div class="headf">
            <div >
                <h2 ><span  class="hbnh"><a
                    @click="$router.go(-1)" class="fan"></a></span>   {{ $t('jy600') }} </h2>
            </div>
        </div>

        <div class="uploads">
            <el-upload
            class="avatar-uploader"
            :action="admin + '/user/upload.do'"
            list-type="picture-avatar"
            name="upload_file"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :on-error="handleError"
            :before-upload="beforeAvatarUpload"
            >
            <img v-if="avatar" :src="avatar" class="id-img avatar" style="width: 100%;height: 100%;" />
            <i v-else class="iconfont icon-zhaopian"></i>
            <span v-if="!avatar && !imgStatus" class="btn-title">{{$t('hj197')}}</span>
            <span v-if="imgStatus" class="btn-title">{{$t('hj198')}}</span>
            </el-upload>
        </div>
    </div>

</template>

<script>
import * as api from '@/axios/api'

export default{
  data () {
	      return {
	        avatar: '',
      admin: '',
      img1Key: '',
      userInfo: {},
      imgStatus: false
	      }
	    },
  mounted () {
    this.admin = process.env.API_HOST
    if (this.admin == undefined) {
      this.admin = 'https://api.captiveventure.com'
    }
  },
  created () {
    this.getUserInfo()
  },
	    methods: {
    async getUserInfo () {
      // 获取用户信息
      let data = await api.getUserInfo()
      if (data.code === 200) {
        // 判断是否登录
        this.$store.commit('dialogVisible', false)
        this.$store.state.userInfo = data.data
        this.userInfo = data.data
      } else {
        this.$store.commit('dialogVisible', true)
      }
    },
    handleAvatarSuccess (res, file) {
      this.imgStatus = false
      this.avatar = res.data.url
    },

    beforeAvatarUpload (file) { },
    // 上传
    handleFile: function (e) {
      // var that = this
      let $target = e.target || e.srcElement
      let file = $target.files[0]
      // if(file.size > 1024 * 1024 *20){
      let i = false
      if (i) {
        Toast(this.$t('hj206'))
      } else {
        // Indicator.open('Loading...')
        this.img1Key = file
        // this.$refs.formDate.submit()
        // this.uploadIdImg({upload_file:file})
        var reader = new FileReader()
        reader.onload = data => {
          let res = data.target || data.srcElement
          this.avatar = res.result
        }
        reader.readAsDataURL(file)
      }
    },
    handleError () {
      this.imgStatus = false
    }
  }
}
</script>

<style scoped >

.bijnm {
    background: #fff;
    min-height: 100vh;
}

.headf {
    width: 100%;
    height: 1.1748rem;
    background: linear-gradient(-55deg,rgb(241, 22, 20),rgb(240, 40, 37));
}

h2 {
    text-align: center;
    height: 1.2549rem;
    width: 100%;
    position: relative;
    line-height: 1.2549rem;
    font-size: 0.4806rem;
    color: #fff;
    background: transparent;
    font-weight: 500;
    z-index: 3;
}

.hbnh {
    position: absolute;
    left: 0.4005rem;
    font-size: 0.4272rem;
    font-weight: 500;
}
.fan {
    width: 0.2403rem;
    height: 0.4272rem;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAAAXNSR0IArs4c6QAAAVdJREFUSEut1usqBVEYxvH/c1MuRJJDkhwTEpIkJUlyCEk++CJJckpycw49Gs3Wa+y9Z82ePd/Xr5n38KwRNR7bE0APMK1OHdtTwAWQGZcdQbZngPMc+QImK0O2Z4GzgIxLuqoE2Z4DTgMyJuk6K08yZHseOAnIqKSbRo2TINsLwHFARiTdxkaVQrYXgaOADEu6K3a7LWR7CTjMD2XdaYq0rZHtFWA/Rz6BIUn3reau6RvZXgX28kMfOfLQbnj/QbbXgN380HuOPJZtwB/I9jqwE5ABSc9lyJ8a2d4AtgPSL+klBfmFbG8CWwHpk/SaivxAtjMgg7Inq0mvpLcqSHehTOvKpzU+oSvFDlj99gesOJCDkp7Kip+yIknT3XL7C0tbum9lMbIMHKQkQEqwJWVSKZTPWTElq0dt6GYxt6uHf8DqX0cBq39BBqz+lR2w+j8RAfv9rfkGqF24CUdT9E4AAAAASUVORK5CYII=) no-repeat 50%;
    background-size: 100%;
    display: inline-block;
    margin-right: 0.1335rem;
    vertical-align: middle;
    margin-top: -0.0534rem;
}
.uploads {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.3rem;
}
</style>
