<template>
  <div>
    <div class="bank-to-stock">
      <div class="headf">
        <div>
          <h2>
            <span class="hbnh"
              ><a class="fan" @click="$router.back()"></a
            ></span>
            {{ $t("jy329") }}
          </h2>
        </div>
      </div>
      <div class="page-warper">
        <div class="balance text-center">{{ $t("jy356") }}</div>
        <div class="money text-center">{{ enableAmt }}</div>
        <div class="form-box">
          <div class="form-item" @click="show = true">
            <div class="form-label text-center">{{ $t("jy357") }}</div>
            <input readonly :value="card" />
          </div>
          <div class="form-item">
            <div class="form-label text-center">{{ $t("jy358") }}</div>
            <input :placeholder="$t('jy359')" type="number" v-model="money" />
          </div>
          <div class="form-item">
            <div class="form-label text-center">{{ $t("jy327") }}</div>
            <input :placeholder="$t('jy327')" type="password" v-model="withPwd" />
          </div>
        </div>
        <div class="tips">
          {{ $t("jy360") }}
        </div>
        <van-button
          @click="sure"
          block
          color="linear-gradient(-55deg,rgb(241, 22, 20),rgb(240, 40, 37))"
          size="large"
          :style="{ borderRadius: '24px' }"
          >{{ $t("sure") }}</van-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import * as api from '@/axios/api'
export default {
  data () {
    return {
      enableAmt: 0, // 可用余额
      card: '', // 银行卡
      money: '', // 余额
      withPwd: '' // 交易密码
    }
  },
  mounted () {
    this.getBankListFn()
    this.getUserInfoFn()
  },
  methods: {
    async getBankListFn () {
      const res = await api.getBankCard()
      if (res) {
        this.card = res.data.bankNo
      }
    },
    async getUserInfoFn () {
      const res = await api.getUserInfo()
      if (res) {
        this.enableAmt = res.data.enaleWithdrawAmt
        // this.enableAmt = res.data.hkAmt;
      }
    },
    async sure () {
      if (!this.card) {
        this.$toast(this.$t('tips1'))
        return
      }
      if (!this.money) {
        this.$toast(this.$t('tips2'))
        return
      }
      if (!this.withPwd) {
        this.$toast(this.$t('jy125') + this.$t('jy327'))
        return
      }
      if (this.money > this.enableAmt) {
        this.$toast(this.$t('tips3'))
        return
      }
      const data = await api.outMoney({ amt: this.money, withPwd: this.withPwd })
      if (data.code === 200) {
        this.$toast(this.$t('tips4'))
        setTimeout(() => {
          $router.back()
        }, 1500)
      } else {
        this.$toast(data.msg ? data.msg : this.$t('hj183'))
      }
    }
  }
}
</script>

<style lang="less" scoped>
.text-center {
  text-align: center;
}
input {
  height: 55px;
  width: 100%;
  background: transparent;
  font-size: 0.4272rem;
  color: #000;
  padding: 12px 18px;
  border-radius: 8px;
  background-color: #ffffff;
  border: 1px solid #dadbde;
  text-align: center;
}
.page-warper {
  padding: 0 16px;
}
.flex {
  display: flex;
}
.col-center {
  align-items: center;
}
.row-between {
  justify-content: space-between;
}
h2 {
  text-align: center;
  height: 1.2549rem;
  width: 100%;
  position: relative;
  line-height: 1.2549rem;
  font-size: 0.4806rem;
  color: #fff;
  background: transparent;
  font-weight: 500;
  z-index: 3;
}
.hbnh {
  position: absolute;
  left: 0.4005rem;
  font-size: 0.4272rem;
  font-weight: 500;
}
.fan {
  width: 0.2403rem;
  height: 0.4272rem;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAAAXNSR0IArs4c6QAAAVdJREFUSEut1usqBVEYxvH/c1MuRJJDkhwTEpIkJUlyCEk++CJJckpycw49Gs3Wa+y9Z82ePd/Xr5n38KwRNR7bE0APMK1OHdtTwAWQGZcdQbZngPMc+QImK0O2Z4GzgIxLuqoE2Z4DTgMyJuk6K08yZHseOAnIqKSbRo2TINsLwHFARiTdxkaVQrYXgaOADEu6K3a7LWR7CTjMD2XdaYq0rZHtFWA/Rz6BIUn3reau6RvZXgX28kMfOfLQbnj/QbbXgN380HuOPJZtwB/I9jqwE5ABSc9lyJ8a2d4AtgPSL+klBfmFbG8CWwHpk/SaivxAtjMgg7Inq0mvpLcqSHehTOvKpzU+oSvFDlj99gesOJCDkp7Kip+yIknT3XL7C0tbum9lMbIMHKQkQEqwJWVSKZTPWTElq0dt6GYxt6uHf8DqX0cBq39BBqz+lR2w+j8RAfv9rfkGqF24CUdT9E4AAAAASUVORK5CYII=)
    no-repeat 50%;
  background-size: 100%;
  display: inline-block;
  margin-right: 0.1335rem;
  vertical-align: middle;
  margin-top: -0.0534rem;
}
.headf {
  width: 100%;
  height: 1.1748rem;
  background: linear-gradient(-55deg, rgb(241, 22, 20), rgb(240, 40, 37));
}
.bank-to-stock {
  min-height: 100vh;
}

.balance {
  font-size: 20px;
  color: #666666;
  margin-top: 15px;
}

.money {
  font-size: 22px;
  font-weight: bold;
  color: #000000;
  margin: 10px 0 50px;
  color: #11183c;
}

.form-box {
  .form-label {
    font-size: 20px;
    color: #000000;
    margin-bottom: 18px;
    margin-top: 20px;
    color: #11183c;
  }

  /deep/ .uni-input-wrapper {
    color: #11183c;
  }
}

.tips {
  font-size: 20px;
  color: #999999;
  margin: 18px 0 35px;
}
</style>
