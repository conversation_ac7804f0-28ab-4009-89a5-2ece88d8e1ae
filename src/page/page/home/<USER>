<template>
  <div class="homecontainer">
    <div class="hbanr">
      <div class="hbx">
        <div class="lety">
          <!-- <img @click="getHeaderlink(0)"
                        src="~@/assets/imgRed/logo.png"> -->
          <div class="shuru" @click="getsearch">
            <span class="jiao"></span>
            <p>{{ $t("jy184") }}</p>
          </div>
        </div>
        <div class="rety">
          <!-- <span class="yf"></span> -->
          <!-- <span class="kf"></span> -->
        </div>
      </div>
      <div class="five">
        <div class="danw" @click="getHeaderlink(1)">
          <img
            src="data:image/png;base64,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"
          />
          <p>{{ $t("hj52") }}</p>
        </div>
        <div class="danw" @click="getHeaderlink11(2)">
          <img
            src="data:image/png;base64,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"
          />
          <p>{{ $t("jy185") }}</p>
        </div>
        <div class="danw" @click="getHeaderlink11(3)">
          <img
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAx5JREFUeF7tm7uvDVEUh78f4v0mQaNSEFEoKNxCgURCI3RIJBKvhigo5EZCISiuxn+ARu1RX9EohF5cKpVHgqvBkiV7ZJx7XnPO3Dnn7D07mZycOTOz9/rOXmvvvea3ReJFidtPDaDuAQMiYGYLgOXhWAmsyH3Pzvvnqhbn/Xo/3gHjkh70YkphFzCzOaFib3S+od0Yk7/HAZRVfgJbJL0p+sCmAMxsPnAJGANWNxi6tGglFV1/tJde0ArAXeBcRQ0vq5pSAXwHFpfVsoqeUyoAq6jRZVZzAngSXNbddiEwJWmqXSWtXGAUAbSy8wVwWtKrZhekAMDtngZ2SXrdCCEVAG73Y0kHYgbgvu7HJ+Az4G58EpgbjJ6WtCRmADNGATN7D2zMjJY0o8fH5AI1gMaZYN0DaheoY4CPCL6c/ltSDIJfwtK9UgBOfRLwyrcCO8qc8Ld5VrNRoHIAE8AVST+yhprZNuAWsH+WQQwcwISki62MNLN9wE1g+yyBGCgAzx9skPS1nXEhnXYcuJafoZUEZKAAJiXt7tYQM1sEXAhpN88TllEGCuCRpINFrTCztcBV4BTgech+yugByAXKTSE+HPLhukcKowsgB2IvcA9Y1wOE0QfgRpvZTuA5MK8ghDgABAgPgSMpA/ARwidVRUpUPeA8cKeI9UBUANJ1gaSDoJntAe4nNwyamU+EbgCHk5oIhanwOHBm1KfCvSyGPNJfBqJYDPlyeL2kb10sh48B12NbDrvdtyW5qqRpMTOf53tmKMqESGZ00imxDMJH4NmQJEU9Q/VP05RiWvw/oUcNIMG3w3UPyA9JtQvULpCeQiSpGOACqbdBIOUvaH+HRVYWBqIXSXXKoEUvk2sHwIWSY83UojGpxFoBcKnsWUkvm10QEwAXSz8NYmmXxfQllk5eLp/8hgl/Ve3pKt8ysyZsmVkWPmfobTuF34p+L2/DRIe0louPO+3myjZHZdCyDVX5HWD96gHyzfwFbC5t01QV/5iZeZDKwPj2N4fWCKwTaL/vg/dWSf4+oXDpVYxQuKJhvaEGMKz/TFXtSr4H/AGhbCxfeVNoYgAAAABJRU5ErkJggg=="
          />
          <p>{{ $t("jy157") }}</p>
        </div>
        <div class="danw" @click="getHeaderlink(4)">
          <img
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAB+xJREFUeF7tmguwttUUx39/RIaILmIUlVQzNCO6SlSalJToMiml4hOFUEYzRa41uqBi3KoJ5VK6uTcqmaQaZBDdXVJCKpeiyDL/M/s5s88+7/s++3nf57xfZ75vzXwz3zlnP2ut/d9rr+sWyzhpGd8/ywFYbgELjEBEPAnYGngRsCGwDrA68Djgf8B9wJ+BG4FfAz8ArpD0zwVWbYb9glyBiHg0sCewD7A98MiOm3kA+CbwBeAiSQZqQahXANLGDwXeCTytJ41vBj4EfH4hgOgNgIh4GXAK8KyeNl6yuRZ4s6Sr+uQ/MQARsSJwPHDIiCv1b+BK4EfADcAdwL1p/crAmsD6wAuBzYAVhmzyv8AHbBGSHuoDiIkAiIjVgAuBLYYocznwWeA8Sf+qUTgingjsASwBNhnyzTeAvftwlGMDEBFPB74PrDtAyR8CR0jyiY9NEbED8BFgowFMfgy8VNLfxhYwbhRIJ+9wtUEh/H7g7T51STGJYs23EfEogwm8H/D/c7I/MAgOpWNRZwtInv4SYKtC4m+BV0j65ViatHwUEb5m5wFrFEu/5iszLuDjAHBSOuVcj+sc7yX9cSE2n1mDr9ulwFqFnHdJsiPuTJ0AiAhnc3Zs+Xe3AVtK+sMg6RHhJMjfbQNsnHzGkwEnS6Z/AHemTPAa4FuSbhm2k4hwmHVEsQNuyInT8yX5IDpRNQDpLtq8Ha4acnjbQtLPSqkR8XjgbY7dYyRFvtu2tHMHmXZEbAd8t8gwL5f0kk677+IEI+Ig4HOFgLdKcvIzhyJiZ+AzwFO7KlSs90nvL8nZYCnjWODdxa93luQUupqqLCAiHgHcCjwj4+wwtHmZkETEkSl1reJdoamvyG6S7HhnKSIeC9jk185+fZWkYTnJQFFVSqYT/XrBweGnVOow4KMVm+q6xOF1W0lXFyC42PpiwWwTST6cKqoFwOFnt4zj1ZI2L5Txz1eMUflVKZoc5UaS/tJ8kPySy+jcCk6V9JZapq0ARMRjgLsAO7WGlkhyijtD6Yq4WBmUsdXqUrPuDEkHFsC/B3hf9jtHo7Vq84IaAOxxv5cJcBGyqiQXMw0AuwPn1OxgwjXuC6yfO8UUFm8q+D6nNiTWAGBPa4/b0DWSXLHNUkRcnBofE+6v6vMTJR1eyLeDzq/BgZLOqOFWA8CXgb0yZidLcnxvTt/Vm+/lsBK2Ro8ua26RNKfnEBFfTRVkw+djklyTtFINAK7stsw4HSLpkxkArti+0yqp3wVr5plnRLhHcFQmwuX3q2tEjgQgIvz33xTx/5WS3ANoLKC8IjVyJ12zq6SLMh3eBMweCuD03L+7WNJ/RgkbCkBErAp8yeVmwWA7SS5IGgAs2MKmSYdJ+nimw2uAswYo4Oh1tktpSX8dpOAoAGzWNu+cXOOvK8lW0QBgwVZgmnS0pA9mOmwKzEmSCmUuleRoNo9GAfBg4dgcgg6XNCfTiwhniM79p0nHSXLKPUsR4UjlbvQgZ/yQpLKZMvPtKADKjo6bDueWu4wI9+dePs3dA/MAsPyIcJntiLWf65RcJ0kD99oFAKehvxgAgDsyr3o4AJBdiecCP58WAEvDBxwjKU9/5+AfEVMF4NOphT1NIzhS0nHZiT/F88Um/582ACcC75jm7i3PzjgNZdwA2Rb4nX2Ra4BpA2DPe8KYANjRXg/ck6bFKwFOrd2AMbkPcDdwO+Cus1tvnhzP9P4i4tQ0kWrEe364/7QB2DdNcbti4N7B6yV5VNaJ0gadB+xSfOh5xJJpA+B5njfThRxN3E67PyKeAHg+eJd/zpmkPoMzUs8O1wNekHWXS3kevW0s6fq+AHCPvxlC2FTXkOSHDKXHtcnahFsLq+zDmZwiIg4GnNI2LXInW82oy9fAvGvIPYp9Jbly9dVwy/xPmU53ShrYoB2VB9i0T0vjqGMl5dVWCYLvcd4ub1PawDo3d65eu8lhPH8PvEGSexKzFBHHAO4WeWZwkCTXBPOorRq0eSLp76N2FBGfAt7Ytuvs725Z3RYRHog4hI1DBt2t909Icto+j5IlPDhqgNrFbIcqGRE7pScttRuZ6dikbrNnDW0g+BTd9fkV4P7EZYOGMbXC83V9AeA77Brcj59qyIMOj7JmLCtNkXxv3YD14ymTH0n53315/7GGeZc1vQCQNuHx9dEdhHv89dpBU58OPCZe2icAqwAeanZxavbeP03OcNSzOEcEW9cBowan46DRGwDJCjyQOHkcRSq+mTcTqPimdUnfAJif+wN2in2Shy5blYlSHwJ6BSBZga/AZcDz+lAQ+Ilbc8N6epPK6B2ABIKfx54PvHhCBd353aeP12DD9OgdgNSW8uk7k3zdhAA4wfoKcO2kr8EWDIAUw3dMHWS/0Bj0bG5CHHAt4qrR7Xg3YS9p6/fXChzbAiLCr8T8asSD0XxyXCt7knUuys5Mz/HKwWgnvp0BSBt3LT7p/e6k6JDFfjrrIeh7x32hVg1AMnXH+AP60LxnHh7VO0m6oCvfKgAiwuWry013Wx+uZD9xlKQPd1GwFYD0QsSvtfzGbzHQnpKqH2vUALA0pr+TAO2n+GsP6xGUjGsAcOm6EKFtkk22fbuTpG+3LfLf2zpCrvPdjFhsNGd8Pkr5GgvwKPyZiwgBO8NtJPlNcyvVAOC29+nAs1u5Lf0FfjPouaEfdlRRKwBVXBbxouUALOLD60X1Zd4C/g9PyMhfHXucCAAAAABJRU5ErkJggg=="
          />
          <p>{{ $t("jy186") }}</p>
        </div>
        <!-- <div class="danw" @click="getHeaderlink(5)">
          <img
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAWVJREFUeF7tmrFNA0EQRd+InADogA4IICChBBI6QAgKsCw6QCJHBPTgFkgIcEpCERjRABq0p1sCyyI6rRb/7wJud97+me+9+4H4L8TrxwCsAHECbgFxAXgI/rZAZh4DM+AMONhSZXwAz8B9RCxLjQOAzLwEHoGdLS18vaxv4DoiniIzT4AXoeIrjALhtABYAOciJ79e5qIAWAF7ogBWBUCKFj+UbQBWgFvAM8BD0C4gTMA2mJlzYQH4fYBfiSnLv94FLpQh2AV8GfJlyJchX4bsAsIEbIO2QdugbdA2KGwC/jJkG7QN9muDb8D7RPPpCDjc9KyeW+A2Iu6mAJCZD8CNAWwgYAV0PATlW+AT+JpiBoy5x93/NgMmqv3vxzgl5pygelJ0zApfAeXPgl5WuI6IMTNc0+L7TSZQ+0VKKramxV+Hj6Pt99DXigbQ13m0340V0J55XytaAX2dR/vdWAHtmfe14g+P2dYX8Ba0FAAAAABJRU5ErkJggg=="
          />
          <p>{{ $t("hj241") }}</p>
        </div> -->
      </div>
    </div>
    <div class="dang">
      <div class="xbox" @click="getHeaderlink1(1)">
        <img
          src="data:image/png;base64,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"
        />
        <p>{{ $t("hj45") }}</p>
      </div>
      <div class="xbox" @click="getHeaderlink1(2)">
        <img
          src="data:image/png;base64,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"
        />
        <p>{{ $t("jy187") }}</p>
      </div>
      <div class="xbox" @click="getHeaderlink1(3)">
        <img
          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAYAAADhu0ooAAAAAXNSR0IArs4c6QAABWpJREFUaEPtWl1sFFUU/s7spluVH6m1y8xssDYEDcaAhCBsl5b+PIhoYkiA4Is/iUFRQR/EaDAYiA8mPmhAIRKivhCFaIwi8gCldtsCCmIwEkkqNs3u3S4tIDFgO9mZY+6GNrvD1s7uztKp7H2953zn++bcmbnn3Eu4RQbdIjpRFvp/y3Q5oyMZDQaDdwQCgQqvZnh4eNhIJpNXx+OXM6O6rrcw85MAlgNQxwPxwHwCwPdEtDcejx/JxSdLaDAYrPP5fB8DaPEA+UIpHDZNc10ymTyfCTAqNBQKLbIs6xCAGYVG8JDfJUVRlsdisR9HOKWF6roeYuafAdztIbLFUrloWdbC/v7+XgmUFqqq6ndE9OgYyNcAJImIi43stj8zS/5BALfnwmbmg4lEYkVaqKZpEQDRHIbnmHljIpE4DMB0m6SLeD5VVVuJ6AMA9+XAXSqE6JRCtwN4KdOAmf8IBAILe3t7/*********************************/xJNYKIT4vKbMSgGuatgaAnfc5IcT9Uug/ACoz4/r9/qq+vr7LJeBSUshZs2bNSKVSl2xBhoQQt0mhN3xkhBCTdms4lp6y0JKusRKClzNafkdLuLxKCV1euvksXU3TNgHYCEBzmJU+AFuFEHvGso9GIjN8FYosKGodYkqzAWbeHT4a3UxA1u+x6Izqur6CmQ/kQWbElBVFWZxZMo1OAHSsufErgJ8oABcMera+7YdPMn2LFqqq6jYi2lwIIbmXFkJ8aPftamrcQMRyM17o2BVu63jBVaGapj0O4JtCGCmK8rA9o53Lli1UFKsLQMH9KAI/s6Qt+qmrQiWYqqpvEtGrAKodCu4H8I4QYkem/cnW1umGZcj3ss4hjt3sCjPviVfP3LR6//6sErLopVsgoZxu3c0N+wCsyjH5Xrit47ViYnlGaFdT43oivuF9JcLxaVWDjQ/sP2tMeqHdrQ0PwUK3vSwEcJksZcGS9vZ0f6eYMeEZ7ayvn0oB30kC5tiEMIhXho9Evy5G4IjvhAvtbl66F6C1djEM3l7fFt3ghkiJ4YpQBuh4U2QRFP/UfIhZbC0mYNuNPnRq+l0DYflehkKhBy3Lkh09J+OaEEL2bFN246KF/rZqbsWVi9UHXeziXzHJWrD0SOd5TdPkx2m9E4UZNqcMw2gaHBz829X/aHdLwzowduVJZkxzYqxZcrRj33+0W52E2iKE2Oqq0GPNDVsYeNtJ9PFsGLyzvi2azqCqqiuJ6MvxfMaY/0gI8aKrQk+0ROaYrJweqyueB9HTw5YSbmpvH5I+VVVV0yorK88AuCcPDGlqKopSH4vFTrgqVIJFmyLz/EQbGTQlT1Kj5habb0WOdp3L9A+FQrplWW8AqHGIK49Jdgsh5F45axT9MXJIYMLNykLz6TBMeLocEChn9FbK6IC9kE6lUjMvXLiQdLBSPGVSU1MT9Pv9stjPHFeFEFPk2Yv8N87PnCGi9fF4fKenVDggo+v688xs531GCDGPdF1/X55s23AGFEUJx2KxHgf4njAJhUKzLcuStW7WPQx5Eh6Px1+h61XDLwAUG+OLALYA+FYIEffo8b5P0zQdgGzcye2pvZdlKYoyPxaL/Zo+B9U0TXbSnvJEatwl8ZkQ4mkJmRZaV1c3fWhoSO4Zc112cDf0zUPrud5mTZ+Aj55sB4PBe30+n+zEz715XEoW6axpmo8lk8k/RyJkHeFXV1dPraioeBfAcwD8JaNROmDZcdhtGMbr9oI8512F63cCVxPRI8wsr7PkvLBUOr55IV8joh5mPmSa5heZWcz6ZeYFOYmNJ+3tk3yfeVlovk/M6/bljHo9Q/ny+xcuWZRZFpiovgAAAABJRU5ErkJggg=="
        />
        <p>{{ $t("jy86") }}</p>
      </div>
      <div class="xbox" @click="getHeaderlink1(4)">
        <img
          src="data:image/png;base64,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"
        />
        <p>{{ $t("jy188") }}</p>
      </div>
      <div class="xbox" @click="getHeaderlink1(5)">
        <img
          src="data:image/png;base64,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"
        />
        <p>{{ $t("jy189") }}</p>
      </div>
      <div class="xbox" @click="getHeaderlink1(6)">
        <img
          src="data:image/png;base64,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"
        />
        <p>{{ $t("jy169") }}</p>
      </div>
      <div class="xbox" @click="getHeaderlink1(7)">
        <img
          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAYAAADhu0ooAAAAAXNSR0IArs4c6QAACdZJREFUaEPdm39wFVcVx79nXxJo+fVAIOzdCBFLaWkFLVRpaCAJajuCWsAitoOWsXXqr7FlnA5V26LTVjtaRe0gttVOaxWpnRYHaeVHQgiQYJU6WkvlRxFM9m6etAgdfiQveXucQ9/LbJbd7L6XH0LPXy+798f53LP33HvPuSEMsEyfPr14z549HQPcLWigOiwtLb0ykUj8FsAVAHYS0Wds224ZqP4HCjShlPoHgMs8YJu01te/o0CVUjcB+LUfiogqbNtuGgjYgbCooZT6e/aT7cbEzC84jjPvHQFqmuYiIno2BIaZ+WrHcfb0N2x/W5SUUi8DeH8PIOu11gsuaFDLsuYz84YICDYMY1pLS8sr/Qnbrxa1LOsl+TRjAKzTWi+JUa7gIv0GqpT6KIBNAZrJWuqHcsVZaa3/WTBJRMX+BN0B4Fpf/7uTyeSc48ePHwTwbu87IvqVbdufvaBAx40bN8cwjPqAdXO+bdsblVJfAfBT3/vORCJxWXNz8+v9AdsvFlVKbQUw16fwHq21zFcuKyu7yHVdATJ9ZX6htb71ggBVSlUA2OVXlpkXOY7zXO65ZVnLmflhX7k0M1/qOM6Rvobtc4uaprmRiD7mVZSZX3EcZ5pYM/e8tLR0SCKR+BeAMT6o1VrrL5/XoKZpziCiPwcoeZPWeq3/uVJqBYDv+p63JRKJ9zY3N+u+hO1Tiyqlngdwg0/BfVrrKQBkCekmo0aNGj548ODDAEb6PPAq27bvPK9Ay8rKLJlXrutOJaIfAd3PuET0Odu2nwpTWil1H4CVvvenAXwVwP5MJrM/lUr9p7fQsSxaVlY2SmAACNClRDRJfgO4BMDQMCWI6JBt25MBdIaVKS8vT6bTaXE+w3uAOU5EB5j5AIB9AM7+bm9v33/s2LG34gxCF+iYMWOGlpSUiOICMCkLdBYOwKg4jQV42tscx3k8qq5lWQ8y891R5ULep3LwuQFwXXf/sGHDDhw8eLA9V4fGjRs3xjCM3wCoAWAU2FlQteZkMnnJ3r1701FtKqVGAxAPHPp1RLUR8F6+IlnOlmmtT8sxah2AxQU01FOVNBEtsm37D3HbtSzri8z8SB8PtnT/kNZ6BVmW9TozT4yrUEA5sZjscmTuHGTm/QA2F7Lom6Z5uWEYVdnpMpmZZSq9B0BRL/Sr01rPFYvK+hZ1RMoQ0ZGsMxCQ/fJb5kIqlfo3gEwvFOmxqoRHW1tbBVYc4GTXdSdlnaH8LQeDKIf6Pa313bk5uh3A5UE9MvNtI0eOfCrOXOsv2LB2s3tmcWL3hJSRE9T1Z+eoFBg7dmxpUVFRHQBZ2P1ykpnnOY7TMNAgUf0ppT6djS4mAjx+veu681Op1Cl512X20tLSsYZhbCWi9wV0cJqI5Ii1LarzgXpvmubNRPQkgHMgAYjRPi6W7FpevIpll5otAGQD7hep9EmttRzB/q9imuZSInoiBHIzgAVeyG4WzWmeXdOk8AeCYJl5oeM4QSGSAYFXSi0D8FgI5CbDMBa0tLSc8SsT6LFky+e6rlj2qgDt25h5geM4fxwQMk8nSqnPA3g0aK0loheLi4sXHj58uC1Ir1DXPH78+JGdnZ1i2RkBFduzG4KNAwVrmuYXiGhNyHKyMZlMLuxpZehxDZo4ceKIM2fObCaiDwYAyUbhU1rrqLhtr8fCsqzbmXl1COTvk8nk4qjlL2qxhcC2tbW9AEBCJH6R0McSx3HkHNovkg2k/SQE8vlkMrkkCjLQGQVpmz0gC+ysINh897VxR6Snz5WZn5UsXdykcqRFc0rJMa64uFhgKwMUlSiCN/cZlyW0XHl5+eB0On005ETzjNb65p7OubG8bljvEtAqKirayMxzfGXatNYX9ZrO04BsYBKJhJw1/bJWa7003/11bIvmelNKyee709t7Nso3tS9BpS2llGw7u74gInratu1b8oWMPUe9AKZp3kpEsmB7RUZZstp5S2NNjUXIXEVU9NLM2tpuFsx6/XvlSoBhGLW2bf+4EMiCQC3LWsXMX/MSEdG3bNt+IF/K3XMrp7pMjQCGAOhgxnNGwlgzc2v9dvLEgPNtN6h8IZ+ubCI+4mtM9pbr81Wosbry2yASi/nlNWZaMyhR/OSMrVtP5NtuX4HaMn28jRmGMamlpUUyZHnJrpo5SwkcGgoFcIpBa8ng1RVbG/6aV+O+wnlZNLsHftPXxhmt9bBC5s62qqqhgwxXIvJSP0r+ROA1fKZzXUVT0zmb9qjKeYEqpSTfKad2r7ystZ4e1dGECRPMjo6O5QAk0N0wYsSI78uOZlfNnMcInE8G7RgBT1ACP5+5pUHivLEkL9BspE72nF0SlcCVAHVHR8ddWQd2safqw1rrr++sqpppGG63u0ZEWM18NrXRbYr4iCRhtQWgNe0ubaiurw8Nkku9vECVUpK8lSSuV1ZorR/yD2s2niNlJZEUFAA/orUul3qNNbPlosaVuTaIeXnxW6cfaR9+8SeI6EsAqiN0tRl0z6y67XIYD5S8QE3T3EZEEo70WvRsFtvzqMg0zWVEJDkVq4fvqklrffag0FRdeScT/dDT6qsVddu7wHdWz5psUOI7EfFnZteYOqu+Xq7inSN5gSqlJNnTLZ+ZyWQmplIpibJLjHgRM98vYcmIifNfIpqXux73l6qq0WnDFW9ekqvHRBWzarc3Nc6tvAFMD4ZFKb39MHDdrLoGWf4KB83Gk/xZrZNa6+FKqblEJPmTqKs2coZd47ru/a2trbJh75LGmtnPALixCxT0uJvO3JUoMcQrD44YOHm9q+TEqeoZIVdkY1vUsqxqZpbomlfECq8B+HCEIi4zPw3g3rAIfmN15XUg8oZnToJwBxhhSSoZtN0E1JFh1H7o2vpGWnluDjanV2zQkJskMQYaGzKZzDdSqVTg3Omy4EoYTQ2VhwCa4GlU9r6lnrm7j8DrXaBu0JBTO2ds2NMVzoxSJDaoaZo/I6Lboxr0vJf1Vjyy7GVjya6a2SsJECcWLMxVFdt2SFYhb4kNqpQKuiAV1OHfiOibPk8cS7FdNTUTCJ2HQjJqp0a8641RV/wuOg0Z1Fk+oMf8dw28DUp223VdmYOStDrnvkIs0rfXVIkZy/U6v7xYUdfQ7bZL3DalXD6gcuE/KH3XCuCBZDL5aJwgVZRyTdWzFzNBcrbdRDYR12zbIXckCpJ8QP0J4xNE9IN0Or3q6NGjJwvqPaDSqzdOKTn+5mibAMmCd4lBPG1m7Q65yV2QxAbNBsfuI6KrXdeV/3JYpbV+o6BeIyo11swWy93hKdZ6TV2D6s1hPDZofwCFtbl7buUUl0mWo7f1I/yyorZB0hEFy3kJKjTZ/a8c6/YZrnHLzPr6Xv2PzP8A0mOzaJ1YyLQAAAAASUVORK5CYII="
        />
        <p>{{ $t("jy190") }}</p>
      </div>
      <div class="xbox" @click="getHeaderlink1(8)">
        <img
          src="data:image/png;base64,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"
        />
        <p>{{ $t("jy191") }}</p>
      </div>
      <div class="xbox" @click="getHeaderlink1(9)">
        <img
          src="data:image/png;base64,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"
        />
        <p>{{ $t("jy159") }}</p>
      </div>
      <div class="xbox" @click="getHeaderlink1(10)">
        <img
          src="data:image/png;base64,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"
        />
        <p>{{ $t("jy192") }}</p>
      </div>
    </div>
    <div class="zoushit" id="Zline">
      <!-- <img src="~@/assets/home/<USER>" alt=""> -->
    </div>
    <div class="hushenmain">
      <div class="hushenwz">{{ $t("jy193") }}</div>
      <div class="zhangfuright">
        <div class="red">{{ $t("jy194") }}</div>
        <div class="zhangfutiao">
          <div
            id="up"
            :style="
              'width:' +
                (
                  (hushentiao.zhang / (hushentiao.zhang + hushentiao.die)) *
                  100
                ).toFixed(2) +
                '%'
            "
          ></div>
          <div
            id="down"
            :style="
              'width:' +
                (
                  (hushentiao.die / (hushentiao.zhang + hushentiao.die)) *
                  100
                ).toFixed(2) +
                '%'
            "
          ></div>
        </div>
        <div class="green">
          <span>{{ $t("jy195") }}</span>
        </div>
      </div>
    </div>
    <div class="touti">
      <img
        src="data:image/png;base64,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"
      />
      <div class="bofang">
        <!-- <div role="alert" class="van-notice-bar" style="background: transparent;">
                    <div role="marquee" class="van-notice-bar__wrap">
                        <div class="van-notice-bar__content"
                            style="transition-duration: 10.3612s; transform: translateX(-333.516px);">
                            周五美联储逆回购工具使用规模为2.050万亿美元</div>
                    </div>
                </div> -->
        <van-notice-bar :text="noticebar"></van-notice-bar>
      </div>
    </div>
    <van-tabs v-model="tabactive" class="ghbn" @click="getNewsList">
      <van-tab :title="$t('jy196')" name="1"></van-tab>
      <van-tab :title="$t('jy197')" name="2"></van-tab>
      <van-tab :title="$t('jy198')" name="3"></van-tab>
      <van-tab :title="$t('jy199')" name="4"></van-tab>
      <van-tab :title="$t('jy200')" name="5"></van-tab>
      <van-tab :title="$t('jy201')" name="6"></van-tab>
      <van-tab :title="$t('jy202')" name="7"></van-tab>
    </van-tabs>
    <div class="ganhj"></div>
    <ul class="wul">
      <li
        class="wlis"
        v-for="(item, index) in newsContent1"
        :key="index"
        @click="getnewsdetail(item)"
      >
        <img class="li_righ" v-if="item.imgurl" :src="item.imgurl" alt="" />
        <div class="li_left tuk">
          <h6>{{ item.title }}</h6>
          <p>{{ new Date(item.addTime) | timeFormat }}</p>
        </div>
      </li>
    </ul>
    <van-dialog
      v-model="show"
      :title="$t('jy203')"
      showCancelButton
      :before-close="onBeforeClose"
    >
      <input
        class="inpt"
        :placeholder="$t('jy204')"
        v-model="secret"
        type="password"
      />
    </van-dialog>
  </div>
</template>
<script>
import * as api from '@/axios/api'
import { init, dispose } from 'klinecharts'
import { Toast } from 'vant'
export default {
  components: {},
  props: {},
  data () {
    return {
      tabactive: 1,
      newsContent1: [],
      noticebar: '',
      hushentiao: '',
      isshow: true,
      show: false,
      secret: ''
    }
  },
  mounted () {
    this.getNewsList(1)
    this.stockgetZdfNumber()
    this.kLineChart = init('Zline')
    this.kLineChart.setStyleOptions({
      candle: {
        type: 'area'
      }
    })
    this.getkline()
  },
  destroyed: function () {
    dispose('chart-type-k-line')
    this.isshow = false
  },
  methods: {
    onBeforeClose (action, done) {
      // 点击了确定按钮
      if (action === 'confirm') {
        if (this.secret) {
          Toast.fail(this.$t('jy205'))
        } else {
          Toast.fail(this.$t('jy204'))
        }

        return done(false) // 直接return它即可阻止
      }
      // 点击了取消按钮
      else {
        done(true) // 关闭弹窗, true可以省略
      }
    },
    getHeaderlink11 (num) {
      if (num == 2 || num == 3) {
        Toast.fail(this.$t('jy507'))
      } else {
        this.show = true
      }
    },
    async getkline (
      baseTimestamp = Date.now(),
      basePrice = 5000,
      dataSize = 800
    ) {
      var opt = {
        code: '000001',
        time: 5,
        ma: 5,
        size: 100
      }
      let data = await api.getMinKEcharts(opt)
      var klinelist = data.data.values.reverse()
      const dataList = []
      for (let i = 0; i < klinelist.length; i++) {
        // const element = klinelist[i];
        const kLineModel = {
          open: klinelist[i][0],
          low: klinelist[i][2],
          high: klinelist[i][3],
          close: klinelist[i][1],
          volume: klinelist[i][4],
          timestamp: klinelist[i][5]
        }
        dataList.unshift(kLineModel)
      }
      // const dataList = []
      // let timestamp = Math.floor(baseTimestamp / 60 / 1000) * 60 * 1000
      // let baseValue = basePrice
      // const prices = []
      // for (let i = 0; i < dataSize; i++) {
      //     baseValue = baseValue + Math.random() * 20 - 10
      //     for (let j = 0; j < 4; j++) {
      //         prices[j] = (Math.random() - 0.5) * 12 + baseValue
      //     }
      //     prices.sort()
      //     const openIdx = +Math.round(Math.random() * 3).toFixed(0)
      //     let closeIdx = +Math.round(Math.random() * 2).toFixed(0)
      //     if (closeIdx === openIdx) {
      //         closeIdx++
      //     }
      //     const volume = Math.random() * 50 + 10
      //     const kLineModel = {
      //         open: prices[openIdx],
      //         low: prices[0],
      //         high: prices[3],
      //         close: prices[closeIdx],
      //         volume: volume,
      //         timestamp
      //     }
      //     timestamp -= 60 * 1000
      //     kLineModel.turnover = (kLineModel.open + kLineModel.close + kLineModel.high + kLineModel.low) / 4 * volume
      //     dataList.unshift(kLineModel)
      // }

      var that = this
      setTimeout(() => {
        this.kLineChart.applyNewData(dataList)
      }, 500)
      if (this.isshow) {
        setTimeout(() => {
          that.getkline()
        }, 3000)
      }
    },
    getsearch () {
      this.$router.push({
        path: '/Searchlist'
      })
    },
    async stockgetZdfNumber () {
      let data = await api.stockgetZdfNumber()
      this.hushentiao = data.data
    },
    async getNewsList (type) {
      let data = await api.queryNewsList(type)
      this.newsContent1 = data.data.list
      this.noticebar = data.data.list[0].title
    },
    getnewsdetail (item) {
      this.$router.push({
        path: '/newPage',
        query: {
          listid: item.id
        }
      })
    },
    async getHeaderlink (val) {
      if (val == 0) {
        this.$router.push({
          path: '/MyList'
        })
      } else if (val == 1) {
        this.$router.push({
          path: '/trading-list'
        })
      } else if (val == 2) {
        this.$router.push({
          // path: '/jijin'
          path: '/about?e=6'
        })
      } else if (val == 3) {
        this.$router.push({
          // path: '/college'
          path: '/about?e=6'
        })
      } else if (val == 4) {
        // this.$router.push({
        //   path: "/service"
        // });
        let data = await api.getInfoSite()
        if (data.code === 200) {
          // this.onlineShow = true;
          const u = navigator.userAgent
          const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
          if (isiOS) {
            window.onpageshow = function (event) {
              if (event.persisted) {
                setTimeout(function () {
                  location.reload(true)
                }, 50)
              }
            }
            let hidden = window.document.hidden || window.document.mozHidden || window.document.msHidden || window.document.webkitHidden
            if (typeof hidden === 'undefined' || hidden == false) {
              location.href = data.data.siteQq
            }
          } else {
            window.open(data.data.siteQq, '_blank')
          }
        } else {
          this.$store.commit('elAlertShow', { 'elAlertShow': true, 'elAlertText': data.msg })
        }
      } else if (val == 5) {
        // this.$router.push({
        //   path: "/openaccount"
        // });
        Toast.fail(this.$t('jy507'))
      }
    },
    getHeaderlink1 (val) {
      switch (val) {
        case 1:
          this.$router.push({
            path: '/Subscription'
          })
          break
        case 2:
          this.$router.push({
            path: '/Subscription?idx=3'
          })
          break
        case 3:
          this.$router.push({
            path: '/Subscription?idx=4'
          })
          break
        case 4:
          this.$router.push({
            path: '/Subscription?idx=5'
          })
          break
        case 5:
          this.$router.push({
            path: '/Subscription?idx=2'
          })
          break
        case 7:
          this.$router.push({
            path: '/stopRecovery'
          })
          break
        case 8:
          this.$router.push({
            path: '/topTen'
          })
          break
        case 9:
          this.$router.push({
            path: '/daylimit'
          })
          break
        case 10:
          this.$router.push('/about?e=5')
          break
      }
    },
    async getInfoSite () {
      let data = await api.getInfoSite()
      if (data.code === 200) {
        this.onlineService = data.data.onlineService
      } else {
        this.$store.commit('elAlertShow', {
          elAlertShow: true,
          elAlertText: data.msg
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.inpt {
  height: 0.8rem;
  background: transparent;
  font-size: 0.4005rem;
  margin-left: 0.4005rem;
  color: #000;
  border: 1px solid #f5f5f5;
}
.red {
  color: #d73d3d;
}

.green {
  color: #20b844;
}

.homecontainer {
  background: #fff;
  min-height: 100vh;
}

.hbanr {
  width: 100%;
  height: 5.65rem;
  background: url("~@/assets/home/<USER>") no-repeat 50%;
  background-size: 100%;
  margin: 0 auto;

  .hbx {
    width: 9.47rem;
    margin: 0 auto;
    height: 1.17rem;
    padding-top: 0.4rem;
    justify-content: space-between;
    display: flex;

    .lety {
      display: flex;
      width: 100%;

      img {
        width: 0.69rem;
        height: 0.69rem;
        margin-right: 0.4rem;
        margin-top: 0.11rem;
      }

      .shuru {
        width: 100%;
        height: 0.91rem;
        background: hsla(0, 0%, 100%, 0.2);
        border-radius: 0.45rem;
        display: flex;

        span {
          width: 0.43rem;
          height: 0.43rem;
          background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA4dJREFUWEe9l0msn2MUxn8PYl4QolhIKhYS00JalAiCIIbeBg1FSrsTQxcsGrEo3WBR0pWZkjSGVEpMiSkiTRPTgkhKYiaGiilmHnna8/H1/r//4N6/+27u/X/vec953nPOc855xZBle39gAXAycCiwH7Ar8C3wIfAa8DTwhKSfh+mbvK9+B2wfCKwAFgI7jKD4G+BW4BZJP40gv0WkB4DtfLsaWAns0lL0MfAq8CnwA7AbcAhwHLBTS+4D4CJJr4wCYhsAtrcH7gQW12EDj9atNnYptL07MAEsBw4umd+ByyQ9MAzEPwDq5vcHfR36qG7y8jAl2be9HXANcGOF7M86v3bQ+TaAK4DbSjiuPk3S5lGMt2Vsnwo8DuwIJBfmSnq7n54tAGzPBiKUmCfWR0r66r8ab+Rtnw08VjmW0M2T9FeXvgbAmnJ9Yn6SpBenarwFYhVwVf1eKOmhTgC2ZwGfVNzWSQrnp71s7wG8D+TvBknz+gG4HFhdmydIemna1hsK2akLV9bP2ZJC0W2WbK8D5gOJ+SxJCcNYlu3jgeZCSyTd3QXgXeAgYL2kc8Zi+V8PJKl/BELRVZKWdQGIQKraakmh4liX7c+qf6yVdEEXgMblN0u6dqzWt1I8DeuAfh5ODqSJ7AncJWnp/wCg0b9G0iVdHngTOGIQVaYKyva+wOd1fqWk67oApPksAX4D9pKUnBjLsn0h8GApm5CU6thDw3OBh+vrpZLuHYv1rfF/LpUV+LUo/l0XgJ2r/u8NvAMcLumP6YKwPRdoWngnA2Kj6QWJzQ1l9HpJzf9TwmE7A0o6aka4sCzN7Y0uZQ2AzHhvAemK6eMLJK2fivWaC+5rzRUD2dWeB44Fnq8+npgtHWWiaYO0nXDeDlxc3zcBcyR93+8yk0eyRUCmopTOuC4MWS7p62HesH0UcAdwWMl+kXlR0nuDznYNpecD99TonbOhZagUpmxsaFquToU7BTivxvZGX/rLGZmGgHTbFZKe6ZsDkzdsJ3nSueZ0HPoSCEv26RjX47WAXxa3294AHF01Zr6kp3po2M89dcN4I/38mCEhSM6kyNwk6fVG1vbp9T3zYQpdD4i+D5NJyZVHyokV37yU8lD5pSae0O0FSXkp9SzbZwGPVHL3gBgJwLAEHLY/CMSMAAjADhDpDU/OGIACcWa9tJqcmJhRAB0gNs84gBaIFLxn/wb6PUsFkO1mDQAAAABJRU5ErkJggg==)
            no-repeat 50%;
          background-size: 100%;
          margin-left: 0.27rem;
          margin-top: 0.24rem;
        }

        p {
          color: #ffd2cc;
          font-size: 0.38rem;
          line-height: 0.85rem;
          margin-left: 0.19rem;
        }
      }
    }

    .rety {
      display: flex;
      margin-top: 0.19rem;

      .yf {
        width: 0.51rem;
        height: 0.51rem;
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAYAAACoPemuAAAAAXNSR0IArs4c6QAAAzdJREFUWEftmE2IlVUYx39/k8SoFoVKofmBFIFfG6NQRrBapGKjy9okbUQKR4mQUafyAxEFjSJBCFxEEkI2i7GPUWFqZdEiZxej0WBCpgUSNtDiL8943st777z3exYj3GdzL+95zjm/83+f8zznvGKKmqYoF/cnmO13gD7gkaTsn8B6ST83o7Tt54F+YHbqdxt4T9LxauNUVcz2PmBvQceAekHS/43A2X4QuASsKPDvk7S/aJxCMNvvx4pSh7+As8BiYG16dhLYKsm14GxPAz4F3kh+F4ERYBMwKz3bI+lg5TgTwGwHUICFBdSLkoZtzwRCrWdT25cJLnwmmO0ngBPAq6lxGHhO0pjtJUBAVoUrA7Md8fRBDuolSZezWW3PA34A5qdn/wKfAV8DV2B8My0ENgCvAQ8nv9/SAuN33GwvBS7k4HolHcraS2C2I54irjKlyqByAz4JnAa6ar3GXFtM/rqk2DhlZnsZcD4Ht0vS4XAaB7O9GziQg3pZ0i/VJrYd/UKR2LVFQR1dfwKOAmdqxWIB3LuSjsh2D3AsQdwEQqmqUAWrXhSxA4SSYX8AP0oqvbZ6ytpeDgzmlNsWYLeAx4DILV3NQNWbsJn2BPc98Cjwd4CNAhHUYTtqJb1mJmrWt+LNjQRY5KZzwIwItwT3YbMDN+qf4nN6PkHb3p7CKWL3TuzqLPhfAb4CIkuX4GyvieAFrgE9kkLqli0F+ufA3MhvkoZsvw2EEBlUt6TBfLpYlzJ8Bvcx8CbwUCL5VdIzLVPd2/2RZCO5hoUyURXeSlD/AQH1XSldZJPZ3pgUCrhKG5WUJdaW+Gz/DjxV0HksQX2btRWVpG7gi/RaI7PHyuJUMJlg14HHU1yHUpslfZMHrlbEn44TRKpnEROrJxlsANgJrIoSJykKe5nVPSjajto46WCSop5WtQ5YSJML/oGOYo3kj45ijaiU9+ko1lGsSIFO5m+jVv4DzGn000Cl+rYfAOIKF6eKScn8Q7k7ZNzErzYb9Ml/AbAy/e+XFMertop4fFfobRGmWrctkk61CxbfLD6Jw1y6WrXDeAP4SFJ2uW5dsXYo2ulb9zzWzuDt9J2yYHcBbbCKnGsh2/cAAAAASUVORK5CYII=)
          no-repeat 50%;
        background-size: 100%;
        margin-right: 0.32rem;
      }

      .kf {
        width: 0.51rem;
        height: 0.51rem;
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAYAAACoPemuAAAAAXNSR0IArs4c6QAAAu5JREFUWEftmEuoTVEYx39/eUV5D7wSg2siRVIGDBkw9SgXIxGXTJCBK+8UImXgnetVbsrESEbIM2Rgooi8yrPLQLg+fcfet3W3vc89+5xzX7lrcur0rW/91v+/vm+vvUUXHeqiXPSA5XWmeyhmZmOBncA8YETeXZYZ/wG4AmyW9CrO0aKYmY0B7gKjy1yg0mlvgemSXnuiEKwBWBpkfwq8qHS1NuaPAyYGMWckLUuCfQKGBkHfgHWSTrYHnJktAI4Dg4L8nyUNS4JZBkAjsELSl2oAmlk/YB+wJi2fpIKLoZUx2H3ggcMEE18CSyRdrwTOzGqAi8CUII+rNhWYVgAqAnZD0qxI6qPAkChJM7Ab2C7pV17AFOu+AqsknTMz3/DMksA8yMwmAOeBGQHILaBW0vNS4MysP7A3Yd0jYKEkLzBfJx9YNKkPsAPYAPSKYJqA1b7bYnAZ1h0G1kv6Hs8tCyyYPAc4DYwMYM4CdZIctNVIsc6Lx4vIiykZm1+xMIOZOZT3u9nB/8+AxZLuRAq7dftd0SDGC8qtS7XfzHyDtcA7SaOyqrJw+LMsMjO3c2Nkb+8ozothK3AJuBBUnVf6QWCTpB9FcnqBLQeuSXpYFlhgrReEQ4wPFvwdnMOPvpiky6UUSTImrY8VVSxhre/0GDA/kfg2sEiS97+yRkVggXorI8s83wGgPq3XmVlfYA8wOYX2J3AqLo6qgEWHfiDQHLaBlOqbG11xslRskjS4ojNWjj9m5ote9etNynw/nyckFR6FVVOsHNBic/4fMDObBHiTzTu85z2Jz2hVFTOzLcC2vERB/E1Jf28ZQcnH97GS+1hK1R0C1lYA9kaSv3tUHWxAdMEMH/Slcnofa5T0OAusRc5SM7ZHXGhl/DLivzWS/LfTRtbr23vAJc16QWkPYL+6N0jyG3OrM+aH7h5QuA910vj3keQg0SeCXdEnguEdDOeKHZFU10qxDoZoc7nu8bWnzW10YECPYnnF/gMpGUo2PhT3BQAAAABJRU5ErkJggg==)
          no-repeat 50%;
        background-size: 100%;
      }
    }
  }

  .five {
    width: 9.35rem;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    margin-top: 0.85rem;

    .danw {
      width: 20%;
      text-align: center;

      img {
        width: 0.85rem;
        height: 0.85rem;
      }

      p {
        font-size: 0.37rem;
        color: #fff;
        margin-top: 0.266rem;
      }
    }
  }
}

.dang {
  width: 9.37rem;
  padding-bottom: 0.43rem;
  background: #fff;
  box-shadow: 0 0.05rem 0.14rem 0 rgb(227 227 227);
  border-radius: 0.266rem;
  margin: 0 auto;
  margin-top: -1.6rem;
  display: flex;
  flex-wrap: wrap;

  .xbox {
    width: 20%;
    text-align: center;
    margin-top: 0.53rem;

    img {
      width: 0.77rem;
      height: 0.77rem;
    }

    p {
      color: #333;
      font-size: 0.35rem;
      margin-top: 0.21rem;
    }
  }
}

.zoushit {
  width: 9.37rem;
  height: 2.67rem;
  margin: 14px auto;
  // background: #fff;
  // border: 1px solid #e0e0e0;
  // border-radius: 10px;
  border: 1px solid #e0e0e0;
  padding: 2px;
  border-radius: 10px;
  margin-top: 10px;

  img {
    width: 100%;
    border-radius: 20px;
  }
}

.hushenmain {
  display: flex;
  align-items: center;
  width: 9.37rem;
  margin: auto;
  justify-content: space-between;

  .hushenwz {
    font-size: 0.4rem;
    font-weight: bold;
    color: rgb(51, 51, 51);
    margin-right: 10px;
  }

  .zhangfuright {
    display: flex;
    align-items: center;
    flex: 1 1 0%;
    font-size: 0.32rem;

    .zhangfutiao {
      width: 40%;
      display: flex;
      align-items: center;
      margin: 0px 5px;

      #up {
        width: 30%;
        height: 7px;
        border-radius: 3px;
        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKoAAAAKCAYAAAAkasVsAAAAAXNSR0IArs4c6QAAAM5JREFUWEftmLsNwjAURe+tgUEYACrHEj1hFqhpiMRE0FNkCMIcDBAjhwShiF+XJ3zd2Ep13vGRIploV+VcDmADcgZg3H3XLgODGyALRoiz93uGsB0cSAAy0DdAFtOy3LFybgnyKEMyYM5AG2nkiqGeQC7MQQoobQNPkd5DzbIrgEnaVjS9KQO9SBWqqdsRTGPgRaT69asNWwbeRNqFmoM82CIWTXIGPkTahKrnqeSSsDfwl0gfocbDxftVqOs1yDmAkb1pRPSXBn6INM59Aza2Nt7dS4nJAAAAAElFTkSuQmCC);
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
      }

      #down {
        width: 70%;
        height: 7px;
        border-radius: 3px;
        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANoAAAAKCAYAAADSI0D0AAAAAXNSR0IArs4c6QAAALZJREFUaEPt2CEOwjAYQOHXe+GGRnIaEjBYOMewwxJMFRyHoUcKJIQA20SreLNrfvHlf0nTwIhvcpgvIaxGHPWIAgpAC5zp2MRpvU8gYUjFyIaE/K9Ar8A6VvWiNzQjc4UUyCHQzX6GZmQ5gJ2hwF3g+DU0I3M9FMgqcPkIzciyAjtMgSTQvoVmZG6FAkUEXldHIysC7FAFgOdjiJG5DQoUE3g87xtZMWAH/6/AFThBt43VrkkMN+jFLeRkDBVgAAAAAElFTkSuQmCC);
        background-size: cover;
        // background-position: center;
        background-repeat: no-repeat;
      }
    }
  }
}

.touti {
  width: 9.37rem;
  height: 1.07rem;
  background: #fef4db;
  border-radius: 0.266rem;
  margin: 0 auto;
  display: flex;
  margin-top: 0.266rem;

  img {
    width: 1.01rem;
    height: 0.48rem;
    margin-left: 0.32rem;
    margin-top: 0.32rem;
  }

  .bofang {
    color: #eb973c;
    width: 7.74rem;
    font-size: 0.37rem;
    line-height: 1.07rem;
    margin-left: 0.32rem;

    .van-notice-bar {
      height: 1.07rem;
      background: transparent;
      font-size: 0.37rem;
      line-height: 1.07rem;
    }
  }
}

.ghbn {
  margin-top: 0.266rem;

  /deep/ .van-tab--active {
    font-weight: 600;
    font-size: 0.44rem !important;
    color: #000;
  }

  /deep/ .van-tab {
    font-size: 0.37rem;
  }

  /deep/ .van-tabs__wrap {
    height: 1rem !important;
  }
}

.ganhj {
  height: 0.03rem;
  background: #ececec;
  margin-top: 0.266rem;
}

.wul {
  width: 8.6rem;
  margin: 0 auto;
  margin-bottom: 2.14rem;

  .wlis {
    width: 8.6rem;
    display: flex;
    // justify-content: space-between;
    border-bottom: 0.03rem solid #ececec;
    margin-top: 0.37rem;
    padding-bottom: 0.266rem;

    .li_righ {
      width: 2.3rem;
      height: 1.74rem;
      margin-right: 0.266rem;
    }

    .li_left.tuk {
      margin-left: 0.266rem;
    }

    .li_left {
      h6 {
        color: #333;
        font-size: 0.37rem;
        line-height: 0.53rem;
        font-weight: 600;
      }

      p {
        color: #999;
        margin-top: 0.16rem;
        font-size: 0.35rem;
      }
    }
  }
}
</style>
