<template>
  <div class="wrapper">
    <!-- <div class="header">
      <mt-header :title="type+'银行卡'">
        <router-link to="/card" slot="left">
          <mt-button icon="back">返回</mt-button>
        </router-link>
      </mt-header>
    </div> -->
    <div class="box transaction">
      <div class="box-contain clearfix">
        <div class="empty text-center" style="margin-top: 0.3rem;">
          <!-- 您已通过实名认证 -->
          <i style="color:red;font-size: 1.2rem;margin-top:0.2rem" class="iconfont icon-chongzhi2"></i>
        </div>
      </div>
    </div>
    <div class="form-block page-part">
      <mt-field :label="$t('hj213')" :placeholder="$t('jy383')" type="text" v-model="bankName"></mt-field>
      <mt-field :label="$t('hj214')" :placeholder="$t('jy384')" type="text" v-model="bankAddress"></mt-field>
      <mt-field :label="$t('hj215')" :placeholder="$t('hj217')" v-model="bankNo"></mt-field>
    </div>
    <!-- <div class="form-block page-part">
        <mt-field label="持卡人姓名" placeholder="和银行卡绑定一致" type="text" v-model="username"></mt-field>
        <mt-field label="银行预留手机号" placeholder="请输入持卡人手机号" type="text" v-model="username"></mt-field>
    </div> -->
    <div class="rule-box">
      <div class="title"> {{ $t('hj165') }}：</div>
      <ul>
        <li> {{ $t('jy380') }}</li>
        <li> {{ $t('jy381') }}</li>
        <li> {{ $t('jy382') }}</li>
      </ul>
    </div>
    <div class="btnbox">
      <span class="text-center btnok" @click="toSure">{{ $t('sure') }}</span>
    </div>

  </div>
</template>

<script>
import * as api from '@/axios/api'
import { Toast } from 'mint-ui'
import { isNull, bankNoReg, isName } from '@/utils/utils'

export default {
  components: {},
  props: {},
  data () {
    return {
      bankName: '',
      bankNo: '',
      bankAddress: '', // 支行地址
      type: this.$t('jy379')
    }
  },
  watch: {},
  computed: {},
  created () {},
  mounted () {
    this.type = this.$route.query.type ? this.$t('jy378') : this.$t('jy379')
    if (this.$store.state.bankInfo) {
      this.bankName = this.$store.state.bankInfo.bankName
      this.bankNo = this.$store.state.bankInfo.bankNo
      this.bankAddress = this.$store.state.bankInfo.bankAddress
    }
  },
  methods: {
    async toSure () {
      // 添加银行卡
      if (isNull(this.bankNo) || !bankNoReg(this.bankNo)) {
        Toast(this.$t('hj217'))
      } else if (isNull(this.bankName) || !isName(this.bankName)) {
        Toast(this.$t('hj218'))
      } else if (isNull(this.bankAddress) || !isName(this.bankAddress)) {
        Toast(this.$t('hj219'))
      } else {
        let opts = {
          bankName: this.bankName,
          bankNo: this.bankNo,
          bankAddress: this.bankAddress
        }
        if (this.$route.query.type === 'edit') {
          let data = await api.updateBankCard(opts)
          if (data.code === 200) {
            Toast(this.$t('hj256'))
            this.$router.push('/card')
          } else {
            Toast(data.msg)
          }
        } else {
          let data = await api.addBankCard(opts)
          if (data.code === 200) {
            Toast(this.$t('hj220'))
            this.$router.push('/card')
          } else {
            Toast(data.msg)
          }
        }
      }
    },
    goBack () {
      this.$router.back(-1)
    }
  }
}
</script>
<style lang="less" scoped>
  .rule-box {
    padding: 0.2rem 0.3rem;

    .title {
      font-size: 0.3rem;
      height: 0.5rem;
      line-height: 0.5rem;
      margin-bottom: 0.2rem;
    }

    ul {
      li {
        color: #999;
        line-height: 0.5rem;
      }
    }
  }

  .transaction {
    // padding-bottom: 0.2rem;
  }
</style>
