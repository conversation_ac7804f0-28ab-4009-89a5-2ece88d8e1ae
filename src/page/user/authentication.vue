<template>
  <div class="wrapper">
    <div class="box transaction">
      <div class="box-contain clearfix">
        <div class="empty text-center">
          <!-- 您已通过实名认证 -->
          <!-- 审核中 -->
          <i v-show="this.$store.state.userInfo.isActive == 1" style="color:red;font-size: 1.5rem;"
             class="iconfont icon-shenhezhong"></i>
             <!-- 通过 -->
          <i v-show="!showBtn && this.$store.state.userInfo.isActive != 1" style="color:red;font-size: 1.5rem;"
             class="iconfont icon-tongguo1"></i>
             <!-- 未通过 -->
          <i v-show="showBtn" style="color:red;font-size: 1.5rem;" class="iconfont icon-icon-test"></i>
        </div>
      </div>
    </div>
    <div class="form-block">
      <div class="auth-msg" v-if="this.$store.state.userInfo.isActive == 3">
        <p> {{ $t('hj200') }}</p>
        <div>
           {{ $t('hj201') }}：{{this.$store.state.userInfo.authMsg}}
        </div>
      </div>
      <!-- <mt-field label="手机号" placeholder="请输入您的手机号" v-model="form.phone"></mt-field> -->
      <mt-field :label="$t('hj195')" :placeholder="$t('hj207')" type="text" v-model="form.name"></mt-field>
      <mt-field :label="$t('hj196')" :placeholder="$t('hj208')" type="text" v-model="form.idCard"></mt-field>
    </div>
    <div class="upload-box clearfix">
      <!-- <form action=""> -->
      <div class="upload-btn">
        <el-upload
          :with-credentials='true'
          class="avatar-uploader"
          :action="admin+'/user/upload.do'"
          list-type="picture-card"
          name="upload_file"
          :show-file-list="false"
          :on-success="handleAvatarSuccess"
          :on-error='handleError'
          :before-upload="beforeAvatarUpload">
          <img v-if="form.img1key" :src="form.img1key" class="id-img avatar">
          <i v-else class="iconfont icon-zhaopian"></i>
          <span v-if="!form.img1key && !imgStatus" class="btn-title"> {{ $t('hj197') }}</span>
          <span v-if="imgStatus" class="btn-title"> {{ $t('hj198') }}</span>
        </el-upload>

      </div>
      <div class="upload-btn">
        <el-upload
          :with-credentials='true'
          class="avatar-uploader"
          :action="admin+'/user/upload.do'"
          list-type="picture-card"
          name="upload_file"
          :show-file-list="false"
          :on-success="handleAvatarSuccess2"
          :on-error='handleError2'
          :before-upload="beforeAvatarUpload2">
          <img v-if="form.img2key" :src="form.img2key" class="id-img avatar">
          <i v-else class="iconfont icon-zhaopian"></i>
          <span v-if="!form.img2key && !imgStatus2" class="btn-title"> {{ $t('hj199') }}</span>
          <span v-if="imgStatus2" class="btn-title"> {{ $t('hj198') }}</span>
        </el-upload>

      </div>

    </div>
    <div class="rule-box">
      <div class="title"> {{ $t('jy387') }}</div>
      <ul>
        <li> {{ $t('jy386') }}</li>
        <li> {{ $t('jy385') }}</li>
        <li> {{ $t('jy382') }}</li>
      </ul>
    </div>
    <div v-show="showBtn" class="btnbox">
      <span class="text-center btnok" @click="toSure"> {{ $t('hj161') }}</span>
    </div>

  </div>
</template>

<script>

export default {
  components: {},
  props: {},
  data () {
    return {
      form: {
        phone: '',
        name: '',
        idCard: '',
        img1key: '',
        img2key: '',
        img3key: ''
      },
      img1Key: '',
      img2Key: '',
      img3Key: '',
      showBtn: true,
      admin: '',
      imgStatus: false,
      imgStatus2: false
    }
  },
  watch: {},
  computed: {},
  created () {
    if (this.$store.state.userInfo.isActive === 1 || this.$store.state.userInfo.isActive === 2) {
      this.form.idCard = this.$store.state.userInfo.idCard
      this.form.name = this.$store.state.userInfo.realName
      this.form.img1key = this.$store.state.userInfo.img1Key
      this.form.img2key = this.$store.state.userInfo.img2Key
      //   this.form.img3key = this.$store.state.userInfo.img3Key
      this.showBtn = false
    }
  },
  beforeDestroy () {
    if (this.$state.theme === 'red') {
      document.body.classList.remove('red-bg')
      document.body.classList.add('black-bg')
    }
  },
  mounted () {
    if (this.$state.theme === 'red') {
      document.body.classList.remove('black-bg')
      document.body.classList.add('red-bg')
    }
    this.admin = process.env.API_HOST
    if (this.admin === undefined) {
      this.admin = ''
    }
  }

}
</script>
<style lang="less" scoped>
  .transaction {
    color: rgba(100, 100, 100, 0.78);

    .empty {
      width: 100%;
      // height: 1.34rem;
      font-size: 0.43rem;
      color: #888888;
      text-align: center;
      line-height: 2rem;
      // background: url('../../assets/img/thingsOk.png') no-repeat center center;
      background-size: 70%;
    }
  }

  .rule-box {
    padding: 0.2rem 0.3rem;

    .title {
      font-size: 0.3rem;
      height: 0.5rem;
      line-height: 0.5rem;
      margin-bottom: 0.2rem;
    }

    ul {
      li {
        color: #999;
        line-height: 0.5rem;
      }
    }
  }

  .upload-box {
    padding: 0.5rem;

    .upload-btn {
      // border: 1px solid #ddd;
      border-radius: 4px;
      width: 40%;
      height: 1.6rem;
      margin-bottom: 10px;
      float: left;
      margin: 0.2rem 5%;
      text-align: center;
      position: relative;

      .btn-hidden {
        height: 100%;
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 3;
        opacity: 0;
      }

      .id-img {
        max-width: 100%;
        max-height: 100%;
      }

      /deep/ .el-upload--picture-card {
        background: none;
        width: 100%;
        height: 1.6rem;
        line-height: 1.6rem;
      }

      .btn-title {
        position: absolute;
        top: 23px;
        left: 0;
        width: 100%;
      }

      /deep/ .el-upload__input {
        display: none;
      }

    }

  }

  .auth-msg {
    padding: 0.2rem 0.6rem;
    line-height: 0.4rem;

    p {
      color: red;
    }

    div {
      color: #ddd;
    }
  }

</style>
