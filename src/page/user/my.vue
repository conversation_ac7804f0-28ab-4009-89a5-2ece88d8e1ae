<template>
  <div class="wrapper">
    <div class="header">
      <mt-header :title=" $t('jy407')">
        <router-link to="/user" slot="left">
          <mt-button icon="back">{{ $t('hj227') }}</mt-button>
        </router-link>
      </mt-header>
    </div>
    <div class="form-block">
      <mt-field :label="$t('hj195')" :placeholder="$t('hj195')" type="text" disabled
                v-model="$store.state.userInfo.realName"></mt-field>
      <mt-field :label="$t('jy406')" :placeholder="$t('jy406')" type="text" disabled v-model="$store.state.userInfo.phone"></mt-field>
    </div>
    <div class="form-block">
      <mt-field :label="$t('hj10')" @click.native="changeLogin" autocomplete="new-password" :placeholder="$t('jy405')"
                type="password" disabled>
        <span @click="changeLogin"><i class="iconfont icon-xiugai"></i>{{ $t('hj144') }}</span>
      </mt-field>
    </div>
    <div class="btnbox">
      <span class="text-center btnok loginout" @click="toRegister">{{ $t('jy404') }}</span>
    </div>
    <!-- 修改密码 -->
    <mt-popup v-model="changeLoginPsdBox" position="bottom" class="mint-popup-wrap">
      <div class="clearfix">
        <a @click="changeLoginPsdBox = false" class="pull-right"><i class="iconfont icon-weitongguo"></i></a>
      </div>
      <div class="form-block">
        <mt-field label="原密码" type="password" placeholder="请输入原密码" v-model="nextPsd"></mt-field>
        <mt-field label="新密码" placeholder="请输入新密码(6-20位)" type="password" v-model="newPsd"></mt-field>
        <mt-field label="确认新密码" placeholder="请再次输入新密码" type="password" v-model="confirmPsd"></mt-field>
      </div>
      <div class="text-center">
        <mt-button class="btn-sure" type="default" @click="changeLoginPsd">确认修改</mt-button>
      </div>
    </mt-popup>
  </div>
</template>

<script>
import * as api from '@/axios/api'
import { Toast, MessageBox } from 'mint-ui'
import { isNull, pwdReg, getResponseMsg } from '@/utils/utils'

export default {
  components: {},
  props: {},
  data () {
    return {
      username: '',
      changeLoginPsdBox: false,
      nextPsd: '',
      newPsd: '',
      confirmPsd: ''
    }
  },
  watch: {},
  computed: {},
  created () {},
  mounted () {},
  methods: {
    toRegister () {
      // 使用 MessageBox 确认
      MessageBox.confirm('确定要注销登录吗？', '确认注销').then(async () => {
        // 用户确认注销
        // 显示加载状态
        const loading = Toast.loading({
          message: '注销中...',
          forbidClick: true,
          duration: 0
        })

        try {
          // 先清除本地数据
          this.clearCookie()

          // 清除用户状态
          if (this.$store && this.$store.state) {
            this.$store.state.userInfo = {}
          }

          // 调用注销接口
          const response = await api.logout()

          if (response && response.code === 200) {
            Toast(getResponseMsg(response, '注销成功'))
          } else {
            // 即使接口失败也已经清除本地数据
            Toast('已退出登录')
          }

          // 跳转到登录页
          setTimeout(() => {
            this.$router.replace('/futu-login')
          }, 500)
        } catch (error) {
          // 即使出错也确保清除本地数据
          this.clearCookie()
          if (this.$store && this.$store.state) {
            this.$store.state.userInfo = {}
          }

          Toast('已退出登录')

          // 跳转到登录页
          setTimeout(() => {
            this.$router.replace('/futu-login')
          }, 500)
        } finally {
          Toast.clear()
        }
      }).catch(() => {
        // 用户取消注销
      })
    },
    changeLogin () {
      this.changeLoginPsdBox = true
    },

    // 清除本地存储的用户信息
    clearCookie () {
      try {
        localStorage.removeItem('USERTOKEN')
        localStorage.removeItem('userInfo')
        localStorage.removeItem('token')
        // 清除其他可能的用户相关数据
        localStorage.removeItem('userId')
        localStorage.removeItem('userName')
      } catch (error) {
      }
    },
    async changeLoginPsd () {
      try {
        // 表单验证
        if (isNull(this.nextPsd)) {
          Toast('请输入原密码')
          return
        }

        if (isNull(this.newPsd)) {
          Toast('请输入新密码')
          return
        }

        if (isNull(this.confirmPsd)) {
          Toast('请确认新密码')
          return
        }

        if (this.newPsd !== this.confirmPsd) {
          Toast('两次输入的新密码不一致')
          return
        }

        if (this.nextPsd === this.newPsd) {
          Toast('新密码不能与原密码相同')
          return
        }

        if (!pwdReg(this.newPsd)) {
          Toast('新密码格式不正确，请输入6-20位字母数字组合')
          return
        }

        if (this.newPsd.length < 6) {
          Toast('新密码长度不能少于6位')
          return
        }

        // 显示加载状态
        const loading = this.$toast.loading({
          message: '修改中...',
          forbidClick: true,
          duration: 0
        })

        try {
          // 调用修改密码接口
          const opts = {
            oldPwd: this.nextPsd,
            newPwd: this.newPsd
          }

          const response = await api.changePassword(opts)

          if (response && response.code === 200) {
            // 修改成功
            this.changeLoginPsdBox = false
            this.nextPsd = ''
            this.newPsd = ''
            this.confirmPsd = ''

            const successMsg = getResponseMsg(response, '密码修改成功')
            Toast(successMsg)

            // 提示用户重新登录
            setTimeout(() => {
              this.$confirm('密码已修改成功，请重新登录', '提示').then(() => {
                // 清除登录状态
                this.clearCookie()
                if (this.$store.state.userInfo) {
                  this.$store.state.userInfo = {}
                }
                this.$router.push('/login')
              }).catch(() => {
                // 用户选择不重新登录，保持当前状态
              })
            }, 1000)
          } else {
            // 修改失败
            const errorMsg = getResponseMsg(response, '密码修改失败，请检查原密码是否正确')
            Toast(errorMsg)
          }
        } catch (error) {
          const errorMsg = getResponseMsg(error.response, '网络错误，请检查网络连接')
          Toast(errorMsg)
        } finally {
          Toast.clear()
        }
      } catch (error) {
        Toast('修改过程出错，请重试')
      }
    }
  }
}
</script>
<style lang="less" scoped>
  .loginout {
    color: #999;
    border: 0.015rem solid #606060;
    font-size: 0.3rem;
    background: none;
  }

  .mint-popup-wrap {
    width: 100%;
    padding: 0.3rem 0.3rem 0.6rem;

    .btn-sure {
      margin-top: 0.5rem;
      width: 80%;
      color: #fff;
      border: none;
    }
  }

  .btnbox .btnok {
    background: none;
  }
</style>
