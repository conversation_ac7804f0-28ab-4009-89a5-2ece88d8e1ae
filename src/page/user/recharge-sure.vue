<template>
  <div class="wrapper">
    <!-- <div class="header">
      <mt-header title="充值说明">
        <router-link to="/recharge" slot="left">
          <mt-button icon="back">充值</mt-button>
        </router-link>
      </mt-header>
    </div> -->
    <div class="box1">
      <div class="form-block page-part" style="color: #fff;">
        <mt-field :label="$t('hj170')" :placeholder="$t('hj170')" disabled type="number" v-model="selectNumber"></mt-field>
        <div>
          <mt-field :label="$t('hj163')" :placeholder="$t('hj163')" disabled type="text" v-model="payDetail.channelName">
            <span v-clipboard:copy="payDetail.channelName"
                          v-clipboard:success="onCopy"
                          v-clipboard:error="onError">
                        <i class="iconfont icon-fuzhi"></i> {{ $t('hj164') }}
                    </span>
          </mt-field>
          <mt-field :label="$t('hj166')" :placeholder="$t('hj166')" disabled type="text" v-model="payDetail.channelDesc">
                    <span v-clipboard:copy="payDetail.channelDesc"
                          v-clipboard:success="onCopy"
                          v-clipboard:error="onError">
                        <i class="iconfont icon-fuzhi"></i> {{ $t('hj164') }}
                    </span>
          </mt-field>
          <mt-field :label="$t('hj167')" :placeholder="$t('hj167')" disabled type="text" v-model="payDetail.channelAccount">
                    <span v-clipboard:copy="payDetail.channelAccount"
                          v-clipboard:success="onCopy"
                          v-clipboard:error="onError">
                        <i class="iconfont icon-fuzhi"></i> {{ $t('hj164') }}
                    </span>
          </mt-field>
        </div>

        <p style="margin-top: 50px"></p>
        <p class="czl"> {{ $t('jy608') }}</p>
        <p class="czl"> {{ $t('jy609') }}</p>
        <p class="czl"> {{ $t('jy610') }}</p>
        <p style="margin-top: 50px"></p>
        <p class="zhuyi"> {{ $t('jy611') }}</p>
        <p class="zhuyi"> {{ $t('jy612') }}</p>

        <!-- <div v-if="type == 0">
          <mt-field :label="$t('jy419')" :placeholder="$t('jy419')" disabled type="text" v-model="payInfo.channelName">
          </mt-field>
          <mt-field :label="$t('jy420')" :placeholder="$t('jy420')" disabled type="text" v-model="payInfo.channelAccount">
                    <span v-clipboard:copy="payInfo.channelAccount"
                          v-clipboard:success="onCopy"
                          v-clipboard:error="onError">
                        <i class="iconfont icon-fuzhi"></i> {{ $t('hj164') }}
                    </span>
          </mt-field>
          <div class="text-right" style="padding: 0.2rem 0.3rem 0;" @click="popupVisible2 = true"> {{ $t('jy421') }}</div>
        </div>
        <div v-else-if="type == 1">
          <mt-field :label="$t('hj163')" :placeholder="$t('hj163')" disabled type="text" v-model="payInfo.channelName">
                    <span v-clipboard:copy="payInfo.channelName"
                          v-clipboard:success="onCopy"
                          v-clipboard:error="onError">
                        <i class="iconfont icon-fuzhi"></i> {{ $t('hj164') }}
                    </span>
          </mt-field>
          <mt-field :label="$t('hj166')" :placeholder="$t('hj166')" disabled type="text" v-model="payInfo.channelDesc">
                    <span v-clipboard:copy="payInfo.channelDesc"
                          v-clipboard:success="onCopy"
                          v-clipboard:error="onError">
                        <i class="iconfont icon-fuzhi"></i> {{ $t('hj164') }}
                    </span>
          </mt-field>
          <mt-field label="收款支行" placeholder="收款支行" disabled type="text" v-model="payInfo.payBankAddress">
              <span v-clipboard:copy="info.payBankAddress"
                  v-clipboard:success="onCopy"
                  v-clipboard:error="onError">
                  <i class="iconfont icon-fuzhi"></i>复制
              </span>
          </mt-field>
          <mt-field :label="$t('hj167')" :placeholder="$t('hj167')" disabled type="text" v-model="payInfo.channelAccount">
                    <span v-clipboard:copy="payInfo.channelAccount"
                          v-clipboard:success="onCopy"
                          v-clipboard:error="onError">
                        <i class="iconfont icon-fuzhi"></i> {{ $t('hj164') }}
                    </span>
          </mt-field>
        </div>
        <div v-else>
          <mt-field :label="$t('jy422')" :placeholder="$t('jy422')" disabled type="text" v-model="payInfo.channelName">
          </mt-field>
          <mt-field :label="$t('hj167')" :placeholder="$t('hj167')" disabled type="text" v-model="payInfo.channelAccount">
                    <span v-clipboard:copy="payInfo.channelAccount"
                          v-clipboard:success="onCopy"
                          v-clipboard:error="onError">
                        <i class="iconfont icon-fuzhi"></i> {{ $t('hj164') }}
                    </span>
          </mt-field>
          <mt-field v-if="payInfo.channelDesc" :label="$t('jy423')" :placeholder="$t('jy423')" disabled type="text"
                    v-model="payInfo.channelDesc">
                    <span v-clipboard:copy="payInfo.channelDesc"
                          v-clipboard:success="onCopy"
                          v-clipboard:error="onError">
                        <i class="iconfont icon-fuzhi"></i> {{ $t('hj164') }}
                    </span>
          </mt-field>
          <div v-if="payInfo.channelImg" class="eq-code-img">
            <img :src="payInfo.channelImg" alt="">
          </div>
        </div> -->
      </div>
      <!-- <div v-if="type == 0">
        <div class="tips-group">
          <p><i class="iconfont icon-jinggao1"></i> {{ $t('jy45') }}：</p>
          <p class="tip-text"><i class="iconfont icon-jingpaibuzhou"></i> {{ $t('jy424') }}<span class="red"> {{ $t('jy425') }}</span>
          </p>
          <p class="tip-text"><i class="iconfont icon-jingpaibuzhou3"></i> {{ $t('jy426') }}<span
            class="red"> {{ $t('jy427') }}</span></p>
          <p class="tip-text"><i class="iconfont icon-jingpaibuzhou2"></i> {{ $t('jy428') }}<span class="red"> {{ $t('jy429') }}</span>.
          </p>
          <p class="tip-text"><i class="iconfont icon-jingpaibuzhou1"></i> {{ $t('jy430') }}</p>
        </div>
        <div v-if="payInfo.channelImg" class="btnbox">
          <span class="text-center btnok" @click="toSure"> {{ $t('jy431') }}</span>
        </div>
        <div v-if="false" class="tips-group">
          <p><i class="iconfont icon-liucheng"></i> {{ $t('jy432') }}：</p>
          <p class="tip-text"><i class="iconfont icon-buzhou"></i> {{ $t('jy433') }}</p>
          <p class="tip-text"><i class="iconfont icon-buzhou2"></i> {{ $t('jy434') }}</p>
        </div>
      </div>
      <div v-else-if="type == 1">
        <div class="tips-group">
          <p><i class="iconfont icon-liucheng"></i> {{ $t('hj173') }}：</p>
          <p class="tip-text"><i class="iconfont icon-buzhou"></i> {{ $t('hj174') }}</p>
          <p class="tip-text"><i class="iconfont icon-buzhou2"></i> {{ $t('hj175') }}</p>
        </div>
        <div class="tips-group red">
          <p><i class="iconfont icon-jinggao1"></i> {{ $t('jy45') }}：</p>
          <p class="tip-text"><i class="iconfont icon-jingpaibuzhou"></i> {{ $t('hj176') }}</p>
        </div>
      </div>
      <div v-else>

      </div>-->

    </div>

    <!-- 倒计时弹框 -->
    <!-- <mt-popup v-model="popupVisible" pop-transition='popup-fade' :closeOnClickModal="false" class="mint-popup-white">
      <div class="clearfix">
        <a @click="closePopup" class="pull-right"><i class="iconfont icon-weitongguo"></i></a>
      </div>
      <div class="box-block">
        <p class="text-center">
          <i v-if="type == 0" class="iconfont icon-umidd17"></i>
          <i v-if="type == 1" class="iconfont icon-02"></i>
        </p>
        <div class="prompt-box text-center">
          {{ $t('jy435') }}
        </div>
        <p class="text-center money">
          ¥<span class="number">{{selectNumber}}</span>
        </p> -->
        <!-- <div class="qrCode"> -->
          <!-- <img src="../../assets/img/timg.png" alt="二维码"> -->
          <!-- <img :src="payInfo.channelImg" :alt="$t('jy436')">
          <div v-if="stopTime" class="alert-box">
            <i class="iconfont icon-jinggao2"></i>
             {{ $t('jy437') }}
          </div>
        </div>
        <div class="timer-box">
          {{time.minutes}}:{{time.seconds}}
        </div>
        <div class="scan">
          <span v-if="type == 0"> {{ $t('jy438') }}</span>
          <span v-if="type == 1"> {{ $t('jy439') }}</span>
        </div>
      </div>
    </mt-popup> -->
    <!-- <mt-popup v-model="popupVisible2" pop-transition='popup-fade' :closeOnClickModal="false"
              class="mint-popup-white mint-popup-white1">
      <div class="clearfix">
        <a @click="popupVisible2 = false" class="pull-right"><i class="iconfont icon-weitongguo"></i></a>
      </div>
      <img width="100%" src="../../assets/img/xiane.png" alt="">
    </mt-popup> -->
  </div>
</template>

<script>
import * as api from '@/axios/api'
import { Toast } from 'mint-ui'

export default {
  components: {},
  props: {},
  data () {
    return {
      type: '0', // 0 支付宝扫码 1 对公转账
      selectNumber: 500,
      popupVisible: false, // 二维码倒计时
      minutes: 5,
      seconds: 0,
      time: {
        minutes: 5,
        seconds: '00'
      },
      stopTime: false, // 倒计时结束提示
      timer: null, // 定时器
      info: {
        account: this.$t('hj167'),
        payName: this.$t('hj163'),
        payBank: this.$t('hj166'),
        payBankAddress: this.$t('jy440'),
        zhifubaoAccount: '<EMAIL>',
        zhifubaoName: this.$t('jy441')
      },
      popupVisible2: false, // 限额说明
      payInfo: {
        channelImg: ''
      }, // 支付渠道的相关信息

      payDetail: {}
    }
  },
  computed: {},
  created () {},
  mounted () {
    // this.type = this.$route.query.type
    // this.selectNumber = this.$route.query.selectNumber

    this.payDetail = this.$route.params.info
    this.selectNumber = this.$route.params.total

    // this.getPayInfo()
  },
  beforeDestroy () {
    window.clearInterval(this.timer)
  },
  watch: {},
  methods: {
    async getPayInfo () {
      // 获取支付渠道 详细信息
      let data = await api.getPayInfoDetail({ payId: this.$route.query.id })
      if (data.code === 200) {
        this.payInfo = data.data
      } else {
        Toast(data.msg)
      }
    },
    onCopy: function (e) {
      Toast(this.$t('hj185'))
    },
    onError: function (e) {
      Toast(this.$t('hj186'))
    },
    toSure () {
      // 充值
      if (this.selectNumber > this.payInfo.channelMaxLimit || this.selectNumber < this.payInfo.channelMinLimit) {
        Toast(this.$t('jy442') + this.payInfo.channelMaxLimit + this.$t('jy443') + this.payInfo.channelMinLimit)
      } else {
        this.popupVisible = true
        this.minutes = 5
        this.seconds = 0
        this.time.minutes = 5
        this.time.seconds = '00'
        this.stopTime = false
        this.timerInterval()
        // this.recharge()
      }
    },
    async recharge () {
      let opts = {
        amt: this.selectNumber,
        payType: this.type
      }
      let data = await api.inMoney(opts)
      if (data.code === 200) {
        // 成功
        Toast(data.msg ? data.msg : this.$t('hj231'))
      } else {
        Toast(data.msg ? data.msg : this.$t('jy444'))
      }
    },
    closePopup () {
      // 关闭弹窗
      this.popupVisible = false
      this.$router.push('/recharge')
      window.clearInterval(this.timer)
    },
    goBack () {
      this.$router.back(-1)
    },
    num (n) {
      return n < 10 ? '0' + n : '' + n
    },
    timerInterval () {
      var _this = this
      _this.timer = window.setInterval(function () {
        if (_this.seconds === 0 && _this.minutes !== 0) {
          _this.seconds = 59
          _this.minutes -= 1
        } else if (_this.minutes === 0 && _this.seconds === 0) {
          // 倒计时结束
          _this.seconds = 0
          _this.stopTime = true
          this.$router.push('/recharge')
          window.clearInterval(_this.timer)
        } else {
          _this.seconds -= 1
        }
        _this.time.minutes = _this.num(_this.minutes)
        _this.time.seconds = _this.num(_this.seconds)
      }, 1000)
    }
  }
}
</script>
<style lang="less" scoped>
  .pay-img {
    padding: 0 0.2rem;
    padding-top: 0.417rem;

    img {
      width: 100%;
    }
  }

  .czl{
    font-size: large;
    margin: 30px 0 0 50px;
    color: #ddd;
  }

  .zhuyi{
    font-size: large;
    margin: 0 0 0 50px;
    color: red;
    line-height: 50px;
  }

  .pay-radio {
    /* padding: 0.2rem 0.1rem; */

    margin-bottom: 0.2rem;
    height: 0.8rem;
    line-height: 0.75rem;

    .pay-icon {
      .iconfont {
        margin-right: 0.2rem;
      }
    }

    .pay-list {
      border: 0.02rem solid #4e4d4d;
      border-radius: 0.2rem;
    }

    .pay-weixin {
      border-color: #36ae55;
    }

    .pay-zhifubao {
      // border-color:#1296db;
    }

    .icon-on {
      color: #b60c0d;
    }

  }

  .btn-default {
    border: 0.02rem solid #4e4d4d;
    border-radius: 0.2rem;
    display: inline-block;
    height: 0.8rem;
    width: 100%;
    text-indent: 0.2rem;
    background: none;
    color: #ddd;
  }

  .tips-group {
    padding: 0.2rem 0.417rem;

    .iconfont {
      margin-right: 0.2rem;
    }

    p {
      line-height: 2;
      font-size: 0.25rem;
    }

    .tip-text {
      text-indent: 0.28rem;
    }
  }

  .radio-group li {
    width: 19%;
    margin-right: 1%;
  }

  .mint-popup-white {
    color: #333;
    width: 80%;
    padding: 0.2rem 0.3rem 0;
    // bottom: 30%;
    border-radius: 0.1rem;
    box-shadow: 0.014rem 0.014rem 0.014rem rgba(103, 107, 111, 0.38);

    .popup-silide-bottom-leave-active {
      // bottom: -10%;
    }

    .box-block {
      .qrCode {
        border: 1px solid #f3f3f3;
        padding: 0.1rem;
        height: 3rem;
        width: 3rem;
        margin: 0.3rem auto;
        position: relative;

        img {
          width: 100%;
          height: 100%;
        }

        .alert-box {
          width: 100%;
          height: 100%;
          background: rgba(255, 255, 255, 0.9);
          position: absolute;
          left: 0;
          top: 0;
          color: #333;
          text-align: center;

          .iconfont {
            color: #f98700;
            font-size: 0.6rem;
            display: block;
            margin-top: 0.8rem;
            margin-bottom: 0.4rem;
          }
        }
      }

      .prompt-box {
        padding: 0.2rem;
        margin: 0.2rem 0;
        color: #666;
      }

      .money {
        font-weight: bold;
        font-size: 0.5rem;

        .number {
          margin-left: 0.1rem;
        }
      }

      .timer-box {
        text-align: center;
        font-size: 0.5rem;
      }

      .number {
        font-size: 0.6rem;
      }
    }

    .scan {
      border-top: 0.02rem dashed #ddd;
      margin-top: 0.5rem;
      padding: 0.3rem;
      text-align: center;
      color: #ff1100;
    }

    // 微信支付宝icon设置
    .icon-umidd17 {
      color: #1296db;
      font-size: 0.6rem;
    }

    .icon-02 {
      color: #36ae55;
      font-size: 0.6rem;
    }
  }

  .mint-popup-white1 {
    background: #eaeaea;
    padding-bottom: 0.3rem;
  }

  .box1 {
    // margin-top: 0.4rem;
    .icon-fuzhi {
      font-size: 0.24rem;
      margin-right: 0.1rem;
    }
  }

  .eq-code-img {
    text-align: center;
    margin-top: 0.2rem;

    img {
      width: 60%
    }
  }
</style>
