<template>
  <div class="wrapper">
    <div class="header">
      <mt-header :title="$t('jy447')">
        <router-link to="/user" slot="left">
          <mt-button icon="back">{{ $t('hj227') }}</mt-button>
        </router-link>
      </mt-header>
    </div>
    <mt-navbar v-model="selected">
      <mt-tab-item v-if="this.$store.state.settingForm.indexDisplay" id="1">{{ $t('hj188') }}</mt-tab-item>
      <mt-tab-item v-if="this.$store.state.settingForm.indexDisplay" id="2">{{ $t('hj189') }}</mt-tab-item>
      <mt-tab-item v-if="this.$store.state.settingForm.futuresDisplay" id="3">{{ $t('jy446') }}</mt-tab-item>
      <mt-tab-item v-if="this.$store.state.settingForm.futuresDisplay" id="4">{{ $t('jy445') }}</mt-tab-item>
    </mt-navbar>
    <mt-tab-container class="order-list" v-model="selected">
      <mt-tab-container-item id="1">
        <div class="form-block">
          <mt-field :label="$t('hj190')" :placeholder="$t('hj190')" type="text" disabled v-model="this.$store.state.userInfo.enableAmt">
          </mt-field>
        </div>
        <div class="form-block">
          <mt-field :label="$t('hj191')" name="amt" v-model="form.account1" :placeholder="$t('hj192')" type="text">
            <span @click="selectAll1">{{ $t('hj160') }}</span>
          </mt-field>
        </div>
        <!-- <div class="form-block">
            <mt-field label="资金密码" placeholder="资金密码" type="password" v-model="form.password"></mt-field>
        </div>
        <p class="prompt">资金密码默认为登录密码</p> -->
        <div class="btnbox">
          <span class="text-center btnok loginout" @click="tosubmit">{{ $t('hj193') }}</span>
        </div>
      </mt-tab-container-item>
      <mt-tab-container-item id="2">
        <div class="form-block">
          <mt-field :label="$t('hj190')" :placeholder="$t('hj190')" type="text" disabled
            v-model="this.$store.state.userInfo.enableIndexAmt"></mt-field>
        </div>
        <div class="form-block">
          <mt-field :label="$t('hj191')" v-model="form.account2" :placeholder="$t('hj192')" type="text">
            <span @click="selectAll2">{{ $t('hj160') }}</span>
          </mt-field>
        </div>
        <div class="btnbox">
          <span class="text-center btnok loginout" @click="tosubmit">{{ $t('hj194') }}</span>
        </div>
      </mt-tab-container-item>
      <mt-tab-container-item id="3">
        <div class="form-block">
          <mt-field :label="$t('hj190')" :placeholder="$t('hj190')" type="text" disabled v-model="this.$store.state.userInfo.enableAmt">
          </mt-field>
        </div>
        <div class="form-block">
          <mt-field :label="$t('hj191')" v-model="form.account3" :placeholder="$t('hj192')" type="text">
            <span @click="selectAll3">{{ $t('hj160') }}</span>
          </mt-field>
        </div>
        <div class="btnbox">
          <span class="text-center btnok loginout" @click="tosubmit">{{ $t('jy368') }}</span>
        </div>
      </mt-tab-container-item>
      <mt-tab-container-item id="4">
        <div class="form-block">
          <mt-field :label="$t('hj190')" :placeholder="$t('hj190')" type="text" disabled
            v-model="this.$store.state.userInfo.enableFuturesAmt"></mt-field>
        </div>
        <div class="form-block">
          <mt-field :label="$t('hj191')" v-model="form.account4" :placeholder="$t('hj192')" type="text">
            <span @click="selectAll4">{{ $t('hj160') }}</span>
          </mt-field>
        </div>
        <div class="btnbox">
          <span class="text-center btnok loginout" @click="tosubmit">{{ $t('hj194') }}</span>
        </div>
      </mt-tab-container-item>
    </mt-tab-container>
  </div>
</template>

<script>
// import '@/assets/style/common.less'
import * as api from '@/axios/api'
import { getResponseMsg } from '@/utils/utils'
import { Toast } from 'mint-ui'

export default {
  components: {
  },
  data () {
    return {
      selected: '1', // 选中
      form: {
        account1: '',
        account2: '',
        account3: '',
        account4: '',
        password: ''
      },
      userInfo: {
        realName: ''
      }
    }
  },
  watch: {},
  computed: {},
  created () {
    this.getProductSetting()
  },
  mounted () {
    if (this.$route.query.type) {
      this.selected = this.$route.query.type + ''
    }
    this.getUserInfo()
  },
  methods: {
    async getProductSetting () {
      try {
        const response = await api.getProductSetting()

        if (response && response.code === 200 && response.data) {
          this.$store.state.settingForm = response.data
          if (!this.$store.state.settingForm.indexDisplay) {
            this.selected = '3'
          }
        } else {
          // 使用默认配置
          const defaultSetting = {
            futuresDisplay: true,
            indexDisplay: true,
            kcStockDisplay: false,
            stockDisplay: true
          }
          this.$store.state.settingForm = defaultSetting
          if (!defaultSetting.indexDisplay) {
            this.selected = '3'
          }
        }
      } catch (error) {
        // 网络错误时使用默认配置
        const defaultSetting = {
          futuresDisplay: true,
          indexDisplay: true,
          kcStockDisplay: false,
          stockDisplay: true
        }
        this.$store.state.settingForm = defaultSetting
      }
    },
    selectAll1 () {
      // 选择全部
      this.form.account1 = this.$store.state.userInfo.enableAmt
    },
    selectAll2 () {
      // 选择全部
      this.form.account2 = this.$store.state.userInfo.enableIndexAmt
    },
    selectAll3 () {
      // 选择全部
      this.form.account3 = this.$store.state.userInfo.enableAmt
    },
    selectAll4 () {
      // 选择全部
      this.form.account4 = this.$store.state.userInfo.enableFuturesAmt
    },
    async tosubmit () {
      try {
        // 获取转账金额
        const amt = this.selected === '1' ? this.form.account1
          : this.selected === '2' ? this.form.account2
            : this.selected === '3' ? this.form.account3
              : this.form.account4

        // 表单验证
        if (!amt || amt <= 0) {
          Toast('请输入正确的转账金额')
          return
        }

        // 检查余额
        const availableAmt = this.selected === '1' ? this.$store.state.userInfo.enableAmt
          : this.selected === '2' ? this.$store.state.userInfo.enableIndexAmt
            : this.selected === '3' ? this.$store.state.userInfo.enableAmt
              : this.$store.state.userInfo.enableFuturesAmt

        if (parseFloat(amt) > parseFloat(availableAmt)) {
          Toast('转账金额不能超过可用余额')
          return
        }

        // 显示加载状态
        const loading = this.$toast.loading({
          message: '转账处理中...',
          forbidClick: true,
          duration: 0
        })

        try {
          let opt = {
            amt: amt,
            type: this.selected // 1 融资转指数 2 指数转融资 3 融资转期货 4 期货转融资
          }

          const response = await api.AmtChange(opt)

          if (response && response.code === 200) {
            const successMsg = getResponseMsg(response, '资金转账成功')
            Toast(successMsg)
            // 刷新用户信息
            this.getUserInfo()
            this.$router.push('/user')
          } else {
            const errorMsg = getResponseMsg(response, '资金转账失败，请重试')
            Toast(errorMsg)
          }
        } catch (error) {
          const errorMsg = getResponseMsg(error.response, '网络错误，请检查网络连接')
          Toast(errorMsg)
        } finally {
          loading.clear()
        }
      } catch (error) {
        Toast('转账过程出错，请重试')
      }
    },
    async getUserInfo () {
      // 获取用户信息
      let data = await api.getUserInfo()
      if (data.code === 200) {
        this.$store.state.userInfo = data.data
      } else {
        Toast(data.msg)
      }
    }
  }
}
</script>
<style lang="less" scoped>
.is-selected .mint-tab-item-label:hover {
  text-decoration: none;
}

.wrapper /deep/ .mint-tab-item-label {
  font-size: 0.264rem;
}

.mint-navbar .mint-tab-item.is-selected {
  // color: #d50000;
  // border-bottom: 2px solid #d50000;
  text-decoration: none;
  margin-bottom: 0;
}

.prompt {
  padding: 0.3rem 0 0.2rem 0.7rem;
}
</style>
