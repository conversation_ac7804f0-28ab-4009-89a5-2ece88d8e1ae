<template>
  <div class="api-test">
    <div class="test-header">
      <h1>Polygon API Interface Test Page</h1>
      <p>Test all Polygon API interfaces and save return information</p>
      <div class="test-controls">
        <button @click="testAllApis" :disabled="isTestingAll" class="test-all-btn">
          {{ isTestingAll ? 'Testing all interfaces...' : 'Test All Interfaces' }}
        </button>
        <button @click="saveAllResults" :disabled="Object.keys(results).length === 0" class="save-btn">
          Save All Results to File
        </button>
        <button @click="clearAllResults" class="clear-btn">
          Clear All Results
        </button>
      </div>
    </div>

    <div class="test-sections">
      <!-- TICKERS 接口测试 -->
      <div class="test-section">
        <h2>📊 TICKERS Interface</h2>
        <div class="test-buttons">
                      <button @click="testGetTickers" :disabled="loading.getTickers">
            {{ loading.getTickers ? 'Testing...' : 'Test getTickers' }}
          </button>
          <button @click="testGetTickerDetails" :disabled="loading.getTickerDetails">
            {{ loading.getTickerDetails ? 'Testing...' : 'Test getTickerDetails' }}
          </button>
          <button @click="testGetRelatedTickers" :disabled="loading.getRelatedTickers">
            {{ loading.getRelatedTickers ? 'Testing...' : 'Test getRelatedTickers' }}
          </button>
          <button @click="testGetTickerNews" :disabled="loading.getTickerNews">
            {{ loading.getTickerNews ? 'Testing...' : 'Test getTickerNews' }}
          </button>
          <button @click="testGetTickerTypes" :disabled="loading.getTickerTypes">
            {{ loading.getTickerTypes ? 'Testing...' : 'Test getTickerTypes' }}
          </button>
        </div>
        <div class="test-results" v-if="results.getTickers">
          <h3>getTickers Results:</h3>
          <pre>{{ JSON.stringify(results.getTickers, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getTickerDetails">
          <h3>getTickerDetails Results:</h3>
          <pre>{{ JSON.stringify(results.getTickerDetails, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getRelatedTickers">
          <h3>getRelatedTickers Results:</h3>
          <pre>{{ JSON.stringify(results.getRelatedTickers, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getTickerNews">
          <h3>getTickerNews Results:</h3>
          <pre>{{ JSON.stringify(results.getTickerNews, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getTickerTypes">
          <h3>getTickerTypes Results:</h3>
          <pre>{{ JSON.stringify(results.getTickerTypes, null, 2) }}</pre>
        </div>
      </div>

      <!-- AGGREGATES 接口测试 -->
      <div class="test-section">
        <h2>📈 AGGREGATES (K-line) Interface</h2>
        <div class="test-buttons">
          <button @click="testGetAggregates" :disabled="loading.getAggregates">
            {{ loading.getAggregates ? '测试中...' : '测试 getAggregates' }}
          </button>
          <button @click="testGetGroupedDaily" :disabled="loading.getGroupedDaily">
            {{ loading.getGroupedDaily ? '测试中...' : '测试 getGroupedDaily' }}
          </button>
          <button @click="testGetDailyOpenClose" :disabled="loading.getDailyOpenClose">
            {{ loading.getDailyOpenClose ? '测试中...' : '测试 getDailyOpenClose' }}
          </button>
          <button @click="testGetPreviousClose" :disabled="loading.getPreviousClose">
            {{ loading.getPreviousClose ? '测试中...' : '测试 getPreviousClose' }}
          </button>
        </div>
        <div class="test-results" v-if="results.getAggregates">
          <h3>getAggregates 结果:</h3>
          <pre>{{ JSON.stringify(results.getAggregates, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getGroupedDaily">
          <h3>getGroupedDaily 结果:</h3>
          <pre>{{ JSON.stringify(results.getGroupedDaily, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getDailyOpenClose">
          <h3>getDailyOpenClose 结果:</h3>
          <pre>{{ JSON.stringify(results.getDailyOpenClose, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getPreviousClose">
          <h3>getPreviousClose 结果:</h3>
          <pre>{{ JSON.stringify(results.getPreviousClose, null, 2) }}</pre>
        </div>
      </div>

      <!-- TRADES & QUOTES 接口测试 -->
      <div class="test-section">
        <h2>💹 TRADES & QUOTES 接口</h2>
        <div class="test-buttons">
          <button @click="testGetTrades" :disabled="loading.getTrades">
            {{ loading.getTrades ? '测试中...' : '测试 getTrades' }}
          </button>
          <button @click="testGetQuotes" :disabled="loading.getQuotes">
            {{ loading.getQuotes ? '测试中...' : '测试 getQuotes' }}
          </button>
          <button @click="testGetLastTrade" :disabled="loading.getLastTrade">
            {{ loading.getLastTrade ? '测试中...' : '测试 getLastTrade' }}
          </button>
          <button @click="testGetLastQuote" :disabled="loading.getLastQuote">
            {{ loading.getLastQuote ? '测试中...' : '测试 getLastQuote' }}
          </button>
        </div>
        <div class="test-results" v-if="results.getTrades">
          <h3>getTrades 结果:</h3>
          <pre>{{ JSON.stringify(results.getTrades, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getQuotes">
          <h3>getQuotes 结果:</h3>
          <pre>{{ JSON.stringify(results.getQuotes, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getLastTrade">
          <h3>getLastTrade 结果:</h3>
          <pre>{{ JSON.stringify(results.getLastTrade, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getLastQuote">
          <h3>getLastQuote 结果:</h3>
          <pre>{{ JSON.stringify(results.getLastQuote, null, 2) }}</pre>
        </div>
      </div>

      <!-- SNAPSHOTS 接口测试 -->
      <div class="test-section">
        <h2>📸 SNAPSHOTS 接口</h2>
        <div class="test-buttons">
          <button @click="testGetAllTickersSnapshot" :disabled="loading.getAllTickersSnapshot">
            {{ loading.getAllTickersSnapshot ? '测试中...' : '测试 getAllTickersSnapshot' }}
          </button>
          <button @click="testGetTickerSnapshot" :disabled="loading.getTickerSnapshot">
            {{ loading.getTickerSnapshot ? '测试中...' : '测试 getTickerSnapshot' }}
          </button>
          <button @click="testGetGainersSnapshot" :disabled="loading.getGainersSnapshot">
            {{ loading.getGainersSnapshot ? '测试中...' : '测试 getGainersSnapshot' }}
          </button>
          <button @click="testGetLosersSnapshot" :disabled="loading.getLosersSnapshot">
            {{ loading.getLosersSnapshot ? '测试中...' : '测试 getLosersSnapshot' }}
          </button>
        </div>
        <div class="test-results" v-if="results.getAllTickersSnapshot">
          <h3>getAllTickersSnapshot 结果:</h3>
          <pre>{{ JSON.stringify(results.getAllTickersSnapshot, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getTickerSnapshot">
          <h3>getTickerSnapshot 结果:</h3>
          <pre>{{ JSON.stringify(results.getTickerSnapshot, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getGainersSnapshot">
          <h3>getGainersSnapshot 结果:</h3>
          <pre>{{ JSON.stringify(results.getGainersSnapshot, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getLosersSnapshot">
          <h3>getLosersSnapshot 结果:</h3>
          <pre>{{ JSON.stringify(results.getLosersSnapshot, null, 2) }}</pre>
        </div>
      </div>

      <!-- REFERENCE DATA 接口测试 -->
      <div class="test-section">
        <h2>📚 REFERENCE DATA 接口</h2>
        <div class="test-buttons">
          <button @click="testGetExchanges" :disabled="loading.getExchanges">
            {{ loading.getExchanges ? '测试中...' : '测试 getExchanges' }}
          </button>
          <button @click="testGetMarketStatus" :disabled="loading.getMarketStatus">
            {{ loading.getMarketStatus ? '测试中...' : '测试 getMarketStatus' }}
          </button>
          <button @click="testGetMarketHolidays" :disabled="loading.getMarketHolidays">
            {{ loading.getMarketHolidays ? '测试中...' : '测试 getMarketHolidays' }}
          </button>
          <button @click="testGetStockSplits" :disabled="loading.getStockSplits">
            {{ loading.getStockSplits ? '测试中...' : '测试 getStockSplits' }}
          </button>
          <button @click="testGetDividends" :disabled="loading.getDividends">
            {{ loading.getDividends ? '测试中...' : '测试 getDividends' }}
          </button>
          <button @click="testGetConditions" :disabled="loading.getConditions">
            {{ loading.getConditions ? '测试中...' : '测试 getConditions' }}
          </button>
        </div>
        <div class="test-results" v-if="results.getExchanges">
          <h3>getExchanges 结果:</h3>
          <pre>{{ JSON.stringify(results.getExchanges, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getMarketStatus">
          <h3>getMarketStatus 结果:</h3>
          <pre>{{ JSON.stringify(results.getMarketStatus, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getMarketHolidays">
          <h3>getMarketHolidays 结果:</h3>
          <pre>{{ JSON.stringify(results.getMarketHolidays, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getStockSplits">
          <h3>getStockSplits 结果:</h3>
          <pre>{{ JSON.stringify(results.getStockSplits, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getDividends">
          <h3>getDividends 结果:</h3>
          <pre>{{ JSON.stringify(results.getDividends, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getConditions">
          <h3>getConditions 结果:</h3>
          <pre>{{ JSON.stringify(results.getConditions, null, 2) }}</pre>
        </div>
      </div>

      <!-- FUNDAMENTALS 接口测试 -->
      <div class="test-section">
        <h2>📊 FUNDAMENTALS 接口</h2>
        <div class="test-buttons">
          <button @click="testGetStockFinancials" :disabled="loading.getStockFinancials">
            {{ loading.getStockFinancials ? '测试中...' : '测试 getStockFinancials' }}
          </button>
        </div>
        <div class="test-results" v-if="results.getStockFinancials">
          <h3>getStockFinancials 结果:</h3>
          <pre>{{ JSON.stringify(results.getStockFinancials, null, 2) }}</pre>
        </div>
      </div>

      <!-- WEBSOCKET 接口测试 -->
      <div class="test-section">
        <h2>🔌 WEBSOCKET 接口</h2>
        <div class="test-buttons">
          <button @click="testSubscribeRealTime" :disabled="loading.subscribeRealTime">
            {{ loading.subscribeRealTime ? '测试中...' : '测试 subscribeRealTime' }}
          </button>
          <button @click="testUnsubscribeRealTime" :disabled="loading.unsubscribeRealTime">
            {{ loading.unsubscribeRealTime ? '测试中...' : '测试 unsubscribeRealTime' }}
          </button>
          <button @click="testGetLatestPrice" :disabled="loading.getLatestPrice">
            {{ loading.getLatestPrice ? '测试中...' : '测试 getLatestPrice' }}
          </button>
          <button @click="testGetLatestPrices" :disabled="loading.getLatestPrices">
            {{ loading.getLatestPrices ? '测试中...' : '测试 getLatestPrices' }}
          </button>
          <button @click="testGetSubscriptionStatus" :disabled="loading.getSubscriptionStatus">
            {{ loading.getSubscriptionStatus ? '测试中...' : '测试 getSubscriptionStatus' }}
          </button>
          <button @click="testReconnectWebSocket" :disabled="loading.reconnectWebSocket">
            {{ loading.reconnectWebSocket ? '测试中...' : '测试 reconnectWebSocket' }}
          </button>
          <button @click="testDisconnectWebSocket" :disabled="loading.disconnectWebSocket">
            {{ loading.disconnectWebSocket ? '测试中...' : '测试 disconnectWebSocket' }}
          </button>
        </div>
        <div class="test-results" v-if="results.subscribeRealTime">
          <h3>subscribeRealTime 结果:</h3>
          <pre>{{ JSON.stringify(results.subscribeRealTime, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.unsubscribeRealTime">
          <h3>unsubscribeRealTime 结果:</h3>
          <pre>{{ JSON.stringify(results.unsubscribeRealTime, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getLatestPrice">
          <h3>getLatestPrice 结果:</h3>
          <pre>{{ JSON.stringify(results.getLatestPrice, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getLatestPrices">
          <h3>getLatestPrices 结果:</h3>
          <pre>{{ JSON.stringify(results.getLatestPrices, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.getSubscriptionStatus">
          <h3>getSubscriptionStatus 结果:</h3>
          <pre>{{ JSON.stringify(results.getSubscriptionStatus, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.reconnectWebSocket">
          <h3>reconnectWebSocket 结果:</h3>
          <pre>{{ JSON.stringify(results.reconnectWebSocket, null, 2) }}</pre>
        </div>
        <div class="test-results" v-if="results.disconnectWebSocket">
          <h3>disconnectWebSocket 结果:</h3>
          <pre>{{ JSON.stringify(results.disconnectWebSocket, null, 2) }}</pre>
        </div>
      </div>

      <!-- 测试日志 -->
      <div class="test-section">
        <h2>📝 测试日志</h2>
        <div class="test-logs">
          <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <button @click="clearLogs" class="clear-btn">清空日志</button>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from '@/axios'

export default {
  name: 'ApiTest',
  data () {
    return {
      isTestingAll: false,
      loading: {
        // TICKERS
        getTickers: false,
        getTickerDetails: false,
        getRelatedTickers: false,
        getTickerNews: false,
        getTickerTypes: false,
        // AGGREGATES
        getAggregates: false,
        getGroupedDaily: false,
        getDailyOpenClose: false,
        getPreviousClose: false,
        // TRADES & QUOTES
        getTrades: false,
        getQuotes: false,
        getLastTrade: false,
        getLastQuote: false,
        // SNAPSHOTS
        getAllTickersSnapshot: false,
        getTickerSnapshot: false,
        getGainersSnapshot: false,
        getLosersSnapshot: false,
        // REFERENCE DATA
        getExchanges: false,
        getMarketStatus: false,
        getMarketHolidays: false,
        getStockSplits: false,
        getDividends: false,
        getConditions: false,
        // FUNDAMENTALS
        getStockFinancials: false,
        // WEBSOCKET
        subscribeRealTime: false,
        unsubscribeRealTime: false,
        getLatestPrice: false,
        getLatestPrices: false,
        getSubscriptionStatus: false,
        reconnectWebSocket: false,
        disconnectWebSocket: false
      },
      results: {},
      logs: []
    }
  },
  methods: {
    // 添加日志
    addLog (message, type = 'info') {
      this.logs.unshift({
        time: new Date().toLocaleTimeString(),
        message,
        type
      })
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100)
      }
    },

    // 清空日志
    clearLogs () {
      this.logs = []
    },

    // 清空所有结果
    clearAllResults () {
      this.results = {}
      this.addLog('已清空所有测试结果')
    },

    // 保存所有结果到文件
    saveAllResults () {
      const dataStr = JSON.stringify(this.results, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `polygon-api-test-results-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      this.addLog('测试结果已保存到文件', 'success')
    },

    // 通用API调用方法
    async callApi (apiName, url, params = {}) {
      this.loading[apiName] = true
      this.addLog(`开始测试 ${apiName} 接口...`)

      try {
        const result = await get(url, params)
        this.results[apiName] = result
        this.addLog(`${apiName} 接口调用成功`, 'success')
        return result
      } catch (error) {
        this.addLog(`${apiName} 接口调用失败: ${error.message}`, 'error')
        throw error
      } finally {
        this.loading[apiName] = false
      }
    },

    // ======================== TICKERS 接口测试 ========================
    async testGetTickers () {
      await this.callApi('getTickers', '/api/polygon/getTickers.do', {
        market: 'stocks',
        active: true,
        sort: 'ticker',
        order: 'asc',
        limit: 10
      })
    },

    async testGetTickerDetails () {
      await this.callApi('getTickerDetails', '/api/polygon/getTickerDetails.do', {
        ticker: 'AAPL'
      })
    },

    async testGetRelatedTickers () {
      await this.callApi('getRelatedTickers', '/api/polygon/getRelatedTickers.do', {
        ticker: 'AAPL'
      })
    },

    async testGetTickerNews () {
      await this.callApi('getTickerNews', '/api/polygon/getTickerNews.do', {
        ticker: 'AAPL',
        order: 'desc',
        limit: 5,
        sort: 'published_utc'
      })
    },

    async testGetTickerTypes () {
      await this.callApi('getTickerTypes', '/api/polygon/getTickerTypes.do')
    },

    // ======================== AGGREGATES 接口测试 ========================
    async testGetAggregates () {
      const today = new Date()
      const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)

      await this.callApi('getAggregates', '/api/polygon/getAggregates.do', {
        ticker: 'AAPL',
        multiplier: 1,
        timespan: 'day',
        from: lastWeek.toISOString().split('T')[0],
        to: today.toISOString().split('T')[0],
        adjusted: true,
        sort: 'asc',
        limit: 10
      })
    },

    async testGetGroupedDaily () {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)

      await this.callApi('getGroupedDaily', '/api/polygon/getGroupedDaily.do', {
        date: yesterday.toISOString().split('T')[0],
        adjusted: true,
        include_otc: false
      })
    },

    async testGetDailyOpenClose () {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)

      await this.callApi('getDailyOpenClose', '/api/polygon/getDailyOpenClose.do', {
        ticker: 'AAPL',
        date: yesterday.toISOString().split('T')[0],
        adjusted: true
      })
    },

    async testGetPreviousClose () {
      await this.callApi('getPreviousClose', '/api/polygon/getPreviousClose.do', {
        ticker: 'AAPL',
        adjusted: true
      })
    },

    // ======================== TRADES & QUOTES 接口测试 ========================
    async testGetTrades () {
      await this.callApi('getTrades', '/api/polygon/getTrades.do', {
        ticker: 'AAPL',
        order: 'asc',
        limit: 10,
        sort: 'timestamp'
      })
    },

    async testGetQuotes () {
      await this.callApi('getQuotes', '/api/polygon/getQuotes.do', {
        ticker: 'AAPL',
        order: 'asc',
        limit: 10,
        sort: 'timestamp'
      })
    },

    async testGetLastTrade () {
      await this.callApi('getLastTrade', '/api/polygon/getLastTrade.do', {
        ticker: 'AAPL'
      })
    },

    async testGetLastQuote () {
      await this.callApi('getLastQuote', '/api/polygon/getLastQuote.do', {
        ticker: 'AAPL'
      })
    },

    // ======================== SNAPSHOTS 接口测试 ========================
    async testGetAllTickersSnapshot () {
      await this.callApi('getAllTickersSnapshot', '/api/polygon/getAllTickersSnapshot.do', {
        include_otc: false
      })
    },

    async testGetTickerSnapshot () {
      await this.callApi('getTickerSnapshot', '/api/polygon/getTickerSnapshot.do', {
        ticker: 'AAPL'
      })
    },

    async testGetGainersSnapshot () {
      await this.callApi('getGainersSnapshot', '/api/polygon/getGainersSnapshot.do', {
        include_otc: false
      })
    },

    async testGetLosersSnapshot () {
      await this.callApi('getLosersSnapshot', '/api/polygon/getLosersSnapshot.do', {
        include_otc: false
      })
    },

    // ======================== REFERENCE DATA 接口测试 ========================
    async testGetExchanges () {
      await this.callApi('getExchanges', '/api/polygon/getExchanges.do')
    },

    async testGetMarketStatus () {
      await this.callApi('getMarketStatus', '/api/polygon/getMarketStatus.do')
    },

    async testGetMarketHolidays () {
      await this.callApi('getMarketHolidays', '/api/polygon/getMarketHolidays.do')
    },

    async testGetStockSplits () {
      await this.callApi('getStockSplits', '/api/polygon/getStockSplits.do', {
        order: 'asc',
        limit: 10,
        sort: 'execution_date'
      })
    },

    async testGetDividends () {
      await this.callApi('getDividends', '/api/polygon/getDividends.do', {
        order: 'asc',
        limit: 10,
        sort: 'ex_dividend_date'
      })
    },

    async testGetConditions () {
      await this.callApi('getConditions', '/api/polygon/getConditions.do', {
        order: 'asc',
        limit: 10,
        sort: 'name'
      })
    },

    // ======================== FUNDAMENTALS 接口测试 ========================
    async testGetStockFinancials () {
      await this.callApi('getStockFinancials', '/api/polygon/getStockFinancials.do', {
        ticker: 'AAPL',
        include_sources: false,
        order: 'desc',
        limit: 5,
        sort: 'period_of_report_date'
      })
    },

    // ======================== WEBSOCKET 接口测试 ========================
    async testSubscribeRealTime () {
      await this.callApi('subscribeRealTime', '/api/polygon/subscribeRealTime.do', {
        symbols: 'AAPL,MSFT',
        userId: 'test_user_001'
      })
    },

    async testUnsubscribeRealTime () {
      await this.callApi('unsubscribeRealTime', '/api/polygon/unsubscribeRealTime.do', {
        symbols: 'AAPL,MSFT',
        userId: 'test_user_001'
      })
    },

    async testGetLatestPrice () {
      await this.callApi('getLatestPrice', '/api/polygon/getLatestPrice.do', {
        symbol: 'AAPL'
      })
    },

    async testGetLatestPrices () {
      await this.callApi('getLatestPrices', '/api/polygon/getLatestPrices.do', {
        symbols: 'AAPL,MSFT,GOOGL'
      })
    },

    async testGetSubscriptionStatus () {
      await this.callApi('getSubscriptionStatus', '/api/polygon/getSubscriptionStatus.do')
    },

    async testReconnectWebSocket () {
      await this.callApi('reconnectWebSocket', '/api/polygon/reconnectWebSocket.do')
    },

    async testDisconnectWebSocket () {
      await this.callApi('disconnectWebSocket', '/api/polygon/disconnectWebSocket.do')
    },

    // ======================== 批量测试方法 ========================
    async testAllApis () {
      this.isTestingAll = true
      this.addLog('开始批量测试所有接口...', 'info')

      const allTests = [
        // TICKERS
        this.testGetTickers,
        this.testGetTickerDetails,
        this.testGetRelatedTickers,
        this.testGetTickerNews,
        this.testGetTickerTypes,
        // AGGREGATES
        this.testGetAggregates,
        this.testGetGroupedDaily,
        this.testGetDailyOpenClose,
        this.testGetPreviousClose,
        // TRADES & QUOTES
        this.testGetTrades,
        this.testGetQuotes,
        this.testGetLastTrade,
        this.testGetLastQuote,
        // SNAPSHOTS
        this.testGetAllTickersSnapshot,
        this.testGetTickerSnapshot,
        this.testGetGainersSnapshot,
        this.testGetLosersSnapshot,
        // REFERENCE DATA
        this.testGetExchanges,
        this.testGetMarketStatus,
        this.testGetMarketHolidays,
        this.testGetStockSplits,
        this.testGetDividends,
        this.testGetConditions,
        // FUNDAMENTALS
        this.testGetStockFinancials,
        // WEBSOCKET
        this.testGetSubscriptionStatus,
        this.testGetLatestPrice,
        this.testGetLatestPrices
      ]

      let successCount = 0
      let failCount = 0

      for (let i = 0; i < allTests.length; i++) {
        try {
          await allTests[i]()
          successCount++
          // 添加延迟避免请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 500))
        } catch (error) {
          failCount++
        }
      }

      this.addLog(`批量测试完成！成功: ${successCount}, 失败: ${failCount}`, 'success')
      this.isTestingAll = false
    }
  },

  mounted () {
    this.addLog('Polygon API 测试页面已加载')
    this.addLog('点击"测试所有接口"按钮可以一次性测试所有接口')
  }
}
</script>

<style scoped>
.api-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.test-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 20px;
}

.test-all-btn {
  padding: 12px 24px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: background 0.3s;
}

.test-all-btn:hover:not(:disabled) {
  background: #218838;
}

.test-all-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.save-btn {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.3s;
}

.save-btn:hover:not(:disabled) {
  background: #0056b3;
}

.save-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.test-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-section h2 {
  color: #333;
  margin-bottom: 15px;
  border-bottom: 2px solid #667eea;
  padding-bottom: 5px;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.test-buttons button {
  padding: 8px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.test-buttons button:hover:not(:disabled) {
  background: #5a67d8;
}

.test-buttons button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.test-results {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.test-results h3 {
  margin-top: 0;
  color: #495057;
}

.test-results pre {
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.test-logs {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 5px;
  padding: 2px 0;
}

.log-item.success {
  color: #28a745;
}

.log-item.error {
  color: #dc3545;
}

.log-item.info {
  color: #6c757d;
}

.log-time {
  color: #6c757d;
  margin-right: 10px;
}

.clear-btn {
  margin-top: 10px;
  padding: 6px 12px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.clear-btn:hover {
  background: #5a6268;
}
</style>
