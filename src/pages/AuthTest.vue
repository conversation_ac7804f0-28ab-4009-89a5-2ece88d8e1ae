<template>
  <div class="auth-test">
    <div class="test-header">
      <h1>认证拦截器测试页面</h1>
      <p>测试登录状态检查和 token 管理</p>
    </div>

    <div class="test-sections">
      <!-- 登录状态 -->
      <div class="test-section">
        <h2>🔐 当前登录状态</h2>
        <div class="status-info">
          <div class="status-item">
            <span class="label">登录状态:</span>
            <span :class="['value', isLoggedIn ? 'success' : 'error']">
              {{ isLoggedIn ? '已登录' : '未登录' }}
            </span>
          </div>
          <div class="status-item">
            <span class="label">Token:</span>
            <span class="value token">{{ token || '无' }}</span>
          </div>
          <div class="status-item">
            <span class="label">用户信息:</span>
            <span class="value">{{ userInfo.name || '无' }}</span>
          </div>
        </div>
      </div>

      <!-- 测试按钮 -->
      <div class="test-section">
        <h2>🧪 拦截器测试</h2>
        <div class="test-buttons">
          <button @click="testNormalApi" :disabled="loading.normal">
            {{ loading.normal ? '测试中...' : '测试普通接口' }}
          </button>
          <button @click="testAuthApi" :disabled="loading.auth">
            {{ loading.auth ? '测试中...' : '测试需要登录的接口' }}
          </button>
          <button @click="testPolygonApi" :disabled="loading.polygon">
            {{ loading.polygon ? '测试中...' : '测试 Polygon API' }}
          </button>
          <button @click="clearToken" class="danger-btn">
            清除 Token
          </button>
        </div>
      </div>

      <!-- 测试结果 -->
      <div class="test-section">
        <h2>📋 测试结果</h2>
        <div class="test-logs">
          <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <button @click="clearLogs" class="clear-btn">清空日志</button>
      </div>

      <!-- 手动登录测试 -->
      <div class="test-section">
        <h2>🔑 手动登录测试</h2>
        <div class="login-form">
          <input
            v-model="testEmail"
            type="email"
            placeholder="测试邮箱"
            class="test-input"
          />
          <input
            v-model="testPassword"
            type="password"
            placeholder="测试密码"
            class="test-input"
          />
          <button @click="testLogin" :disabled="loading.login" class="login-btn">
            {{ loading.login ? '登录中...' : '测试登录' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  isLoggedIn,
  getToken,
  getUserInfo,
  handleLogout,
  handleLoginSuccess
} from '@/utils/auth'
import {
  getMarket,
  getMyList,
  getTickers,
  login
} from '@/axios/api'

export default {
  name: 'AuthTest',
  data () {
    return {
      loading: {
        normal: false,
        auth: false,
        polygon: false,
        login: false
      },
      logs: [],
      testEmail: '<EMAIL>',
      testPassword: '123456'
    }
  },

  computed: {
    ...mapGetters(['token', 'isLoggedIn', 'userInfo'])
  },

  methods: {
    // 添加日志
    addLog (message, type = 'info') {
      this.logs.unshift({
        time: new Date().toLocaleTimeString(),
        message,
        type
      })
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20)
      }
    },

    // 清空日志
    clearLogs () {
      this.logs = []
    },

    // 测试普通接口
    async testNormalApi () {
      this.loading.normal = true
      this.addLog('开始测试普通接口 (getMarket)...')

      try {
        const result = await getMarket()
        this.addLog('普通接口调用成功', 'success')
      } catch (error) {
        this.addLog(`普通接口调用失败: ${error.message}`, 'error')
      } finally {
        this.loading.normal = false
      }
    },

    // 测试需要登录的接口
    async testAuthApi () {
      this.loading.auth = true
      this.addLog('开始测试需要登录的接口 (getMyList)...')

      try {
        const result = await getMyList()
        this.addLog('需要登录的接口调用成功', 'success')
      } catch (error) {
        this.addLog(`需要登录的接口调用失败: ${error.message}`, 'error')
      } finally {
        this.loading.auth = false
      }
    },

    // 测试 Polygon API
    async testPolygonApi () {
      this.loading.polygon = true
      this.addLog('开始测试 Polygon API (getTickers)...')

      try {
        const result = await getTickers({
          market: 'stocks',
          active: true,
          limit: 5
        })
        this.addLog('Polygon API 调用成功', 'success')
      } catch (error) {
        this.addLog(`Polygon API 调用失败: ${error.message}`, 'error')
      } finally {
        this.loading.polygon = false
      }
    },

    // 测试登录
    async testLogin () {
      if (!this.testEmail || !this.testPassword) {
        this.addLog('请输入测试邮箱和密码', 'error')
        return
      }

      this.loading.login = true
      this.addLog(`开始测试登录: ${this.testEmail}`)

      try {
        const result = await login({
          username: this.testEmail,
          password: this.testPassword,
          email: this.testEmail
        })

        if (result) {
          const success = handleLoginSuccess(result)
          if (success) {
            this.addLog('登录测试成功', 'success')
          } else {
            this.addLog('登录处理失败', 'error')
          }
        } else {
          this.addLog('登录响应格式错误', 'error')
        }
      } catch (error) {
        this.addLog(`登录测试失败: ${error.message}`, 'error')
      } finally {
        this.loading.login = false
      }
    },

    // 清除 Token
    clearToken () {
      this.addLog('手动清除 Token')
      handleLogout()
    }
  },

  mounted () {
    this.addLog('认证拦截器测试页面已加载')
    this.addLog(`当前登录状态: ${this.isLoggedIn ? '已登录' : '未登录'}`)
  }
}
</script>

<style scoped>
.auth-test {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.test-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-section h2 {
  margin-bottom: 15px;
  color: #333;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.status-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: 500;
  margin-right: 10px;
  min-width: 80px;
}

.value {
  font-family: monospace;
}

.value.success {
  color: #00aa3b;
}

.value.error {
  color: #ff4444;
}

.value.token {
  font-size: 12px;
  word-break: break-all;
  max-width: 200px;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.test-buttons button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.test-buttons button:not(.danger-btn) {
  background: #007bff;
  color: white;
}

.test-buttons button:not(.danger-btn):hover:not(:disabled) {
  background: #0056b3;
}

.danger-btn {
  background: #dc3545 !important;
  color: white !important;
}

.danger-btn:hover {
  background: #c82333 !important;
}

.test-buttons button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.test-logs {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-size: 14px;
}

.log-time {
  color: #666;
  margin-right: 10px;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-item.success .log-message {
  color: #00aa3b;
}

.log-item.error .log-message {
  color: #ff4444;
}

.log-item.warning .log-message {
  color: #ff8800;
}

.clear-btn {
  padding: 8px 16px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 300px;
}

.test-input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.login-btn {
  padding: 10px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.login-btn:hover:not(:disabled) {
  background: #218838;
}

.login-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}
</style>
