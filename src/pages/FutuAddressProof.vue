<template>
  <div class="futu-address-proof">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left">
        <div class="back-icon" @click="goBack">‹</div>
        <span class="title">网上转账开户</span>
      </div>
      <div class="header-right">
        <div class="help-icon">🎧</div>
        <div class="more-icon">⋯</div>
        <div class="message-icon">
          <div class="message-icon-inner">✉️</div>
          <span class="badge">3</span>
        </div>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar">
      <div class="progress-fill" :style="{ width: '60%' }"></div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <h1 class="page-title">上传地址证明</h1>

      <!-- 地址信息表单 -->
      <div class="address-form">
        <h2 class="form-title">地址信息</h2>

        <!-- 居住地址 -->
        <div class="form-group">
          <label class="form-label">居住地址</label>
          <textarea
            v-model="addressInfo.address"
            placeholder="请输入详细居住地址"
            class="form-textarea"
            :class="{ 'error': addressError }"
            @blur="validateAddress"
            @input="clearAddressError"
            rows="3"
          ></textarea>
          <div v-if="addressError" class="error-message">Please enter detailed residential address</div>
        </div>

        <!-- 邮政编码 -->
        <div class="form-group">
          <label class="form-label">邮政编码</label>
          <input
            type="text"
            v-model="addressInfo.postalCode"
            placeholder="请输入邮政编码"
            class="form-input"
          />
        </div>

        <!-- 地址是否与身份证地址一致 -->
        <div class="checkbox-group">
          <div class="checkbox-field">
            <div class="checkbox" :class="{ checked: addressInfo.sameAsIdCard }" @click="addressInfo.sameAsIdCard = !addressInfo.sameAsIdCard">
              <span v-if="addressInfo.sameAsIdCard" class="checkmark">✓</span>
            </div>
            <span class="checkbox-label">居住地址与身份证地址一致</span>
          </div>
        </div>
      </div>

      <!-- 地址证明文件上传 -->
      <div class="proof-upload-section">
        <h2 class="upload-title">上传地址证明文件</h2>
        <div class="upload-description">
          请上传能证明您居住地址的文件，文件需要显示您的姓名、完整地址以及发出日期（3个月内）
        </div>

        <!-- 地址证明类型说明 -->
        <div class="proof-types">
          <h3 class="types-title">可接受的地址证明文件包括：</h3>
          <div class="type-list">
            <div class="type-item">
              <div class="type-icon">📄</div>
              <div class="type-content">
                <div class="type-name">银行账户结单</div>
                <div class="type-desc">3个月内发出的载有地址的银行月结单</div>
              </div>
            </div>
            <div class="type-item">
              <div class="type-icon">💡</div>
              <div class="type-content">
                <div class="type-name">水电费账单</div>
                <div class="type-desc">电费单、水费单、煤气费单等公用事业账单</div>
              </div>
            </div>
            <div class="type-item">
              <div class="type-icon">📱</div>
              <div class="type-content">
                <div class="type-name">电话费账单</div>
                <div class="type-desc">手机或固定电话的费用账单</div>
              </div>
            </div>
            <div class="type-item">
              <div class="type-icon">💳</div>
              <div class="type-content">
                <div class="type-name">信用卡对账单</div>
                <div class="type-desc">3个月内发出的信用卡月结单</div>
              </div>
            </div>
            <div class="type-item">
              <div class="type-icon">🏠</div>
              <div class="type-content">
                <div class="type-name">物业管理费单</div>
                <div class="type-desc">住宅物业管理费收据或账单</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 文件上传区域 -->
        <div class="upload-areas">
          <!-- 地址证明文件上传 -->
          <div class="upload-area" @click="uploadProofFile" v-if="!uploadedProofFile">
            <div class="upload-icon">📤</div>
            <div class="upload-text">上传地址证明文件</div>
            <div class="upload-note">支持 JPG, PNG, PDF 格式，文件大小不超过 5MB</div>
          </div>

          <!-- 已上传的地址证明文件 -->
          <div class="uploaded-file" v-if="uploadedProofFile">
            <div class="file-preview">
              <div class="file-icon">📄</div>
              <div class="file-info">
                <div class="file-name">地址证明文件</div>
                <div class="file-size">{{ uploadedProofFile.size }}</div>
              </div>
              <button class="delete-btn" @click="deleteProofFile">🗑️</button>
            </div>
          </div>
        </div>

        <!-- 温馨提示 -->
        <div class="upload-tips">
          <div class="tips-title">📋 温馨提示：</div>
          <ul class="tips-list">
            <li>• 地址证明文件必须是最近3个月内发出的</li>
            <li>• 文件上必须清晰显示您的姓名和完整地址</li>
            <li>• 请确保文件内容清晰可读，避免模糊或缺失</li>
            <li>• 如果您的居住地址与身份证地址不同，必须提供地址证明</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-buttons">
      <button class="prev-btn" @click="goBack">
        上一步
      </button>
      <button
        class="next-btn"
        :class="{ disabled: !canProceed }"
        @click="nextStep"
        :disabled="!canProceed"
      >
        下一步
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FutuAddressProof',
  data () {
    return {
      addressInfo: {
        address: '',
        postalCode: '',
        sameAsIdCard: false
      },
      uploadedProofFile: null,
      // 验证错误状态
      addressError: false
    }
  },
  computed: {
    canProceed () {
      // 如果地址与身份证一致，则不需要上传地址证明
      if (this.addressInfo.sameAsIdCard) {
        return this.addressInfo.address.length > 0
      }
      // 如果地址不一致，需要上传地址证明
      return this.addressInfo.address.length > 0 && this.uploadedProofFile
    }
  },
  methods: {
    goBack () {
      this.$router.go(-1)
    },
    nextStep () {
      if (!this.canProceed) return

      // 保存地址证明信息
      this.$store.commit('setAddressProofData', {
        addressProofType: this.addressProofType,
        uploadedFile: this.uploadedProofFile,
        addressInfo: this.addressInfo
      })

      // 跳转到投资经验页面
      this.$router.push('/open-account/investment-experience')
    },
    uploadProofFile () {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/*,.pdf'
      input.onchange = (e) => {
        const file = e.target.files[0]
        if (file) {
          // 检查文件大小 (5MB)
          if (file.size > 5 * 1024 * 1024) {
            alert('文件大小不能超过 5MB')
            return
          }

          this.uploadedProofFile = {
            file: file,
            name: file.name,
            size: this.formatFileSize(file.size)
          }
        }
      }
      input.click()
    },
    deleteProofFile () {
      this.uploadedProofFile = null
    },
    formatFileSize (bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    validateAddress () {
      this.addressError = this.addressInfo.address.length < 10
    },
    clearAddressError () {
      this.addressError = false
    }
  }
}
</script>

<style scoped>
.futu-address-proof {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: white;
  border-bottom: 1px solid #eee;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #333;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.help-icon, .more-icon, .message-icon-inner {
  font-size: 18px;
  cursor: pointer;
  color: #333;
}

.message-icon {
  position: relative;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-bar {
  height: 4px;
  background: #f0f0f0;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: #333;
  transition: width 0.3s ease;
}

.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0 0 30px 0;
}

/* 地址信息表单 */
.address-form {
  margin-bottom: 40px;
}

.form-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px 0;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  color: #333;
  background: #fff;
  box-sizing: border-box;
  transition: border-color 0.3s;
  font-family: inherit;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #333;
}

.form-input.error, .form-textarea.error {
  border-color: #ff4444;
}

.form-input::placeholder, .form-textarea::placeholder {
  color: #999;
}

.error-message {
  margin-top: 6px;
  font-size: 12px;
  color: #ff4444;
}

.checkbox-group {
  margin-top: 20px;
}

.checkbox-field {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 4px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.checkbox.checked {
  background: #333;
  border-color: #333;
}

.checkmark {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-label {
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

/* 地址证明上传区域 */
.proof-upload-section {
  margin-bottom: 40px;
}

.upload-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.upload-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
  line-height: 1.5;
}

.proof-types {
  margin-bottom: 30px;
}

.types-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.type-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.type-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  background: #f8f8f8;
  border-radius: 8px;
}

.type-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
}

.type-content {
  flex: 1;
}

.type-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.type-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.upload-areas {
  margin-bottom: 24px;
}

.upload-area {
  border: 2px dashed #ddd;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #333;
  background: #f8f8f8;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.upload-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
}

.upload-note {
  font-size: 12px;
  color: #999;
}

.uploaded-file {
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 16px;
  background: #f8f8f8;
}

.file-preview {
  display: flex;
  align-items: center;
}

.file-icon {
  font-size: 24px;
  margin-right: 12px;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #666;
}

.delete-btn {
  width: 36px;
  height: 36px;
  background: rgba(0, 0, 0, 0.1);
  color: #666;
  border: none;
  border-radius: 50%;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s;
}

.delete-btn:hover {
  background: rgba(0, 0, 0, 0.2);
}

.upload-tips {
  padding: 16px;
  background: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #007AFF;
}

.tips-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.tips-list {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

.tips-list li {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  line-height: 1.4;
}

/* 底部按钮 */
.bottom-buttons {
  display: flex;
  gap: 12px;
  padding: 20px;
  background: white;
  border-top: 1px solid #eee;
}

.prev-btn {
  flex: 1;
  height: 50px;
  background: white;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.prev-btn:hover {
  background: #f8f8f8;
}

.next-btn {
  flex: 1;
  height: 50px;
  background: #333;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
}

.next-btn:hover:not(.disabled) {
  background: #555;
}

.next-btn.disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>
