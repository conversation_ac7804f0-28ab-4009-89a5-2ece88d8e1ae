<template>
  <div class="futu-discover">
    <!-- 顶部标题栏 -->
    <div class="discover-header">
      <div class="header-left">
        <div class="logo-icon">🐂</div>
        <span class="title">Discover</span>
      </div>
      <div class="header-right">
        <van-icon name="search" class="header-icon" @click="goToSearch" />
        <van-icon name="envelop-o" class="header-icon" @click="showMessages" />
      </div>
    </div>

    <!-- 机会卡片 -->
    <div class="opportunity-card">
      <div class="card-header">
        <div class="card-icon">
          <van-icon name="bulb-o" />
        </div>
        <span class="card-title">Opportunities</span>
        <van-icon name="arrow" class="card-arrow" />
      </div>
    </div>

    <!-- 主要内容卡片 -->
    <div class="main-content">
      <!-- 以色列对伊朗发动袭击 -->
      <div class="news-card featured-news" @click="handleNewsClick('israel-iran')">
        <div class="news-content">
          <h3 class="news-title">Israel strikes Iran - How to navigate volatile markets?</h3>
          <div class="news-meta">
            <span class="price-change up">+0.51%</span>
            <span class="time-info">10-day gain</span>
          </div>
        </div>
        <div class="news-image">
          <div class="image-placeholder">
            <div class="play-button">▶</div>
            <div class="uvix-label">UVIX</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能菜单列表 -->
    <div class="feature-list">
      <!-- 资讯 -->
      <div class="feature-item" @click="handleFeature('news')">
        <div class="feature-icon news-icon">
          <van-icon name="newspaper-o" />
        </div>
        <span class="feature-text">资讯</span>
        <van-icon name="arrow" class="feature-arrow" />
      </div>

      <!-- 牛牛圈 -->
      <div class="feature-item" @click="handleFeature('community')">
        <div class="feature-icon community-icon">
          <div class="bull-icon">🐂</div>
        </div>
        <span class="feature-text">牛牛圈</span>
        <div class="feature-badge">
          <span class="badge-text">牛牛AI看财报，秒get赢家</span>
          <div class="red-dot"></div>
        </div>
        <van-icon name="arrow" class="feature-arrow" />
      </div>

      <!-- 聊天 -->
      <div class="feature-item" @click="handleFeature('chat')">
        <div class="feature-icon chat-icon">
          <van-icon name="chat-o" />
        </div>
        <span class="feature-text">聊天</span>
        <van-icon name="arrow" class="feature-arrow" />
      </div>

      <!-- 课堂 -->
      <div class="feature-item" @click="handleFeature('classroom')">
        <div class="feature-icon classroom-icon">
          <van-icon name="book-o" />
        </div>
        <span class="feature-text">课堂</span>
        <div class="red-dot"></div>
        <van-icon name="arrow" class="feature-arrow" />
      </div>

      <!-- 模拟交易 -->
      <div class="feature-item" @click="handleFeature('simulation')">
        <div class="feature-icon simulation-icon">
          <van-icon name="chart-trending-o" />
        </div>
        <span class="feature-text">模拟交易</span>
        <van-icon name="arrow" class="feature-arrow" />
      </div>
    </div>

    <!-- 底部布局设置 -->
    <div class="layout-settings">
      <div class="settings-button" @click="showLayoutSettings">
        <van-icon name="setting-o" />
        <span>布局设置</span>
      </div>
    </div>

    <!-- 底部导航 -->
    <FutuTabbar />
  </div>
</template>

<script>
import FutuTabbar from '@/components/FutuTabbar.vue'

export default {
  name: 'FutuDiscover',
  components: {
    FutuTabbar
  },
  data () {
    return {
      featuredNews: {
        title: '以色列对伊朗发动袭击 震荡市如何"攻守兼备"？',
        change: '+0.51%',
        period: '10日涨幅',
        symbol: 'UVIX'
      }
    }
  },

  methods: {
    goToSearch () {
      this.$router.push('/search')
    },

    showMessages () {
      this.$toast('Messages feature')
    },

    handleNewsClick (newsId) {
      this.$toast('View news details')
      // 可以跳转到具体新闻页面
      // this.$router.push(`/news/${newsId}`)
    },

    handleFeature (type) {
      switch (type) {
        case 'news':
          this.$toast('News feature')
          break
        case 'community':
          this.$toast('Bull Circle - AI Financial Reports')
          break
        case 'chat':
          this.$toast('Chat feature')
          break
        case 'classroom':
          this.$toast('Classroom feature')
          break
        case 'simulation':
          this.$toast('Paper trading')
          break
        default:
          this.$toast('Feature under development')
      }
    },

    showLayoutSettings () {
      this.$toast('Layout settings')
    }
  }
}
</script>

<style scoped>
.futu-discover {
  min-height: 100vh;
  background: #F5F5F5;
  padding-bottom: 80px;
}

/* 顶部标题栏 */
.discover-header {
  background: #fff;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #F0F0F0;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-icon {
  font-size: 24px;
  margin-right: 8px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  gap: 16px;
}

.header-icon {
  font-size: 20px;
  color: #666;
  cursor: pointer;
}

/* 机会卡片 */
.opportunity-card {
  background: #fff;
  margin: 12px 16px;
  border-radius: 12px;
  padding: 16px 20px;
}

.card-header {
  display: flex;
  align-items: center;
}

.card-icon {
  width: 24px;
  height: 24px;
  background: #007AFF;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  margin-right: 12px;
}

.card-title {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.card-arrow {
  color: #C8C9CC;
  font-size: 16px;
}

/* 主要内容 */
.main-content {
  margin: 0 16px 12px;
}

.news-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.news-card:hover {
  background-color: #F8F9FA;
}

.featured-news {
  display: flex;
  align-items: center;
  gap: 16px;
}

.news-content {
  flex: 1;
}

.news-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin: 0 0 12px 0;
}

.news-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-change {
  font-size: 14px;
  font-weight: 600;
}

.price-change.up {
  color: #00D4AA;
}

.price-change.down {
  color: #FF4444;
}

.time-info {
  font-size: 12px;
  color: #999;
}

.news-image {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FFB800, #FF8A00);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.play-button {
  color: #fff;
  font-size: 20px;
  margin-bottom: 8px;
}

.uvix-label {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 功能列表 */
.feature-list {
  background: #fff;
  margin: 0 16px 12px;
  border-radius: 12px;
  overflow: hidden;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #F5F5F5;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.feature-item:hover {
  background-color: #F8F9FA;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 16px;
  color: #fff;
}

.news-icon {
  background: #007AFF;
}

.community-icon {
  background: #FF9500;
}

.chat-icon {
  background: #007AFF;
}

.classroom-icon {
  background: #00D4AA;
}

.simulation-icon {
  background: #00D4AA;
}

.bull-icon {
  font-size: 18px;
}

.feature-text {
  flex: 1;
  font-size: 16px;
  color: #333;
}

.feature-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 12px;
}

.badge-text {
  font-size: 12px;
  color: #999;
}

.red-dot {
  width: 6px;
  height: 6px;
  background: #FF4444;
  border-radius: 50%;
}

.feature-arrow {
  color: #C8C9CC;
  font-size: 16px;
}

/* 布局设置 */
.layout-settings {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.settings-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #fff;
  border: 1px solid #E5E5E5;
  border-radius: 24px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.settings-button:hover {
  background-color: #F8F9FA;
  border-color: #D0D0D0;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .news-title {
    font-size: 15px;
  }

  .news-image {
    width: 70px;
    height: 70px;
  }

  .feature-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
}
</style>
