<template>
  <div class="gainers-losers-page">
    <!-- 头部导航 -->
    <div class="header">
      <div class="header-left" @click="goBack">
        <van-icon name="arrow-left" />
      </div>
      <div class="header-title">Featured Rankings</div>
      <div class="header-right">
        <van-icon name="search" />
      </div>
    </div>

    <!-- 标签页切换 -->
    <div class="tabs-container">
      <div class="tabs-wrapper">
        <div class="tab-item" :class="{ active: activeTab === 'premarket' }" @click="switchTab('premarket')">
          Pre-Market
        </div>
        <div class="tab-item" :class="{ active: activeTab === 'aftermarket' }" @click="switchTab('aftermarket')">
          After Hours
        </div>
        <div class="tab-item" :class="{ active: activeTab === 'gainers' }" @click="switchTab('gainers')">
          Gainers
        </div>
        <div class="tab-item" :class="{ active: activeTab === 'losers' }" @click="switchTab('losers')">
          Losers
        </div>
        <div class="tab-item" :class="{ active: activeTab === 'hot' }" @click="switchTab('hot')">
          Trending
        </div>
        <div class="tab-item" :class="{ active: activeTab === 'unusual' }" @click="switchTab('unusual')">
          Short Activity
        </div>
        <div class="tab-item" :class="{ active: activeTab === 'regions' }" @click="switchTab('regions')">
          Regions
        </div>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="view-options">
        <van-icon name="apps-o" class="view-icon" />
        <van-icon name="list-switch" class="view-icon" />
        <van-icon name="sort" class="view-icon" />
      </div>
      <div class="sort-options">
        <div class="sort-item active">
          <span>价格</span>
          <van-icon name="arrow-down" />
        </div>
        <div class="sort-item">
          <span>涨跌幅</span>
          <van-icon name="arrow-down" />
        </div>
        <div class="sort-item">
          <span>涨跌额</span>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-container" v-if="loading">
      <van-loading type="spinner" vertical>加载中...</van-loading>
    </div>

    <!-- 表头 -->
    <div class="table-header" v-if="hasData && !loading">
      <div class="header-rank">排名</div>
      <div class="header-stock">股票</div>
      <div class="header-price">价格<br><span class="sub-label">高/低</span></div>
      <div class="header-change">涨跌幅<br><span class="sub-label">涨跌额</span></div>
      <div class="header-volume">成交量<br><span class="sub-label">成交额</span></div>
      <div class="header-amplitude">振幅</div>
    </div>

    <!-- 股票列表 -->
    <div class="stock-list" v-if="hasData && !loading">
      <div
        v-for="stock in currentData"
        :key="stock.ticker"
        class="stock-item"
        @click="goToStockDetail(stock.ticker)"
      >
        <!-- 排名 -->
        <div class="rank">{{ stock.rank }}</div>

        <!-- 股票信息 -->
        <div class="stock-info">
          <div class="stock-name">{{ stock.name }}</div>
          <div class="stock-code">{{ stock.ticker }}</div>
        </div>

        <!-- 价格信息 -->
        <div class="price-info">
          <div class="current-price">{{ formatPrice(stock.currentPrice) }}</div>
          <div class="price-details">
            <span class="high-low">{{ formatPrice(stock.high) }}/{{ formatPrice(stock.low) }}</span>
          </div>
        </div>

        <!-- 涨跌信息 -->
        <div class="change-info">
          <div class="change-percent" :class="getChangeClass(stock.changePercent)">
            {{ formatPercent(stock.changePercent) }}
          </div>
          <div class="change-amount" :class="getChangeClass(stock.change)">
            {{ formatChange(stock.change) }}
          </div>
        </div>

        <!-- 成交信息 -->
        <div class="volume-info">
          <div class="volume">{{ formatVolume(stock.volume) }}</div>
          <div class="turnover">{{ formatTurnover(stock.turnover) }}</div>
        </div>

        <!-- 振幅 -->
        <div class="amplitude-info">
          <div class="amplitude">{{ formatPercent(stock.amplitude) }}</div>
          <div class="amplitude-label">振幅</div>
        </div>
      </div>
    </div>

    <!-- 空数据状态 -->
    <div class="empty-state" v-else-if="!loading && !hasData">
      <div class="empty-icon">📊</div>
      <div class="empty-text">暂无数据</div>
      <div class="empty-subtext">请稍后重试或检查网络连接</div>
    </div>

    <!-- 错误状态 -->
    <div class="error-state" v-if="error">
      <div class="error-icon">⚠️</div>
      <div class="error-text">{{ error }}</div>
      <van-button type="primary" @click="refreshData">重新加载</van-button>
    </div>

    <!-- 底部导航 -->
    <FutuTabbar />
  </div>
</template>

<script>
import FutuTabbar from '@/components/FutuTabbar.vue'
import { getGainersSnapshot, getLosersSnapshot } from '@/axios/api'

export default {
  name: 'FutuGainersLosers',
  components: {
    FutuTabbar
  },
  data () {
    return {
      activeTab: 'gainers', // 当前选中的标签页
      loading: false,
      error: null,
      lastUpdateTime: '',

      // 涨幅榜数据
      gainersData: [],

      // 跌幅榜数据
      losersData: [],

      // 定时器
      refreshTimer: null
    }
  },

  computed: {
    // 当前显示的数据
    currentData () {
      return this.activeTab === 'gainers' ? this.gainersData : this.losersData
    },

    // 当前标签页是否有数据
    hasData () {
      return this.currentData && this.currentData.length > 0
    }
  },

  mounted () {
    this.initPage()
  },

  beforeDestroy () {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },

  methods: {
    // 初始化页面
    async initPage () {

      // 从路由参数获取默认标签页
      const tab = this.$route.query.tab
      if (tab === 'losers') {
        this.activeTab = 'losers'
      }

      // 加载数据
      await this.loadData()

      // 设置定时刷新（每30秒）
      this.refreshTimer = setInterval(() => {
        this.loadData(true) // 静默刷新
      }, 30000)
    },

    // 加载数据
    async loadData (silent = false) {
      if (!silent) {
        this.loading = true
        this.error = null
      }

      try {

        // 并发调用两个接口
        const [gainersResponse, losersResponse] = await Promise.all([
          getGainersSnapshot({
            include_otc: false
          }).catch(e => {
            return { error: e }
          }),

          getLosersSnapshot({
            include_otc: false
          }).catch(e => {
            return { error: e }
          })
        ])

        // 处理涨幅榜数据
        if (gainersResponse && !gainersResponse.error && gainersResponse.data) {
          this.gainersData = this.processStockData(gainersResponse.data.tickers || [], 'gainers')
        }

        // 处理跌幅榜数据
        if (losersResponse && !losersResponse.error && losersResponse.data) {
          this.losersData = this.processStockData(losersResponse.data.tickers || [], 'losers')
        }

        // 更新时间
        this.lastUpdateTime = new Date().toLocaleTimeString()

        // 检查是否有错误
        if (gainersResponse.error && losersResponse.error) {
          this.error = '数据加载失败，请检查网络连接'
        }
      } catch (error) {
        this.error = '数据加载失败: ' + error.message
      } finally {
        this.loading = false
      }
    },

    // 处理股票数据
    processStockData (data, type) {
      if (!data || !Array.isArray(data)) {
        return []
      }

      return data.map((item, index) => {
        // 当前价格（使用day.c作为收盘价）
        const currentPrice = (item.day && item.day.c) || (item.lastTrade && item.lastTrade.p) || 0

        // 前一日收盘价
        const prevClose = (item.prevDay && item.prevDay.c) || 0

        // 涨跌额和涨跌幅
        const change = item.todaysChange || 0
        const changePercent = item.todaysChangePerc || 0

        // 成交量和成交额
        const volume = (item.day && item.day.v) || 0
        const turnover = ((item.day && item.day.vw) || 0) * volume // 成交额 = 成交量加权平均价 * 成交量

        // 最高价和最低价
        const high = (item.day && item.day.h) || 0
        const low = (item.day && item.day.l) || 0
        const open = (item.day && item.day.o) || 0

        // 振幅计算
        const amplitude = prevClose > 0 ? ((high - low) / prevClose * 100) : 0

        // 模拟数据（实际项目中需要从其他接口获取）
        const mockData = {
          turnoverRate: Math.random() * 10, // 换手率 0-10%
          pe: Math.random() * 50 + 10, // 市盈率 10-60
          marketCap: Math.random() * 1000000000 + 100000000, // 市值
          volumeRatio: Math.random() * 3 + 0.5, // 量比 0.5-3.5
          bidRatio: (Math.random() - 0.5) * 100 // 委比 -50% 到 50%
        }

        return {
          rank: index + 1,
          ticker: item.ticker || '',
          name: item.ticker || '', // 实际项目中需要股票名称映射
          currentPrice: currentPrice,
          change: change,
          changePercent: changePercent,
          volume: volume,
          turnover: turnover,
          turnoverRate: mockData.turnoverRate,
          pe: mockData.pe,
          amplitude: amplitude,
          marketCap: mockData.marketCap,
          volumeRatio: mockData.volumeRatio,
          bidRatio: mockData.bidRatio,
          high: high,
          low: low,
          open: open,
          prevClose: prevClose,
          // 原始数据保存，用于调试
          rawData: item
        }
      })
    },

    // 切换标签页
    switchTab (tab) {
      this.activeTab = tab

      // 更新路由参数
      this.$router.replace({
        query: { ...this.$route.query, tab }
      })

      // 如果切换到涨幅榜或跌幅榜，加载对应数据
      if (tab === 'gainers' || tab === 'losers') {
        if (this[tab + 'Data'].length === 0) {
          this.loadData()
        }
      }
    },

    // 刷新数据
    async refreshData () {
      await this.loadData()
    },

    // 返回上一页
    goBack () {
      this.$router.go(-1)
    },

    // 跳转到股票详情
    goToStockDetail (stock) {
      this.$router.push({
        path: '/stock-detail',
        query: {
          ticker: stock.ticker,
          name: stock.name
        }
      })
    },

    // 格式化价格
    formatPrice (value) {
      if (value === null || value === undefined || isNaN(value)) {
        return '0.000'
      }
      return value.toFixed(3)
    },

    // 格式化百分比
    formatPercent (value) {
      if (value === null || value === undefined || isNaN(value)) {
        return '0.00%'
      }
      return (value >= 0 ? '+' : '') + value.toFixed(2) + '%'
    },

    // 格式化涨跌额
    formatChange (value) {
      if (value === null || value === undefined || isNaN(value)) {
        return '0.000'
      }
      return (value >= 0 ? '+' : '') + value.toFixed(3)
    },

    // 格式化成交量
    formatVolume (value) {
      if (value === null || value === undefined || isNaN(value)) {
        return '0'
      }

      if (value >= 1000000000) {
        return (value / 1000000000).toFixed(2) + 'B'
      } else if (value >= 1000000) {
        return (value / 1000000).toFixed(2) + 'M'
      } else if (value >= 1000) {
        return (value / 1000).toFixed(2) + 'K'
      }
      return Math.round(value).toString()
    },

    // 格式化成交额
    formatTurnover (value) {
      if (value === null || value === undefined || isNaN(value)) {
        return '0'
      }

      if (value >= 1000000000) {
        return '$' + (value / 1000000000).toFixed(2) + 'B'
      } else if (value >= 1000000) {
        return '$' + (value / 1000000).toFixed(2) + 'M'
      } else if (value >= 1000) {
        return '$' + (value / 1000).toFixed(2) + 'K'
      }
      return '$' + Math.round(value).toString()
    },

    // 格式化市值
    formatMarketCap (value) {
      if (value === null || value === undefined || isNaN(value)) {
        return '0'
      }

      if (value >= 1000000000000) {
        return '$' + (value / 1000000000000).toFixed(2) + 'T'
      } else if (value >= 1000000000) {
        return '$' + (value / 1000000000).toFixed(2) + 'B'
      } else if (value >= 1000000) {
        return '$' + (value / 1000000).toFixed(2) + 'M'
      }
      return '$' + Math.round(value).toString()
    },

    // 格式化比率
    formatRatio (value) {
      if (value === null || value === undefined || isNaN(value)) {
        return '0.00'
      }
      return value.toFixed(2)
    },

    // 获取涨跌类名
    getChangeClass (value) {
      if (value === null || value === undefined || isNaN(value)) {
        return 'neutral'
      }
      return value >= 0 ? 'up' : 'down'
    }
  }
}
</script>

<style lang="less" scoped>
.gainers-losers-page {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: 60px;
}

// 头部导航
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;

  .header-left {
    .van-icon {
      font-size: 18px;
      color: #333;
    }
  }

  .header-title {
    font-size: 17px;
    font-weight: 600;
    color: #333;
  }

  .header-right {
    .van-icon {
      font-size: 18px;
      color: #333;
    }
  }
}

// 标签页
.tabs-container {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;

  .tabs-wrapper {
    display: flex;
    padding: 0 16px;
    overflow-x: auto;

    .tab-item {
      flex-shrink: 0;
      padding: 12px 16px;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      position: relative;
      border-radius: 16px;
      margin-right: 8px;

      &.active {
        color: #333;
        background: #f0f0f0;
        font-weight: 500;
      }
    }
  }
}

// 工具栏
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;

  .view-options {
    display: flex;
    align-items: center;
    gap: 16px;

    .view-icon {
      font-size: 16px;
      color: #666;
    }
  }

  .sort-options {
    display: flex;
    align-items: center;
    gap: 16px;

    .sort-item {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #666;
      cursor: pointer;

      &.active {
        color: #333;
        font-weight: 500;
      }

      span {
        margin-right: 4px;
      }

      .van-icon {
        font-size: 12px;
      }
    }
  }
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background: #fff;
}

// 表头
.table-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;

  .header-rank {
    width: 30px;
    margin-right: 12px;
    font-size: 13px;
    color: #666;
    text-align: center;
  }

  .header-stock {
    flex: 1;
    font-size: 13px;
    color: #666;
    text-align: left;
  }

  .header-price {
    width: 80px;
    margin-right: 16px;
    font-size: 13px;
    color: #666;
    text-align: right;
  }

  .header-change {
    width: 70px;
    margin-right: 16px;
    font-size: 13px;
    color: #666;
    text-align: right;
  }

  .header-volume {
    width: 70px;
    margin-right: 16px;
    font-size: 13px;
    color: #666;
    text-align: right;
  }

  .header-amplitude {
    width: 60px;
    font-size: 13px;
    color: #666;
    text-align: right;
  }

  .sub-label {
    font-size: 10px;
    color: #999;
  }
}

// 股票列表
.stock-list {
  background: #fff;

    // 股票项
    .stock-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      background: #fff;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: #f9f9f9;
      }

      &:last-child {
        border-bottom: none;
      }

      // 排名
      .rank {
        width: 30px;
        font-size: 14px;
        font-weight: 600;
        color: #666;
        text-align: center;
        margin-right: 12px;
      }

      // 股票信息
      .stock-info {
        flex: 1;
        min-width: 0;

        .stock-name {
          font-size: 14px;
          font-weight: 600;
          color: #333;
          margin-bottom: 2px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .stock-code {
          font-size: 12px;
          color: #999;
        }
      }

      // 价格信息
      .price-info {
        width: 80px;
        text-align: right;
        margin-right: 16px;

        .current-price {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 2px;
        }

        .price-details {
          font-size: 12px;
          color: #999;

          .high-low {
            margin-left: 4px;
          }
        }
      }

      // 涨跌信息
      .change-info {
        width: 70px;
        text-align: right;
        margin-right: 16px;

        .change-percent {
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 2px;

          &.up {
            color: #ff4d4f;
          }

          &.down {
            color: #52c41a;
          }

          &.neutral {
            color: #333;
          }
        }

        .change-amount {
          font-size: 13px;
          font-weight: 500;

          &.up {
            color: #ff4d4f;
          }

          &.down {
            color: #52c41a;
          }

          &.neutral {
            color: #333;
          }
        }
      }

      // 成交信息
      .volume-info {
        width: 70px;
        text-align: right;
        margin-right: 16px;

        .volume {
          font-size: 12px;
          color: #999;
          margin-bottom: 2px;
        }

        .turnover {
          font-size: 13px;
          font-weight: 500;
          color: #333;
        }
      }

      // 振幅
      .amplitude-info {
        width: 60px;
        text-align: right;

        .amplitude {
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 2px;
          color: #333;
        }

        .amplitude-label {
          font-size: 12px;
          color: #999;
        }
      }
    }
}

// 空数据状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #fff;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    color: #333;
    margin-bottom: 8px;
  }

  .empty-subtext {
    font-size: 14px;
    color: #999;
  }
}

// 错误状态
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #fff;

  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .error-text {
    font-size: 16px;
    color: #ff4d4f;
    margin-bottom: 20px;
    text-align: center;
  }
}
</style>
