<template>
  <div class="futu-identity-selection">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left">
        <div class="back-icon" @click="goBack">‹</div>
        <span class="title">网上转账开户</span>
      </div>
      <div class="header-right">
        <div class="help-icon">🎧</div>
        <div class="more-icon">⋯</div>
        <div class="message-icon">
          <div class="message-icon-inner">✉️</div>
          <span class="badge">3</span>
        </div>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar">
      <div class="progress-fill" :style="{ width: '30%' }"></div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <h1 class="page-title">请选择您的身份信息</h1>

      <!-- 身份选项列表 -->
      <div class="identity-options">
        <div
          class="identity-option"
          :class="{ 'identity-selected': selectedIdentity === 'hongkong' }"
          @click="selectIdentity('hongkong')"
        >
          <div class="option-content">
            <div class="option-title">香港居民</div>
            <div class="option-description">持有香港永久/非永久居民身份证</div>
          </div>
          <div class="radio-button">
            <div class="radio-inner" v-if="selectedIdentity === 'hongkong'"></div>
          </div>
        </div>

        <div
          class="identity-option"
          :class="{ 'identity-selected': selectedIdentity === 'macau' }"
          @click="selectIdentity('macau')"
        >
          <div class="option-content">
            <div class="option-title">澳门居民</div>
          </div>
          <div class="radio-button">
            <div class="radio-inner" v-if="selectedIdentity === 'macau'"></div>
          </div>
        </div>

        <div
          class="identity-option"
          :class="{ 'identity-selected': selectedIdentity === 'other' }"
          @click="selectIdentity('other')"
        >
          <div class="option-content">
            <div class="option-title">其他国家/地区居民</div>
          </div>
          <div class="radio-button">
            <div class="radio-inner" v-if="selectedIdentity === 'other'"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-buttons">
      <button class="prev-btn" @click="goBack">
        上一步
      </button>
      <button
        class="next-btn"
        :class="{ disabled: !selectedIdentity }"
        @click="nextStep"
        :disabled="!selectedIdentity"
      >
        下一步
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FutuIdentitySelection',
  data () {
    return {
      selectedIdentity: 'hongkong' // 默认选择香港居民
    }
  },
  methods: {
    goBack () {
      this.$router.go(-1)
    },
    selectIdentity (identity) {
      this.selectedIdentity = identity
    },
    nextStep () {
      if (!this.selectedIdentity) return

      // 根据选择的身份跳转到对应页面
      if (this.selectedIdentity === 'hongkong') {
        this.$router.push('/open-account/upload-documents')
      } else {
        // 其他身份暂时跳转到相同页面
        this.$router.push('/open-account/upload-documents')
      }
    }
  }
}
</script>

<style scoped>
.futu-identity-selection {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: white;
  border-bottom: 1px solid #eee;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #333;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.help-icon, .more-icon, .message-icon-inner {
  font-size: 18px;
  cursor: pointer;
  color: #333;
}

.message-icon {
  position: relative;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-bar {
  height: 4px;
  background: #f0f0f0;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: #333;
  transition: width 0.3s ease;
}

.main-content {
  flex: 1;
  padding: 24px 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0 0 40px 0;
}

.identity-options {
  margin-bottom: 40px;
}

.identity-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  cursor: pointer;
  transition: border-color 0.3s ease;
  background: #fff;
  min-height: 80px;
  box-sizing: border-box;
}

.identity-option:hover {
  border-color: #333;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.option-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.radio-button {
  width: 24px;
  height: 24px;
  border: 2px solid #ddd;
  border-radius: 50%;
  position: relative;
  transition: border-color 0.3s ease;
}

.identity-option.identity-selected .radio-button {
  border-color: #333;
}

.radio-inner {
  width: 12px;
  height: 12px;
  background: #333;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.bottom-buttons {
  display: flex;
  gap: 12px;
  padding: 20px;
  background: white;
  border-top: 1px solid #eee;
}

.prev-btn {
  flex: 1;
  height: 50px;
  background: white;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.prev-btn:hover {
  background: #f8f8f8;
}

.next-btn {
  flex: 1;
  height: 50px;
  background: #333;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
}

.next-btn:hover:not(.disabled) {
  background: #555;
}

.next-btn.disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>
