<template>
  <div class="futu-investment-experience">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left">
        <div class="back-icon" @click="goBack">‹</div>
        <span class="title">网上转账开户</span>
      </div>
      <div class="header-right">
        <div class="help-icon">🎧</div>
        <div class="more-icon">⋯</div>
        <div class="message-icon">
          <div class="message-icon-inner">✉️</div>
          <span class="badge">3</span>
        </div>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar">
      <div class="progress-fill" :style="{ width: '80%' }"></div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <h1 class="page-title">填写个人信息</h1>
      <p class="subtitle">请根据实际情况填写以下信息</p>

      <!-- 职业状况 -->
      <div class="section">
        <h2 class="section-title">职业状况</h2>

        <div class="form-group">
          <label class="form-label">学历</label>
          <select v-model="formData.education" class="form-select">
            <option value="">请选择学历</option>
            <option value="高中或以下">高中或以下</option>
            <option value="大专">大专</option>
            <option value="学士">学士</option>
            <option value="硕士">硕士</option>
            <option value="博士">博士</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">职业</label>
          <select v-model="formData.occupation" class="form-select" @change="onOccupationChange">
            <option value="">请选择职业</option>
            <option value="在职人员">在职人员</option>
            <option value="退休人员">退休人员</option>
            <option value="学生">学生</option>
            <option value="无业">无业</option>
            <option value="家庭主妇/主夫">家庭主妇/主夫</option>
            <option value="自由职业者">自由职业者</option>
          </select>
        </div>

        <!-- 在职人员额外信息 -->
        <div v-if="formData.occupation === '在职人员'" class="employment-details">
          <div class="form-group">
            <label class="form-label">公司名称</label>
            <input
              type="text"
              v-model="formData.companyName"
              placeholder="请输入公司名称"
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label class="form-label">行业</label>
            <select v-model="formData.industry" class="form-select">
              <option value="">请选择行业</option>
              <option value="金融服务">金融服务</option>
              <option value="信息技术">信息技术</option>
              <option value="制造业">制造业</option>
              <option value="房地产">房地产</option>
              <option value="医疗保健">医疗保健</option>
              <option value="教育">教育</option>
              <option value="零售贸易">零售贸易</option>
              <option value="建筑工程">建筑工程</option>
              <option value="政府机关">政府机关</option>
              <option value="其他">其他</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">职位</label>
            <input
              type="text"
              v-model="formData.position"
              placeholder="请输入职位"
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label class="form-label">工作年限</label>
            <select v-model="formData.workYears" class="form-select">
              <option value="">请选择工作年限</option>
              <option value="1年以下">1年以下</option>
              <option value="1-3年">1-3年</option>
              <option value="3-5年">3-5年</option>
              <option value="5-10年">5-10年</option>
              <option value="10年以上">10年以上</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 财务状况 -->
      <div class="section">
        <h2 class="section-title">财务状况</h2>

        <div class="form-group">
          <label class="form-label">年收入（港币）</label>
          <select v-model="formData.annualIncome" class="form-select">
            <option value="">请选择年收入</option>
            <option value="30万以下">30万以下</option>
            <option value="30万-50万">30万-50万</option>
            <option value="50万-100万">50万-100万</option>
            <option value="100万-200万">100万-200万</option>
            <option value="200万以上">200万以上</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">净资产（港币）</label>
          <select v-model="formData.netWorth" class="form-select">
            <option value="">请选择净资产</option>
            <option value="50万以下">50万以下</option>
            <option value="50万-100万">50万-100万</option>
            <option value="100万-500万">100万-500万</option>
            <option value="500万-1000万">500万-1000万</option>
            <option value="1000万以上">1000万以上</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">资金来源</label>
          <select v-model="formData.fundSource" class="form-select">
            <option value="">请选择资金来源</option>
            <option value="工资收入">工资收入</option>
            <option value="投资收益">投资收益</option>
            <option value="商业经营">商业经营</option>
            <option value="继承或赠与">继承或赠与</option>
            <option value="其他">其他</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">预计投资金额（港币）</label>
          <select v-model="formData.investmentAmount" class="form-select">
            <option value="">请选择投资金额</option>
            <option value="10万以下">10万以下</option>
            <option value="10万-50万">10万-50万</option>
            <option value="50万-100万">50万-100万</option>
            <option value="100万-500万">100万-500万</option>
            <option value="500万以上">500万以上</option>
          </select>
        </div>
      </div>

      <!-- 投资经验 -->
      <div class="section">
        <h2 class="section-title">投资经验</h2>

        <div class="form-group">
          <label class="form-label">股票投资经验</label>
          <select v-model="formData.stockExperience" class="form-select">
            <option value="">请选择经验</option>
            <option value="无经验">无经验</option>
            <option value="1年以下">1年以下</option>
            <option value="1-3年">1-3年</option>
            <option value="3-5年">3-5年</option>
            <option value="5年以上">5年以上</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">期权投资经验</label>
          <select v-model="formData.optionExperience" class="form-select">
            <option value="">请选择经验</option>
            <option value="无经验">无经验</option>
            <option value="1年以下">1年以下</option>
            <option value="1-3年">1-3年</option>
            <option value="3-5年">3-5年</option>
            <option value="5年以上">5年以上</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">期货投资经验</label>
          <select v-model="formData.futureExperience" class="form-select">
            <option value="">请选择经验</option>
            <option value="无经验">无经验</option>
            <option value="1年以下">1年以下</option>
            <option value="1-3年">1-3年</option>
            <option value="3-5年">3-5年</option>
            <option value="5年以上">5年以上</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">投资目标</label>
          <select v-model="formData.investmentGoal" class="form-select">
            <option value="">请选择投资目标</option>
            <option value="资本保值">资本保值</option>
            <option value="稳健增长">稳健增长</option>
            <option value="积极增长">积极增长</option>
            <option value="投机获利">投机获利</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">风险承受能力</label>
          <select v-model="formData.riskTolerance" class="form-select">
            <option value="">请选择风险承受能力</option>
            <option value="保守型">保守型 - 不愿承受本金损失</option>
            <option value="稳健型">稳健型 - 可承受轻微本金损失</option>
            <option value="平衡型">平衡型 - 可承受适度本金损失</option>
            <option value="积极型">积极型 - 可承受较大本金损失</option>
            <option value="激进型">激进型 - 可承受重大本金损失</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">预期交易频率</label>
          <select v-model="formData.tradingFrequency" class="form-select">
            <option value="">请选择交易频率</option>
            <option value="很少交易">很少交易 - 每年1-2次</option>
            <option value="偶尔交易">偶尔交易 - 每季度1-2次</option>
            <option value="定期交易">定期交易 - 每月1-2次</option>
            <option value="频繁交易">频繁交易 - 每周多次</option>
            <option value="日内交易">日内交易 - 每日多次</option>
          </select>
        </div>
      </div>

      <!-- 投资知识确认 -->
      <div class="section">
        <h2 class="section-title">投资知识确认</h2>

        <div class="knowledge-check">
          <label class="checkbox-label">
            <input
              type="checkbox"
              v-model="formData.understandRisks"
              class="checkbox-input"
            />
            <span class="checkbox-custom"></span>
            我了解投资股票、期权、期货等金融产品存在风险，可能导致本金损失
          </label>
        </div>

        <div class="knowledge-check">
          <label class="checkbox-label">
            <input
              type="checkbox"
              v-model="formData.readDisclosure"
              class="checkbox-input"
            />
            <span class="checkbox-custom"></span>
            我已阅读并理解相关的风险披露声明和产品条款
          </label>
        </div>

        <div class="knowledge-check">
          <label class="checkbox-label">
            <input
              type="checkbox"
              v-model="formData.confirmAccuracy"
              class="checkbox-input"
            />
            <span class="checkbox-custom"></span>
            我确认以上填写的所有信息真实、准确、完整
          </label>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-container">
        <button
          class="submit-btn"
          :class="{ 'disabled': !canSubmit }"
          @click="nextStep"
          :disabled="!canSubmit"
        >
          下一步
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FutuInvestmentExperience',
  data () {
    return {
      formData: {
        // 职业状况
        education: '',
        occupation: '',
        companyName: '',
        industry: '',
        position: '',
        workYears: '',

        // 财务状况
        annualIncome: '',
        netWorth: '',
        fundSource: '',
        investmentAmount: '',

        // 投资经验
        stockExperience: '',
        optionExperience: '',
        futureExperience: '',
        investmentGoal: '',
        riskTolerance: '',
        tradingFrequency: '',

        // 投资知识确认
        understandRisks: false,
        readDisclosure: false,
        confirmAccuracy: false
      }
    }
  },
  computed: {
    canSubmit () {
      const basicFields = [
        this.formData.education,
        this.formData.occupation,
        this.formData.annualIncome,
        this.formData.netWorth,
        this.formData.fundSource,
        this.formData.investmentAmount,
        this.formData.stockExperience,
        this.formData.investmentGoal,
        this.formData.riskTolerance,
        this.formData.tradingFrequency
      ].every(field => field !== '')

      // 如果是在职人员，检查公司信息
      const employmentFields = this.formData.occupation === '在职人员'
        ? [this.formData.companyName, this.formData.industry, this.formData.position, this.formData.workYears].every(field => field !== '')
        : true

      const checkboxes = [
        this.formData.understandRisks,
        this.formData.readDisclosure,
        this.formData.confirmAccuracy
      ].every(checkbox => checkbox === true)

      return basicFields && employmentFields && checkboxes
    }
  },
  methods: {
    goBack () {
      this.$router.go(-1)
    },
    onOccupationChange () {
      // 清空就业相关字段当职业改变时
      if (this.formData.occupation !== '在职人员') {
        this.formData.companyName = ''
        this.formData.industry = ''
        this.formData.position = ''
        this.formData.workYears = ''
      }
    },
    nextStep () {
      if (!this.canSubmit) return


      // 跳转到税务信息页面
      this.$router.push('/open-account/tax-information')
    }
  }
}
</script>

<style scoped>
.futu-investment-experience {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部导航 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f5a 100%);
  color: white;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-icon {
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  padding: 4px;
}

.title {
  font-size: 18px;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.help-icon, .more-icon {
  font-size: 20px;
  cursor: pointer;
}

.message-icon {
  position: relative;
  cursor: pointer;
}

.message-icon-inner {
  font-size: 20px;
}

.badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: #ff3b30;
  color: white;
  border-radius: 10px;
  font-size: 12px;
  padding: 2px 6px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 进度条 */
.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  position: relative;
}

.progress-fill {
  height: 100%;
  background: white;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 主要内容 */
.main-content {
  padding: 24px 16px;
  max-width: 600px;
  margin: 0 auto;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  margin-bottom: 8px;
  text-align: center;
}

.subtitle {
  font-size: 16px;
  color: #666;
  text-align: center;
  margin-bottom: 32px;
}

/* 表单区块 */
.section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #ff6b35;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.form-input, .form-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  background: white;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #ff6b35;
  box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1);
}

.form-select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
}

/* 就业详情 */
.employment-details {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

/* 投资知识确认 */
.knowledge-check {
  margin-bottom: 16px;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 4px;
  background: white;
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
  transition: all 0.2s ease;
}

.checkbox-input:checked + .checkbox-custom {
  background: #ff6b35;
  border-color: #ff6b35;
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 提交按钮 */
.submit-container {
  text-align: center;
  margin-top: 32px;
}

.submit-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f5a 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 48px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
  min-width: 200px;
}

.submit-btn:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4);
}

.submit-btn.disabled {
  background: #ccc;
  cursor: not-allowed;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .main-content {
    padding: 20px 12px;
  }

  .section {
    padding: 20px 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .submit-btn {
    width: 100%;
    min-width: unset;
  }
}
</style>
