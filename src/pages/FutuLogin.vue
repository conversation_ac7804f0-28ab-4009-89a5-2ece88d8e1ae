<template lang="html">
  <div class="futu-login">
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left" @click="goBack">
        <span class="back-arrow">‹</span>
      </div>
      <div class="nav-right">
        <span class="help-link" @click="showHelp">Help</span>
      </div>
    </div>

    <!-- 登录内容 -->
    <div class="login-content">
      <!-- 标题 -->
      <h1 class="login-title">Login</h1>

      <!-- 标签切换 -->
      <div class="tab-switcher">
        <div class="tab-item active">
          <span class="tab-text">Email/Account ID</span>
          <div class="tab-underline"></div>
        </div>
      </div>

      <!-- 邮箱输入 -->
      <div class="input-section" v-if="!showPasswordStep">
        <div class="input-container">
          <input
            type="text"
            class="email-input"
            placeholder="Enter email or account ID"
            v-model="emailInput"
            @input="validateForm"
          >
        </div>
      </div>

      <!-- 密码输入（第二步显示） -->
      <div class="password-section" v-if="showPasswordStep">
        <h1 class="login-title">Enter Login Password</h1>

        <p class="login-info">
          Logging in with <span class="email-text">{{ emailInput }}</span>
        </p>

        <div class="password-input-container">
          <input
            :type="showPassword ? 'text' : 'password'"
            class="password-input"
            placeholder="Enter password"
            v-model="password"
            @input="validateForm"
          >
          <span class="password-toggle" @click="togglePassword">
            <i class="toggle-icon" :class="showPassword ? 'icon-eye-open' : 'icon-eye-close'"></i>
          </span>
        </div>

        <div class="forgot-password">
          <span class="forgot-link" @click="goToForgotPassword">Forgot Password</span>
        </div>
      </div>

      <!-- 协议文本 -->
      <div class="agreement-section" v-if="!showPasswordStep">
        <p class="agreement-text">
          By clicking Next, you agree to the
          <span class="link">Privacy Policy</span>
          and
          <span class="link">Terms of Service</span>
        </p>
      </div>

      <!-- 按钮 -->
      <div class="button-container">
        <button
          v-if="!showPasswordStep"
          class="next-button"
          :class="{ disabled: !canProceed }"
          @click="proceedToPassword"
          :disabled="!canProceed"
        >
          Next
        </button>

        <button
          v-else
          class="login-button"
          :class="{ disabled: !canLogin }"
          @click="handleLogin"
          :disabled="!canLogin"
        >
          Login
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="js">
import { Toast } from 'vant'
import { getResponseMsg } from '@/utils/utils'
import * as api from '@/axios/api'
import { handleLoginSuccess, validateEmail, validatePassword, validateLoginAccount } from '@/utils/auth'

export default {
  name: 'FutuLogin',
  data () {
    return {
      emailInput: '***********',
      password: '',
      showPassword: false,
      showPasswordStep: false,
      loginAttempts: 0
    }
  },
  computed: {
    canProceed () {
      return this.emailInput.trim().length > 0 && this.isValidLoginAccount(this.emailInput)
    },
    canLogin () {
      return this.password.length > 0
    }
  },

  mounted () {
    // 清理可能的递归重定向参数
    const redirect = this.$route.query.redirect
    if (redirect && (redirect.includes('/futu-login') || redirect.includes('redirect='))) {
      this.$router.replace('/futu-login')
    }
  },

  methods: {
    goBack () {
      if (this.showPasswordStep) {
        this.showPasswordStep = false
        this.password = ''
      } else {
        this.$router.push('/futu-welcome')
      }
    },

    showHelp () {
      // 显示帮助信息
              Toast('For help, please contact customer service')
    },

    validateForm () {
      // 实时验证表单
    },

    isValidEmail (email) {
      return validateEmail(email)
    },

    isValidLoginAccount (account) {
      return validateLoginAccount(account)
    },

    proceedToPassword () {
      if (!this.canProceed) return

      // 进入密码输入步骤
      this.showPasswordStep = true
    },

    togglePassword () {
      this.showPassword = !this.showPassword
    },

    goToForgotPassword () {
      this.$router.push({
        path: '/forget',
        query: {
          email: this.emailInput
        }
      })
    },

    async handleLogin () {
      if (!this.canLogin) return

      // 验证账号格式（邮箱或牛牛号）
      if (!this.isValidLoginAccount(this.emailInput)) {
        Toast.fail('Please enter a valid email or account ID format')
        return
      }

      // 验证密码
      const passwordValidation = validatePassword(this.password)
      if (!passwordValidation.isValid) {
        Toast.fail(passwordValidation.message)
        return
      }

      try {

        // 准备登录参数
        const loginParams = {
          phone: this.emailInput.trim(),
          userPwd: this.password
          // email: this.emailInput.trim() // 添加邮箱字段
        }

        // 调用登录API
        const response = await api.login(loginParams)


        // 检查响应格式
        if (response) {
          // 使用新的登录处理函数 - 传递完整的响应对象
          const success = handleLoginSuccess(response)

          if (success) {
            // 重置登录尝试次数
            this.loginAttempts = 0

            // 清空表单
            this.password = ''
            this.showPasswordStep = false
          } else {
            // 登录处理失败
            this.loginAttempts++
            this.password = ''
          }
        } else {
          // 响应格式错误
          this.loginAttempts++

          const errorMsg = getResponseMsg(response, '登录失败，请检查邮箱和密码')
          Toast.fail(errorMsg)

          this.password = ''
        }
      } catch (error) {
        this.loginAttempts++

        // 检查是否是网络错误
        if (error.message === 'Login required') {
          // 这是由拦截器抛出的，不需要额外处理
          return
        }

        const errorMsg = getResponseMsg(error.response, '网络错误，请检查网络连接')
        Toast.fail(errorMsg)

        // 清空密码
        this.password = ''
      } finally {
        Toast.clear()
      }
    }
  }
}
</script>

<style scoped>
.futu-login {
  min-height: 100vh;
  background: #fff;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  height: 44px;
}

.nav-left {
  cursor: pointer;
}

.back-arrow {
  font-size: 24px;
  color: #000;
  font-weight: 300;
}

.nav-right {

}

.help-link {
  font-size: 16px;
  color: #000;
  text-decoration: underline;
  cursor: pointer;
}

/* 登录内容 */
.login-content {
  padding: 40px 20px;
}

/* 标题 */
.login-title {
  font-size: 34px;
  font-weight: bold;
  margin-bottom: 40px;
  color: #000;
}

/* 标签切换器 */
.tab-switcher {
  display: flex;
  margin-bottom: 40px;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  position: relative;
  padding: 0 0 12px 0;
  margin-right: 40px;
  cursor: pointer;
}

.tab-item.active .tab-text {
  color: #FF6600;
  font-weight: 500;
}

.tab-text {
  font-size: 16px;
  color: #999;
}

.tab-underline {
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: #FF6600;
}

.tab-item:not(.active) .tab-underline {
  display: none;
}

/* 输入区域 */
.input-section {
  margin-bottom: 60px;
}

.input-container {
  margin-bottom: 20px;
}

.email-input {
  width: 100%;
  padding: 16px 0;
  border: none;
  border-bottom: 1px solid #E5E5E7;
  font-size: 16px;
  background: transparent;
  outline: none;
  color: #000;
}

.email-input:focus {
  border-bottom-color: #FF6600;
}

/* 密码步骤 */
.password-section {
  margin-top: -40px;
}

.login-info {
  font-size: 16px;
  color: #666;
  margin-bottom: 40px;
  line-height: 1.5;
}

.email-text {
  color: #000;
  font-weight: 500;
}

.password-input-container {
  position: relative;
  margin-bottom: 20px;
}

.password-input {
  width: 100%;
  padding: 16px 40px 16px 0;
  border: none;
  border-bottom: 1px solid #E5E5E7;
  font-size: 16px;
  background: transparent;
  outline: none;
  color: #000;
}

.password-input:focus {
  border-bottom-color: #FF6600;
}

.password-toggle {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-eye-close {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23666' viewBox='0 0 24 24'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E");
}

.icon-eye-open {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23666' viewBox='0 0 24 24'%3E%3Cpath d='M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z'/%3E%3C/svg%3E");
}

/* 忘记密码 */
.forgot-password {
  text-align: right;
  margin-bottom: 40px;
}

.forgot-link {
  color: #8E8E93;
  text-decoration: none;
  cursor: pointer;
  font-size: 14px;
}

.forgot-link:hover {
  text-decoration: underline;
}

/* 协议区域 */
.agreement-section {
  margin-bottom: 60px;
  text-align: center;
}

.agreement-text {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.link {
  color: #007AFF;
  text-decoration: none;
  cursor: pointer;
}

.link:hover {
  text-decoration: underline;
}

/* 按钮容器 */
.button-container {
  position: fixed;
  bottom: 40px;
  left: 20px;
  right: 20px;
}

/* 下一步按钮 */
.next-button {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  background: #000;
  cursor: pointer;
  transition: all 0.2s;
}

.next-button:hover:not(.disabled) {
  background: #333;
}

.next-button.disabled {
  background: #C7C7CC;
  cursor: not-allowed;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  background: #C7C7CC;
  cursor: pointer;
  transition: all 0.2s;
}

.login-button:not(.disabled) {
  background: #000;
}

.login-button:hover:not(.disabled) {
  background: #333;
}

.login-button.disabled {
  background: #C7C7CC;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .login-content {
    padding: 30px 16px;
  }

  .login-title {
    font-size: 28px;
    margin-bottom: 30px;
  }

  .button-container {
    left: 16px;
    right: 16px;
  }
}
</style>
