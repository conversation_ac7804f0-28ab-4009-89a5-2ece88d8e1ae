<template>
  <div class="futu-market">
    <!-- Top Navigation Bar -->
    <div class="market-header">
      <div class="header-left">
        <span class="market-title">Market</span>
      </div>
      <div class="header-right">
        <div class="search-icon" @click="goToSearch">
          <van-icon name="search" />
        </div>
        <div class="message-icon" @click="goToMessage">
          <van-icon name="envelop-o" />
          <span class="message-badge">1</span>
        </div>
      </div>
    </div>

    <!-- Market Notice Banner -->
    <div class="market-notice" v-if="showNotice">
      <div class="notice-content">
        <van-icon name="info-o" />
        <span>US Market Holiday Notice</span>
      </div>
      <van-icon name="cross" @click="closeNotice" />
    </div>

    <!-- Market Tabs -->
    <div class="market-tabs">
      <div
        class="tab-item"
        :class="{ active: activeTab === 'overview' }"
        @click="switchTab('overview')"
      >
        Overview
      </div>
      <div
        class="tab-item"
        :class="{ active: activeTab === 'us' }"
        @click="switchTab('us')"
      >
        US Stocks
      </div>
      <div class="tab-item crypto-tab">
        Crypto
        <span class="new-badge">NEW</span>
      </div>
      <div class="tab-item">ETF</div>
      <div class="tab-item">Options</div>
      <div class="tab-item">Futures</div>
    </div>

    <!-- Overview Content -->
    <div v-if="activeTab === 'overview'" class="overview-content">
      <!-- US Major Indices -->
      <div class="section">
        <div class="section-header">
          <h3>US Major Indices</h3>
          <van-icon name="arrow" />
        </div>
        <div class="indices-grid">
          <div
            class="index-card"
            v-for="(index, idx) in usIndices"
            :key="idx"
            @click="goToIndexDetail(index)"
          >
            <div class="index-info">
              <div class="index-name">{{ index.name }}</div>
              <div class="index-value" :class="index.changeType">
                {{ index.currentPrice || 'Loading...' }}
              </div>
              <div class="index-change" :class="index.changeType">
                {{ index.change || '--' }} {{ index.changePercent || '--' }}%
              </div>
            </div>
            <div class="mini-chart" :class="index.changeType">
              <svg width="60" height="30" viewBox="0 0 60 30">
                <polyline
                  :points="index.chartPoints"
                  fill="none"
                  :stroke="index.changeType === 'up' ? '#ff3b30' : '#34c759'"
                  stroke-width="1.5"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Market Status -->
      <div class="section">
        <div class="section-header">
          <h3>Market Status</h3>
        </div>
        <div class="market-status">
          <div class="status-item">
            <span class="status-label">Market:</span>
            <span class="status-value" :class="marketStatus.isOpen ? 'open' : 'closed'">
              {{ marketStatus.isOpen ? 'Open' : 'Closed' }}
            </span>
          </div>
          <div class="status-item">
            <span class="status-label">Next Open:</span>
            <span class="status-value">{{ marketStatus.nextOpen || 'Loading...' }}</span>
          </div>
        </div>
      </div>

      <!-- Top Gainers & Losers -->
      <div class="section">
        <div class="section-header">
          <h3>Market Movers</h3>
          <div class="arrow-right" @click="goToGainersLosers('gainers')">›</div>
        </div>
        <div class="movers-tabs">
          <div class="mover-tab" :class="{ active: activeMoversTab === 'gainers' }" @click="activeMoversTab = 'gainers'">
            Top Gainers
          </div>
          <div class="mover-tab" :class="{ active: activeMoversTab === 'losers' }" @click="activeMoversTab = 'losers'">
            Top Losers
          </div>
        </div>
        <div class="movers-list">
          <div
            class="mover-item"
            v-for="(stock, index) in activeMoversTab === 'gainers' ? gainersData.slice(0, 5) : losersData.slice(0, 5)"
            :key="stock.ticker"
            @click="goToStockDetail(stock)"
          >
            <div class="rank">{{ index + 1 }}</div>
            <div class="stock-info">
              <div class="stock-symbol">{{ stock.ticker }}</div>
              <div class="stock-name">{{ stock.name || stock.ticker }}</div>
            </div>
            <div class="stock-price">
              <div class="current-price">${{ stock.currentPrice || '--' }}</div>
              <div class="price-change" :class="stock.changeType">
                <span>{{ stock.change || '--' }}</span>
                <span>{{ stock.changePercent || '--' }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- US Stocks Content -->
    <div v-if="activeTab === 'us'" class="us-content">
      <!-- Major Indices -->
      <div class="indices-section">
        <div class="index-card" v-for="index in usIndices" :key="index.symbol">
          <div class="index-name">{{ index.name }}</div>
          <div class="index-price" :class="index.changeType">
            {{ index.currentPrice || 'Loading...' }}
          </div>
          <div class="index-change" :class="index.changeType">
            {{ index.change || '--' }} {{ index.changePercent || '--' }}%
          </div>
          <div class="mini-chart">
            <canvas :ref="'chart-' + index.symbol" width="100" height="30"></canvas>
          </div>
        </div>
      </div>

      <!-- Market Movers -->
      <div class="section">
        <div class="section-header">
          <h3>Market Movers</h3>
          <div class="arrow-right" @click="goToGainersLosers('gainers')">›</div>
        </div>
        <div class="feature-tabs">
          <div class="feature-tab" :class="{ active: activeMoversTab === 'gainers' }" @click="setActiveMoversTab('gainers')">
            Top Gainers
          </div>
          <div class="feature-tab" :class="{ active: activeMoversTab === 'losers' }" @click="setActiveMoversTab('losers')">
            Top Losers
          </div>
        </div>
        <div class="feature-list">
          <div
            class="feature-item"
            v-for="(stock, index) in activeMoversTab === 'gainers' ? gainersData.slice(0, 10) : losersData.slice(0, 10)"
            :key="stock.ticker"
            @click="goToStockDetail(stock)"
          >
            <div class="rank">{{ index + 1 }}</div>
            <div class="stock-info">
              <div class="stock-name">{{ stock.name || stock.ticker }}</div>
              <div class="stock-symbol">{{ stock.ticker }}</div>
            </div>
            <div class="stock-price">${{ stock.currentPrice || '--' }}</div>
            <div class="stock-change" :class="stock.changeType">{{ stock.changePercent || '--' }}%</div>
          </div>
        </div>
      </div>

      <!-- All Stocks -->
      <div class="section">
        <div class="section-header">
          <h3>All Stocks</h3>
          <div class="arrow-right">›</div>
        </div>
        <div class="stock-tabs">
          <div class="stock-tab active">All</div>
          <div class="stock-tab">Dow Jones</div>
          <div class="stock-tab">NASDAQ 100</div>
          <div class="stock-tab">S&P 500</div>
        </div>
        <div class="stock-list">
          <div class="stock-item" v-for="stock in usStockList.slice(0, 20)" :key="stock.ticker" @click="goToStockDetail(stock)">
            <div class="stock-info">
              <div class="stock-name">{{ stock.name || stock.ticker }}</div>
              <div class="stock-symbol">{{ stock.ticker }}</div>
              <div class="stock-exchange">{{ stock.primary_exchange }}</div>
            </div>
            <div class="stock-price">
              <div class="current-price">${{ stock.currentPrice || '--' }}</div>
              <div class="price-change" :class="stock.changeType">
                <span>{{ stock.change > 0 ? '+' : '' }}{{ stock.change || '--' }}</span>
                <span>{{ stock.changePercent > 0 ? '+' : '' }}{{ stock.changePercent || '--' }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <FutuTabbar />
  </div>
</template>

<script>
import FutuTabbar from '@/components/FutuTabbar.vue'
import {
  // Polygon API
  getTickers,
  getTickerSnapshot,
  getLatestPrices,
  getAllTickersSnapshot,
  getGainersSnapshot,
  getLosersSnapshot,
  // Stock API
  getMarket
} from '@/axios/api'

export default {
  name: 'FutuMarket',
  components: {
    FutuTabbar
  },
  data () {
    return {
      activeTab: 'overview',
      activeMoversTab: 'gainers',
      showNotice: true,
      refreshTimer: null,

      // US Major Indices - Real data from Polygon API
      usIndices: [
        {
          name: 'Dow Jones',
          symbol: 'DJI',
          currentPrice: 'Loading...',
          change: '--',
          changePercent: '--',
          changeType: 'neutral',
          chartPoints: '0,20 10,15 20,25 30,10 40,5 50,15 60,8'
        },
        {
          name: 'NASDAQ',
          symbol: 'IXIC',
          currentPrice: 'Loading...',
          change: '--',
          changePercent: '--',
          changeType: 'neutral',
          chartPoints: '0,25 10,20 20,15 30,18 40,12 50,8 60,5'
        },
        {
          name: 'S&P 500',
          symbol: 'SPX',
          currentPrice: 'Loading...',
          change: '--',
          changePercent: '--',
          changeType: 'neutral',
          chartPoints: '0,22 10,18 20,20 30,15 40,10 50,12 60,7'
        }
      ],

      // Market Status
      marketStatus: {
        isOpen: false,
        nextOpen: 'Loading...'
      },

      // Market Movers - Real data from Polygon API
      gainersData: [],
      losersData: [],

      // US Stock List - Real data from Polygon API
      usStockList: [],

      // Loading states
      polygonLoading: {
        indices: false,
        gainers: false,
        losers: false,
        stocks: false
      }
    }
  },

  mounted () {
    this.loadMarketData()
  },

  beforeDestroy () {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  },

  updated () {
    if (this.activeTab === 'us') {
      this.$nextTick(() => {
        this.drawUSCharts()
      })
    }
  },

  methods: {
    // Switch tabs
    switchTab (tab) {
      this.activeTab = tab
      if (tab === 'us') {
        this.$nextTick(() => {
          this.drawUSCharts()
        })
      }
    },

    // Set active movers tab
    setActiveMoversTab (tab) {
      this.activeMoversTab = tab
    },

    // Draw US charts
    drawUSCharts () {
      this.usIndices.forEach(index => {
        const canvas = this.$refs['chart-' + index.symbol]
        if (canvas && canvas[0]) {
          const ctx = canvas[0].getContext('2d')
          this.drawMiniChart(ctx, index.changeType === 'up')
        }
      })
    },

    drawMiniChart (ctx, isPositive) {
      const color = isPositive ? '#FF1744' : '#00C853'
      ctx.strokeStyle = color
      ctx.lineWidth = 1
      ctx.beginPath()

      const points = [
        [5, 20], [15, 18], [25, 22], [35, 15], [45, 25],
        [55, 20], [65, 18], [75, 22], [85, 19], [95, 16]
      ]

      ctx.moveTo(points[0][0], points[0][1])
      points.forEach(point => {
        ctx.lineTo(point[0], point[1])
      })
      ctx.stroke()
    },

    // Close notice
    closeNotice () {
      this.showNotice = false
    },

    // Load market data
    async loadMarketData () {
      try {
        // Call getMarket API
        await this.callGetMarketAPI()

        // Load US indices data
        await this.loadUSIndicesData()

        // Load market movers data
        await this.loadMarketMoversData()

        // Load US stocks data
        await this.loadUSStocksData()

        // Start auto refresh
        this.startRealTimeRefresh()
      } catch (error) {
        console.error('Failed to load market data:', error)
        this.handleDataLoadError(error)
      }
    },

    // Call getMarket API
    async callGetMarketAPI () {
      try {
        console.log('Calling /api/stock/getMarket.do API...')
        const result = await getMarket()
        console.log('getMarket API response:', result)
      } catch (error) {
        console.error('Failed to call getMarket API:', error)
      }
    },

                // Load US indices data using Polygon API getTickerSnapshot
    async loadUSIndicesData () {
      this.polygonLoading.indices = true

      try {
        console.log('Loading US indices data using getTickerSnapshot...')

        // Method 1: Try individual getTickerSnapshot calls
        const promises = this.usIndices.map(index => {
          const params = { ticker: index.symbol }
          console.log(`Loading snapshot for ${index.name} (${index.symbol}) with params:`, params)
          return getTickerSnapshot(params)
        })

        const results = await Promise.all(promises)
        let successCount = 0

        results.forEach((result, index) => {
          console.log(`Snapshot result for ${this.usIndices[index].name}:`, result)

          if (result && result.success && result.data && result.data.results) {
            const snapshot = result.data.results

            // Extract price information from snapshot
            const currentPrice = snapshot.value || snapshot.lastQuote.p || snapshot.lastTrade.p
            const changeValue = snapshot.todaysChange || 0
            const changePercent = snapshot.todaysChangePerc || 0

            console.log(`${this.usIndices[index].name} - Current: ${currentPrice}, Change: ${changeValue}, Change%: ${changePercent}`)

            this.usIndices[index] = {
              ...this.usIndices[index],
              currentPrice: currentPrice ? currentPrice.toFixed(2) : 'Loading...',
              change: changeValue ? (changeValue >= 0 ? `+${changeValue.toFixed(2)}` : changeValue.toFixed(2)) : '--',
              changePercent: changePercent ? (changePercent >= 0 ? `+${changePercent.toFixed(2)}` : changePercent.toFixed(2)) : '--',
              changeType: changeValue > 0 ? 'up' : (changeValue < 0 ? 'down' : 'neutral'),
              chartPoints: this.generateChartPoints(changeValue >= 0)
            }
            successCount++
          } else {
            console.warn(`No valid data for ${this.usIndices[index].name}`)
            this.usIndices[index] = {
              ...this.usIndices[index],
              currentPrice: 'No Data',
              change: '--',
              changePercent: '--',
              changeType: 'neutral'
            }
          }
        })

        // Method 2: If individual calls failed, try batch getLatestPrices
        if (successCount === 0) {
          console.log('Individual snapshot calls failed, trying batch getLatestPrices...')
          await this.loadUSIndicesDataBatch()
        }

      } catch (error) {
        console.error('Failed to load US indices data:', error)

        // Try batch method as fallback
        try {
          console.log('Trying batch method as fallback...')
          await this.loadUSIndicesDataBatch()
        } catch (batchError) {
          console.error('Batch method also failed:', batchError)
          // Set error state for display
          this.usIndices.forEach((index, i) => {
            this.usIndices[i] = {
              ...this.usIndices[i],
              currentPrice: 'API Error',
              change: '--',
              changePercent: '--',
              changeType: 'neutral'
            }
          })

          // Show error message to user
          if (this.$toast) {
            this.$toast('Failed to load market data. Please check your connection.')
          }
        }
      } finally {
        this.polygonLoading.indices = false
      }
    },

    // Batch method using getLatestPrices
    async loadUSIndicesDataBatch () {
      const symbols = this.usIndices.map(index => index.symbol).join(',')
      console.log('Loading batch prices for symbols:', symbols)

      const result = await getLatestPrices({ symbols })
      console.log('Batch prices result:', result)

      if (result && result.success && result.data) {
        this.usIndices.forEach((index, i) => {
          const priceData = result.data[index.symbol]
          if (priceData) {
            const currentPrice = priceData.price || priceData.value
            const changeValue = priceData.change || 0
            const changePercent = priceData.changePercent || 0

            this.usIndices[i] = {
              ...this.usIndices[i],
              currentPrice: currentPrice ? currentPrice.toFixed(2) : 'Loading...',
              change: changeValue ? (changeValue >= 0 ? `+${changeValue.toFixed(2)}` : changeValue.toFixed(2)) : '--',
              changePercent: changePercent ? (changePercent >= 0 ? `+${changePercent.toFixed(2)}` : changePercent.toFixed(2)) : '--',
              changeType: changeValue > 0 ? 'up' : (changeValue < 0 ? 'down' : 'neutral'),
              chartPoints: this.generateChartPoints(changeValue >= 0)
            }
          }
        })
      }
    },

    // Load market movers data
    async loadMarketMoversData () {
      try {
        // Load gainers and losers in parallel
        const [gainersResponse, losersResponse] = await Promise.all([
          getGainersSnapshot({ include_otc: false }),
          getLosersSnapshot({ include_otc: false })
        ])

        // Process gainers data
        if (gainersResponse && gainersResponse.success && gainersResponse.data && gainersResponse.data.tickers) {
          this.gainersData = gainersResponse.data.tickers.map(stock => {
            const changePercent = stock.todaysChangePerc || 0
            return {
              ticker: stock.ticker,
              name: stock.ticker,
              currentPrice: stock.lastTrade ? stock.lastTrade.p.toFixed(2) : '--',
              change: stock.todaysChange ? stock.todaysChange.toFixed(2) : '--',
              changePercent: changePercent ? changePercent.toFixed(2) : '--',
              changeType: changePercent > 0 ? 'up' : (changePercent < 0 ? 'down' : 'neutral')
            }
          })
        }

        // Process losers data
        if (losersResponse && losersResponse.success && losersResponse.data && losersResponse.data.tickers) {
          this.losersData = losersResponse.data.tickers.map(stock => {
            const changePercent = stock.todaysChangePerc || 0
            return {
              ticker: stock.ticker,
              name: stock.ticker,
              currentPrice: stock.lastTrade ? stock.lastTrade.p.toFixed(2) : '--',
              change: stock.todaysChange ? stock.todaysChange.toFixed(2) : '--',
              changePercent: changePercent ? changePercent.toFixed(2) : '--',
              changeType: changePercent > 0 ? 'up' : (changePercent < 0 ? 'down' : 'neutral')
            }
          })
        }
      } catch (error) {
        console.error('Failed to load market movers data:', error)
      }
    },

    // Load US stocks data
    async loadUSStocksData () {
      this.polygonLoading.stocks = true

      try {
        const response = await getTickers({
          market: 'stocks',
          active: true,
          sort: 'ticker',
          order: 'asc',
          limit: 100
        })

        if (response && response.success && response.data && response.data.results) {
          this.usStockList = response.data.results.map(stock => ({
            ticker: stock.ticker,
            name: stock.name,
            primary_exchange: stock.primary_exchange,
            type: stock.type,
            market: stock.market,
            currentPrice: '--',
            change: '--',
            changePercent: '--',
            changeType: 'neutral'
          }))

          // Load price data for the stocks
          await this.loadStockPrices()
        }
      } catch (error) {
        console.error('Failed to load US stocks data:', error)
      } finally {
        this.polygonLoading.stocks = false
      }
    },

    // Load stock prices using snapshot API
    async loadStockPrices () {
      try {
        const response = await getAllTickersSnapshot({ include_otc: false })

        if (response && response.success && response.data && response.data.tickers) {
          const snapshotMap = {}
          response.data.tickers.forEach(snapshot => {
            snapshotMap[snapshot.ticker] = snapshot
          })

          this.usStockList = this.usStockList.map(stock => {
            const snapshot = snapshotMap[stock.ticker]
            if (snapshot) {
              const changePercent = snapshot.todaysChangePerc || 0
              return {
                ...stock,
                currentPrice: snapshot.lastTrade ? snapshot.lastTrade.p.toFixed(2) : '--',
                change: snapshot.todaysChange ? snapshot.todaysChange.toFixed(2) : '--',
                changePercent: changePercent ? changePercent.toFixed(2) : '--',
                changeType: changePercent > 0 ? 'up' : (changePercent < 0 ? 'down' : 'neutral')
              }
            }
            return stock
          })
        }
      } catch (error) {
        console.error('Failed to load stock prices:', error)
      }
    },

        // Generate chart points from real data
    generateChartPointsFromData (aggregateData) {
      if (!aggregateData || aggregateData.length === 0) {
        return this.generateChartPoints(true)
      }

      // Use last 7 trading days for the mini chart, but sample from more data if available
      let dataPoints
      if (aggregateData.length > 7) {
        // Sample 7 points evenly distributed across the data range
        const stepSize = Math.floor(aggregateData.length / 7)
        dataPoints = []
        for (let i = 0; i < 7; i++) {
          const index = i * stepSize
          if (index < aggregateData.length) {
            dataPoints.push(aggregateData[index])
          }
        }
        dataPoints.reverse() // Reverse to show chronological order (oldest to newest)
      } else {
        dataPoints = aggregateData.slice().reverse() // Reverse to show chronological order
      }

      const points = []

      if (dataPoints.length === 0) {
        return this.generateChartPoints(true)
      }

      // Find min and max close prices for scaling
      const closePrices = dataPoints.map(d => d.c)
      const minPrice = Math.min(...closePrices)
      const maxPrice = Math.max(...closePrices)
      const priceRange = maxPrice - minPrice || 1 // Avoid division by zero

      dataPoints.forEach((data, index) => {
        const x = index * (60 / Math.max(dataPoints.length - 1, 1)) // Distribute across 60px width
        // Scale price to fit in 5-35 pixel range
        const y = 35 - ((data.c - minPrice) / priceRange) * 30
        points.push(`${x},${Math.max(5, Math.min(35, y))}`)
      })

      return points.join(' ')
    },

    // Generate chart points (fallback for when no data available)
    generateChartPoints (isUp = true) {
      const baseY = isUp ? 15 : 25
      const points = []
      for (let i = 0; i < 7; i++) {
        const x = i * 10
        const y = baseY + (Math.random() - 0.5) * 10 * (isUp ? 1 : -1)
        points.push(`${x},${Math.max(5, Math.min(35, y))}`)
      }
      return points.join(' ')
    },

    // Start real-time refresh
    startRealTimeRefresh () {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
      }

      this.refreshTimer = setInterval(() => {
        this.loadUSIndicesData()
        this.loadMarketMoversData()
      }, 60000) // Refresh every 60 seconds for aggregates data
    },

    // Handle data load error
    handleDataLoadError (error) {
      if (this.$toast) {
        this.$toast('Market data load failed, please check network connection')
      }
    },

    // Navigation methods
    goToSearch () {
      this.$router.push('/search')
    },

    goToGainersLosers (tab = 'gainers') {
      this.$router.push({
        path: '/gainers-losers',
        query: { tab }
      })
    },

    goToMessage () {
      this.$router.push('/message')
    },

    goToIndexDetail (index) {
      this.$router.push({
        path: '/stock-detail',
        query: {
          symbol: index.symbol,
          name: index.name,
          price: index.currentPrice
        }
      })
    },

    goToStockDetail (stock) {
      this.$router.push({
        path: '/stock-detail',
        query: {
          symbol: stock.ticker || stock.symbol,
          name: stock.name,
          price: stock.currentPrice || stock.price,
          exchange: stock.primary_exchange,
          type: stock.type,
          market: stock.market
        }
      })
    }
  }
}
</script>

<style scoped>
.futu-market {
  min-height: 100vh;
  background: #F5F5F5;
  padding-bottom: 80px;
}

/* Top Navigation Bar */
.market-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #E5E5E5;
}

.header-left .market-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-icon, .message-icon {
  position: relative;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #FF3B30;
  color: white;
  border-radius: 8px;
  padding: 1px 4px;
  font-size: 10px;
  min-width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-right .van-icon {
  font-size: 20px;
  color: #666;
}

/* Market Notice Banner */
.market-notice {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: #FFF3E0;
  border-bottom: 1px solid #E5E5E5;
}

.notice-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #FF8F00;
}

.notice-content .van-icon {
  font-size: 14px;
}

/* Market Tabs */
.market-tabs {
  display: flex;
  background: #fff;
  padding: 0 16px;
  border-bottom: 1px solid #E5E5E5;
  overflow-x: auto;
}

.tab-item {
  padding: 12px 16px;
  font-size: 14px;
  color: #666;
  position: relative;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tab-item.active {
  color: #00D4AA;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: #00D4AA;
  border-radius: 1px;
}

.crypto-tab {
  position: relative;
}

.new-badge {
  background: #FF3B30;
  color: white;
  font-size: 8px;
  padding: 1px 4px;
  border-radius: 2px;
  position: absolute;
  top: 6px;
  right: 8px;
}

/* Overview Content */
.overview-content {
  padding-bottom: 20px;
}

/* Section Styles */
.section {
  background: #fff;
  margin: 8px 0;
  padding: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.section-header .van-icon, .arrow-right {
  font-size: 12px;
  color: #999;
  cursor: pointer;
}

/* Indices Grid */
.indices-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.index-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #F8F9FA;
  border-radius: 8px;
  cursor: pointer;
}

.index-info {
  flex: 1;
}

.index-info .index-name {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.index-info .index-value {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.index-info .index-change {
  font-size: 11px;
  display: flex;
  gap: 4px;
}

.mini-chart {
  width: 60px;
  height: 30px;
  margin-left: 8px;
}

/* Market Status */
.market-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-size: 14px;
  color: #666;
}

.status-value {
  font-size: 14px;
  font-weight: 600;
}

.status-value.open {
  color: #34C759;
}

.status-value.closed {
  color: #FF3B30;
}

/* Movers Tabs */
.movers-tabs, .feature-tabs, .stock-tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.mover-tab, .feature-tab, .stock-tab {
  padding: 8px 16px;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  cursor: pointer;
}

.mover-tab.active, .feature-tab.active, .stock-tab.active {
  color: #333;
  border-bottom-color: #333;
  font-weight: 600;
}

/* Movers List */
.movers-list, .feature-list, .stock-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mover-item, .feature-item, .stock-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.mover-item:hover, .feature-item:hover, .stock-item:hover {
  background-color: #f8f9fa;
}

.mover-item:last-child, .feature-item:last-child, .stock-item:last-child {
  border-bottom: none;
}

.rank {
  width: 24px;
  height: 24px;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-right: 12px;
}

.stock-info {
  flex: 1;
}

.stock-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.stock-symbol {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.stock-exchange {
  font-size: 10px;
  color: #ccc;
  background: #f5f5f5;
  padding: 1px 4px;
  border-radius: 2px;
  display: inline-block;
}

.stock-price {
  text-align: right;
  margin-right: 12px;
}

.current-price {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.price-change {
  font-size: 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.price-change span {
  margin-bottom: 1px;
}

/* Color Classes */
.up {
  color: #FF3B30;
}

.down {
  color: #34C759;
}

.neutral {
  color: #999;
}

.price-change.up {
  color: #ff4444;
}

.price-change.down {
  color: #00aa3b;
}

.price-change.neutral {
  color: #999;
}

/* US Content */
.us-content {
  background: #f5f5f5;
}

.us-content .indices-section {
  display: flex;
  padding: 16px;
  gap: 12px;
  background: white;
  margin-bottom: 8px;
}

.us-content .index-card {
  flex: 1;
  background: #f8f8f8;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
}

.us-content .index-name {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.us-content .index-price {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.us-content .index-change {
  font-size: 12px;
  margin-bottom: 8px;
}

.us-content .mini-chart {
  height: 30px;
}

.stock-change {
  font-size: 14px;
  font-weight: 600;
  margin-left: 8px;
}
</style>
