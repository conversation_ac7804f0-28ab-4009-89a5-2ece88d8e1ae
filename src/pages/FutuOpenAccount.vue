<template>
  <div class="futu-open-account">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left">
        <div class="logo-icon">
          <img src="/static/img/favicon-futunn.ico" alt="Futu Logo" />
        </div>
        <span class="title">Accounts</span>
      </div>
      <div class="header-right">
        <van-icon name="search" class="search-icon" />
        <div class="message-icon">
          <van-icon name="envelop-o" class="message-icon-inner" />
          <span class="badge">3</span>
        </div>
      </div>
    </div>

    <!-- 开户步骤指示器 -->
    <div class="steps-indicator">
      <div class="progress-container">
        <div class="progress-line">
          <div class="progress-fill" :style="{ width: progressWidth }"></div>
        </div>
        <div class="steps-wrapper">
          <div class="step active">
            <div class="step-icon">
              <van-icon name="records-o" size="16px" />
            </div>
            <div class="step-text">Open Account</div>
          </div>
          <div class="step">
            <div class="step-icon">
              <van-icon name="balance-list-o" size="16px" />
            </div>
            <div class="step-text">Deposit</div>
          </div>
          <div class="step">
            <div class="step-icon">
              <van-icon name="chart-trending-o" size="16px" />
            </div>
            <div class="step-text">Start Investing</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <div class="title-section">
        <h1>Invest in 30+ Categories<br>Across Global Markets</h1>
      </div>

      <!-- 特色介绍 -->
      <div class="features">
        <div class="feature-item">
          <div class="feature-icon">
            <van-icon name="medal-o" size="20px" />
          </div>
          <div class="feature-text">Listed on NASDAQ</div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">
            <van-icon name="fire-o" size="20px" />
          </div>
          <div class="feature-text">No.1 retail broker in HK</div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">
            <van-icon name="diamond-o" size="20px" />
          </div>
          <div class="feature-text">HK$500,000 ICF compensation</div>
        </div>
      </div>

      <!-- 继续开户按钮 -->
      <div class="action-section">
        <button class="continue-btn" @click="goToOpenAccountSteps">
          Continue Account Opening
        </button>
      </div>

    </div>

    <!-- 底部说明 -->
    <div class="footer-note">
      <p>Trading services provided by <span class="company-name">Futu Securities International (Hong Kong) Limited</span></p>
    </div>

    <!-- 底部导航 -->
    <FutuTabbar />
  </div>
</template>

<script>
import FutuTabbar from '@/components/FutuTabbar.vue'

export default {
  name: 'FutuOpenAccount',
  components: {
    FutuTabbar
  },
  data () {
    return {
      currentStep: 1, // 当前步骤，1=开户，2=存款，3=开始投资
      progressWidth: '0%'
    }
  },
  mounted () {
    // 设置当前步骤的进度
    this.updateProgress()
  },
  methods: {
    updateProgress () {
      // 根据当前步骤设置进度条宽度
      if (this.currentStep === 1) {
        this.progressWidth = '0%'
      } else if (this.currentStep === 2) {
        this.progressWidth = '50%'
      } else if (this.currentStep === 3) {
        this.progressWidth = '100%'
      }
    },
    goToOpenAccountSteps () {
      this.$router.push('/open-account/steps')
    }
  }
}
</script>

<style scoped>
.futu-open-account {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 80px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: white;
  border-bottom: 1px solid #eee;
}

.header-left {
  display: flex;
  align-items: center;
}

.futu-logo {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-icon, .message-icon-inner {
  font-size: 20px;
  color: #666;
  cursor: pointer;
}

.message-icon {
  position: relative;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.steps-indicator {
  padding: 30px 20px;
  background: #fff;
}

.progress-container {
  position: relative;
  width: 100%;
  margin: 0 auto;
}

.progress-line {
  position: absolute;
  top: 16px;
  left: 16px;
  right: 16px;
  height: 2px;
  background: #E5E5E5;
  border-radius: 1px;
}

.progress-fill {
  height: 100%;
  width: 0%;
  background: #FF6B35;
  border-radius: 1px;
  transition: width 0.3s ease;
}

.steps-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 1;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
}

.step-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #F5F5F5;
  border: 2px solid #E5E5E5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
}

.step-icon .van-icon {
  color: #999;
}

.step.active .step-icon {
  background: #FF6B35;
  border-color: #FF6B35;
}

.step.active .step-icon .van-icon {
  color: white;
}

.step-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.step.active .step-text {
  color: #FF6B35;
  font-weight: 500;
}

.main-content {
  padding: 20px;
}

.title-section {
  text-align: center;
  margin-bottom: 40px;
}

.title-section h1 {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.features {
  margin-bottom: 60px;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px 0 100px;
}

.feature-icon {
  width: 24px;
  height: 24px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon .van-icon {
  color: #333;
}

.feature-text {
  font-size: 16px;
  color: #333;
  line-height: 1.4;
}

.action-section {
  padding: 0 20px;
  margin-bottom: 40px;
}

.continue-btn {
  width: 100%;
  height: 50px;
  background: #000;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
}

.continue-btn:hover {
  background: #333;
}

.footer-note {
  text-align: center;
  padding: 20px;
  background: #fff;
}

.footer-note p {
  font-size: 12px;
  color: #999;
  margin: 0;
}

.company-name {
  color: #FF6B35;
}
.logo-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>
