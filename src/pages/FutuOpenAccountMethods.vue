<template>
  <div class="futu-open-account-methods">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left">
        <div class="back-icon" @click="goBack">‹</div>
        <span class="title">网上转账开户</span>
      </div>
      <div class="header-right">
        <div class="help-icon">🎧</div>
        <div class="more-icon">⋯</div>
        <div class="message-icon">
          <div class="message-icon-inner">✉️</div>
          <span class="badge">3</span>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <h1 class="page-title">请选择开户方式</h1>

      <!-- 开户方式列表 -->
      <div class="methods-list">
        <div class="method-item recommended" @click="selectMethod('online')">
          <div class="method-icon">💳</div>
          <div class="method-content">
            <div class="method-title">
              网上转账
              <span class="recommended-badge">推荐</span>
            </div>
            <div class="method-description">香港银行账户转账一万港币</div>
          </div>
          <div class="arrow-icon">›</div>
        </div>

        <div class="method-item" @click="selectMethod('postal')">
          <div class="method-icon">💰</div>
          <div class="method-content">
            <div class="method-title">邮寄支票</div>
            <div class="method-description">寄送一万港币的香港银行账户支票</div>
          </div>
          <div class="arrow-icon">›</div>
        </div>

        <div class="method-item" @click="selectMethod('appointment')">
          <div class="method-icon">📅</div>
          <div class="method-content">
            <div class="method-title">预约开户</div>
            <div class="method-description">香港地区一对一服务，零门槛开户</div>
          </div>
          <div class="arrow-icon">›</div>
        </div>

        <div class="method-item" @click="selectMethod('witness')">
          <div class="method-icon">👤</div>
          <div class="method-content">
            <div class="method-title">第三方见证</div>
            <div class="method-description">香港以外地区见证开户</div>
          </div>
          <div class="arrow-icon">›</div>
        </div>

        <div class="method-item" @click="selectMethod('mainland')">
          <div class="method-icon">🏛️</div>
          <div class="method-content">
            <div class="method-title">内地客户开户</div>
            <div class="method-description">在境外工作或生活的内地籍人士</div>
          </div>
          <div class="arrow-icon">›</div>
        </div>
      </div>

      <!-- 底部说明 -->
      <div class="footer-note">
        <p>证券相关服务由富途证券(香港)提供</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FutuOpenAccountMethods',
  methods: {
    goBack () {
      this.$router.go(-1)
    },
    selectMethod (method) {
      // 这里可以根据不同的方式跳转到不同的页面
      switch (method) {
        case 'online':
          this.$router.push('/open-account/online-transfer')
          break
        case 'postal':
          this.$router.push('/open-account/postal-check')
          break
        case 'appointment':
          this.$router.push('/open-account/appointment')
          break
        case 'witness':
          this.$router.push('/open-account/witness')
          break
        case 'mainland':
          this.$router.push('/open-account/mainland')
          break
        default:
      }
    }
  }
}
</script>

<style scoped>
.futu-open-account-methods {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 80px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: white;
  border-bottom: 1px solid #eee;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #333;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.help-icon, .more-icon, .message-icon-inner {
  font-size: 18px;
  cursor: pointer;
  color: #333;
}

.message-icon {
  position: relative;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-content {
  padding: 20px;
}

.page-title {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin: 0 0 30px 0;
}

.methods-list {
  margin-bottom: 40px;
}

.method-item {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background 0.2s;
}

.method-item:hover {
  background: #f8f8f8;
}

.method-item:last-child {
  border-bottom: none;
}

.method-item.recommended {
  position: relative;
}

.method-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 16px;
  flex-shrink: 0;
}

.method-content {
  flex: 1;
}

.method-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
}

.recommended-badge {
  background: #FF6B35;
  color: white;
  font-size: 10px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

.method-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.arrow-icon {
  font-size: 18px;
  color: #ccc;
  margin-left: 12px;
}

.footer-note {
  text-align: center;
  padding: 20px 0;
}

.footer-note p {
  font-size: 12px;
  color: #999;
  margin: 0;
}
</style>
