<template>
  <div class="futu-open-account-process">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left">
        <div class="back-icon" @click="goBack">‹</div>
        <span class="title">网上转账开户</span>
      </div>
      <div class="header-right">
        <div class="help-icon">🎧</div>
        <div class="more-icon">⋯</div>
        <div class="message-icon">
          <div class="message-icon-inner">✉️</div>
          <span class="badge">3</span>
        </div>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar">
      <div class="progress-fill" :style="{ width: progressWidth }"></div>
    </div>

    <!-- 主要内容 - 模糊背景 -->
    <div class="main-content" :class="{ blurred: showModal }">
      <h1 class="page-title">网上转账开户</h1>

      <!-- 步骤列表 -->
      <div class="steps-list">
        <div class="step-item">
          <div class="step-number">1</div>
          <div class="step-content">
            <div class="step-title">资料填写</div>
            <div class="step-description">需准备身份证件以及住址证明</div>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">2</div>
          <div class="step-content">
            <div class="step-title">风险披露</div>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">3</div>
          <div class="step-content">
            <div class="step-title">转账至富途证券(香港)</div>
            <div class="step-description">单笔转账不少于 10,000 港元或 1,500 美元以完成身份核验</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 开户提示弹窗 -->
    <div v-if="showModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-handle"></div>
        <h2 class="modal-title">开户提示</h2>

        <div class="modal-body">
          <p class="modal-text">本人已阅读并同意：</p>

          <div class="agreement-list">
            <div class="agreement-item">
              1. <span class="link-text">隐私政策及个人资料收集声明</span>、<span class="link-text">开户文件</span>；
            </div>
            <div class="agreement-item">
              2. 本人或与本人配偶没有共同控制富途证券国际(香港)有限公司的其他保证金账户的35%或以上的股权；
            </div>
            <div class="agreement-item">
              3. 本人不是美国公民、绿卡持有者或美国税法定义的居民。
            </div>
          </div>
        </div>

        <button class="agree-btn" @click="agreeAndContinue">
          同意
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FutuOpenAccountProcess',
  data () {
    return {
      showModal: true,
      progressWidth: '15%'
    }
  },
  methods: {
    goBack () {
      this.$router.go(-1)
    },
    closeModal () {
      // 可以选择是否允许点击背景关闭
    },
    agreeAndContinue () {
      this.showModal = false
      // 跳转到身份信息选择页面
      this.$router.push('/open-account/identity-selection')
    }
  }
}
</script>

<style scoped>
.futu-open-account-process {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 80px;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: white;
  border-bottom: 1px solid #eee;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #333;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.help-icon, .more-icon, .message-icon-inner {
  font-size: 18px;
  cursor: pointer;
  color: #333;
}

.message-icon {
  position: relative;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-bar {
  height: 4px;
  background: #f0f0f0;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: #333;
  transition: width 0.3s ease;
}

.main-content {
  padding: 20px;
  transition: filter 0.3s ease;
}

.main-content.blurred {
  filter: blur(5px);
  pointer-events: none;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0 0 40px 0;
}

.steps-list {
  margin-bottom: 80px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40px;
  padding: 0 10px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #f8f8f8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 20px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.step-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 16px 16px 0 0;
  padding: 20px;
  width: 100%;
  max-height: 70vh;
  overflow-y: auto;
  position: relative;
}

.modal-handle {
  width: 36px;
  height: 4px;
  background: #ddd;
  border-radius: 2px;
  margin: 0 auto 20px;
}

.modal-title {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin: 0 0 20px 0;
  text-align: left;
}

.modal-body {
  margin-bottom: 30px;
}

.modal-text {
  font-size: 16px;
  color: #333;
  margin: 0 0 16px 0;
}

.agreement-list {
  margin-left: 0;
}

.agreement-item {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
  margin-bottom: 12px;
  text-align: left;
}

.link-text {
  color: #007AFF;
  text-decoration: underline;
}

.agree-btn {
  width: 100%;
  height: 50px;
  background: #333;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
}

.agree-btn:hover {
  background: #555;
}
</style>
