<template>
  <div class="futu-open-account-steps">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left">
        <div class="back-icon" @click="goBack">‹</div>
        <span class="title">Online Transfer Account Opening</span>
      </div>
      <div class="header-right">
        <div class="help-icon">🎧</div>
        <div class="more-icon">⋯</div>
        <div class="message-icon">
          <div class="message-icon-inner">✉️</div>
          <span class="badge">3</span>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <h1 class="page-title">Online Transfer Account Opening</h1>

      <!-- 步骤列表 -->
      <div class="steps-list">
        <div class="step-item">
          <div class="step-number">1</div>
          <div class="step-content">
            <div class="step-title">Information Filling</div>
            <div class="step-description">Prepare ID documents and address proof</div>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">2</div>
          <div class="step-content">
            <div class="step-title">Risk Disclosure</div>
          </div>
        </div>

        <div class="step-item">
          <div class="step-number">3</div>
          <div class="step-content">
            <div class="step-title">Transfer to Futu Securities (Hong Kong)</div>
            <div class="step-description">Single transfer of at least HK$10,000 or US$1,500 to complete identity verification</div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="action-section">
        <button class="next-btn" @click="proceedToNext">
          Next
        </button>
        <button class="other-methods-btn" @click="goToOtherMethods">
          Other Account Opening Methods
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FutuOpenAccountSteps',
  methods: {
    goBack () {
      this.$router.go(-1)
    },
    proceedToNext () {
      // 跳转到开户流程页面
      this.$router.push('/open-account/process')
    },
    goToOtherMethods () {
      this.$router.push('/open-account/methods')
    }
  }
}
</script>

<style scoped>
.futu-open-account-steps {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 80px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: white;
  border-bottom: 1px solid #eee;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #333;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.help-icon, .more-icon, .message-icon-inner {
  font-size: 18px;
  cursor: pointer;
  color: #333;
}

.message-icon {
  position: relative;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-content {
  padding: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0 0 40px 0;
}

.steps-list {
  margin-bottom: 80px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40px;
  padding: 0 10px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #f8f8f8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 20px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.step-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.action-section {
  position: fixed;
  bottom: 80px;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  border-top: 1px solid #eee;
}

.next-btn {
  width: 100%;
  height: 50px;
  background: #000;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 16px;
  transition: background 0.3s;
}

.next-btn:hover {
  background: #333;
}

.other-methods-btn {
  width: 100%;
  height: 50px;
  background: transparent;
  color: #333;
  border: none;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

.other-methods-btn:hover {
  color: #666;
}
</style>
