<template>
  <div class="futu-profile">
    <!-- 用户信息区域 -->
    <div class="profile-header">
      <div class="user-avatar">
        <div class="avatar-content">
          <div class="bull-head">
            <div class="bull-face">🐂</div>
            <div class="bull-shirt">
              <div class="shirt-stripe"></div>
              <div class="shirt-stripe"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="user-info">
        <div class="user-id">********</div>
        <div class="user-label">Account ID: ********</div>
      </div>
    </div>

    <!-- Premium 数据试用横幅 -->
    <div class="premium-banner">
      <div class="premium-content">
        <div class="premium-icon">V</div>
        <span class="premium-text">Premium Data Trial</span>
      </div>
      <div class="premium-action">
        <span class="unlock-btn">Unlock</span>
      </div>
    </div>

    <!-- 功能网格 -->
    <div class="feature-grid">
      <div class="grid-row">
        <div class="grid-item" @click="handleFeature('mall')">
          <div class="item-icon" style="background: #007AFF;">
            <van-icon name="shop-o" />
          </div>
          <span class="item-text">Market Mall</span>
        </div>
        <div class="grid-item" @click="handleFeature('rewards')">
          <div class="item-icon" style="background: #FF9500;">
            <van-icon name="gift-o" />
          </div>
          <span class="item-text">My Rewards</span>
        </div>
        <div class="grid-item" @click="handleFeature('cards')">
          <div class="item-icon" style="background: #FFCC00;">
            <van-icon name="credit-pay" />
          </div>
          <span class="item-text">My Coupons</span>
        </div>
        <div class="grid-item" @click="handleFeature('favorites')">
          <div class="item-icon" style="background: #FFD700;">
            <van-icon name="star" />
          </div>
          <span class="item-text">Favorites</span>
        </div>
      </div>
      <div class="grid-row">
        <div class="grid-item" @click="handleFeature('alerts')">
          <div class="item-icon" style="background: #FF6B35;">
            <van-icon name="bell" />
          </div>
          <span class="item-text">My Alerts</span>
        </div>
        <div class="grid-item" @click="handleFeature('football')">
          <div class="item-icon" style="background: #007AFF;">
            <van-icon name="play" />
          </div>
          <span class="item-text">Footprints</span>
        </div>
        <div class="grid-item"></div>
        <div class="grid-item"></div>
      </div>
    </div>

    <!-- 菜单列表 -->
    <div class="menu-list">
      <div class="menu-item" @click="handleMenu('tasks')">
        <div class="menu-icon">
          <van-icon name="todo-list-o" />
        </div>
        <span class="menu-text">Task Center</span>
        <van-icon name="arrow" class="menu-arrow" />
      </div>

      <div class="menu-item" @click="handleMenu('points')">
        <div class="menu-icon">
          <van-icon name="shop-o" />
        </div>
        <span class="menu-text">Points Store</span>
        <van-icon name="arrow" class="menu-arrow" />
      </div>

      <div class="menu-item" @click="handleMenu('activities')">
        <div class="menu-icon">
          <van-icon name="fire-o" />
        </div>
        <span class="menu-text">Activity Center</span>
        <van-icon name="arrow" class="menu-arrow" />
      </div>

      <div class="menu-item" @click="handleMenu('service')">
        <div class="menu-icon">
          <van-icon name="service" />
        </div>
        <span class="menu-text">Customer Service</span>
        <van-icon name="arrow" class="menu-arrow" />
      </div>

      <div class="menu-item" @click="handleMenu('help')">
        <div class="menu-icon">
          <van-icon name="question-o" />
        </div>
        <span class="menu-text">Help Center</span>
        <van-icon name="arrow" class="menu-arrow" />
      </div>

      <div class="menu-item" @click="handleMenu('feedback')">
        <div class="menu-icon">
          <van-icon name="warning-o" />
        </div>
        <span class="menu-text">Anti-Fraud Center</span>
        <van-icon name="arrow" class="menu-arrow" />
      </div>

      <div class="menu-item" @click="handleMenu('suggestions')">
        <div class="menu-icon">
          <van-icon name="chat-o" />
        </div>
        <span class="menu-text">Feedback</span>
        <van-icon name="arrow" class="menu-arrow" />
      </div>

      <div class="menu-item" @click="handleMenu('settings')">
        <div class="menu-icon">
          <van-icon name="setting-o" />
        </div>
        <span class="menu-text">Settings</span>
        <van-icon name="arrow" class="menu-arrow" />
      </div>

      <!-- 注销登录按钮 -->
      <div class="menu-item logout-item" @click="handleLogout">
        <div class="menu-icon">
          <van-icon name="sign-out" />
        </div>
        <span class="menu-text logout-text">Sign Out</span>
      </div>
    </div>

    <!-- 底部导航 -->
    <FutuTabbar />
  </div>
</template>

<script>
import FutuTabbar from '@/components/FutuTabbar.vue'
import { logout } from '@/axios/api'
import { getResponseMsg } from '@/utils/utils'
import { Dialog, Toast } from 'vant'

export default {
  name: 'FutuProfile',
  components: {
    FutuTabbar
  },
  data () {
    return {
      userInfo: {
        id: '********',
        avatar: '',
        isPremium: true
      }
    }
  },

  methods: {
    handleFeature (type) {
      switch (type) {
        case 'mall':
          this.$toast('Market Mall')
          break
        case 'rewards':
          this.$toast('My Rewards')
          break
        case 'cards':
          this.$router.push('/user/cards')
          break
        case 'favorites':
          this.$router.push('/watchlist')
          break
        case 'alerts':
          this.$toast('My Alerts')
          break
        case 'football':
          this.$toast('Footprints')
          break
        default:
          this.$toast('Feature under development')
      }
    },

    handleMenu (type) {
      switch (type) {
        case 'tasks':
          this.$toast('Task Center')
          break
        case 'points':
          this.$toast('Points Store')
          break
        case 'activities':
          this.$toast('Activity Center')
          break
        case 'service':
          this.$toast('Customer Service')
          break
        case 'help':
          this.$toast('Help Center')
          break
        case 'feedback':
          this.$toast('Anti-Fraud Center')
          break
        case 'suggestions':
          this.$toast('Feedback')
          break
        case 'settings':
          this.$router.push('/user/settings')
          break
        default:
          this.$toast('Feature under development')
      }
    },

    unlockPremium () {
      this.$toast('Premium unlock feature')
    },

    // 注销登录
    handleLogout () {
      Dialog.confirm({
        title: 'Confirm Sign Out',
        message: 'Are you sure you want to sign out?',
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel'
      }).then(async () => {
        // 用户确认注销
        // 显示加载状态
        Toast.loading({
          message: 'Signing out...',
          forbidClick: true,
          duration: 0
        })

        try {
          // 调用注销接口
          const response = await logout()

          // 清除本地存储的用户信息（无论接口成功与否都要清除）
          localStorage.removeItem('USERTOKEN')
          localStorage.removeItem('userInfo')
          localStorage.removeItem('token')
          localStorage.removeItem('userId')
          localStorage.removeItem('userName')
          localStorage.removeItem('phone')

          // 清除 Vuex 中的用户状态
          if (this.$store && this.$store.state) {
            this.$store.state.userInfo = {}
          }

          if (response && response.code === 200) {
            // 注销成功
            Toast.success(getResponseMsg(response, 'Sign out successful'))
          } else {
            // 注销失败但仍然清除本地数据并跳转
            const errorMsg = getResponseMsg(response, 'Sign out failed, but local data cleared')
            Toast(errorMsg)
          }

          // 跳转到登录页面
          setTimeout(() => {
            this.$router.replace('/futu-login')
          }, 500)
        } catch (error) {
          // 即使出错也清除本地数据并跳转
          localStorage.removeItem('USERTOKEN')
          localStorage.removeItem('userInfo')
          localStorage.removeItem('token')
          localStorage.removeItem('userId')
          localStorage.removeItem('userName')
          localStorage.removeItem('phone')

          if (this.$store && this.$store.state) {
            this.$store.state.userInfo = {}
          }

          Toast('Signed out successfully')

          setTimeout(() => {
            this.$router.replace('/futu-login')
          }, 500)
        } finally {
          Toast.clear()
        }
      }).catch(() => {
        // 用户取消注销
      })
    }
  }
}
</script>

<style scoped>
.futu-profile {
  min-height: 100vh;
  background: #F5F5F5;
  padding-bottom: 80px;
}

/* 用户信息区域 */
.profile-header {
  background: #fff;
  padding: 40px 20px 30px;
  text-align: center;
}

.user-avatar {
  width: 80px;
  height: 80px;
  margin: 0 auto 16px;
  border-radius: 50%;
  overflow: hidden;
  background: linear-gradient(135deg, #00D4AA, #00B894);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bull-head {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.bull-face {
  font-size: 32px;
  margin-bottom: -8px;
  z-index: 2;
}

.bull-shirt {
  width: 40px;
  height: 24px;
  background: #007AFF;
  border-radius: 8px 8px 12px 12px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 2px;
}

.shirt-stripe {
  width: 24px;
  height: 2px;
  background: #fff;
  border-radius: 1px;
}

.user-info {
  text-align: center;
}

.user-id {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.user-label {
  font-size: 14px;
  color: #999;
}

/* Premium 横幅 */
.premium-banner {
  background: linear-gradient(135deg, #2C2C2C, #1A1A1A);
  margin: 12px 16px;
  border-radius: 12px;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.premium-content {
  display: flex;
  align-items: center;
}

.premium-icon {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: #000;
  margin-right: 12px;
}

.premium-text {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

.unlock-btn {
  background: #fff;
  color: #333;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

/* 功能网格 */
.feature-grid {
  background: #fff;
  margin: 12px 16px;
  border-radius: 12px;
  padding: 20px;
}

.grid-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
}

.grid-row:last-child {
  margin-bottom: 0;
}

.grid-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 8px;
}

.item-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  color: #fff;
  font-size: 20px;
}

.item-text {
  font-size: 14px;
  color: #333;
  text-align: center;
}

/* 菜单列表 */
.menu-list {
  background: #fff;
  margin: 12px 16px;
  border-radius: 12px;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #F5F5F5;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #F8F9FA;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: #666;
  font-size: 18px;
}

.menu-text {
  flex: 1;
  font-size: 16px;
  color: #333;
}

.menu-arrow {
  color: #C8C9CC;
  font-size: 16px;
}

/* 注销登录按钮样式 */
.logout-item {
  border-top: 8px solid #F5F5F5;
  margin-top: 8px;
}

.logout-item .menu-icon {
  color: #FF4444;
}

.logout-text {
  color: #FF4444 !important;
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .feature-grid {
    padding: 16px;
  }

  .grid-row {
    margin-bottom: 20px;
  }

  .item-icon {
    width: 44px;
    height: 44px;
  }

  .item-text {
    font-size: 13px;
  }
}
</style>
