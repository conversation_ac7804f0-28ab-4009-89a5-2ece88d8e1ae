<template>
  <div class="futu-signup">
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left" @click="goBack">
        <span class="back-arrow">‹</span>
      </div>
      <div class="nav-right" v-if="showVerificationStep">
        <span class="help-link" @click="showHelp">Help</span>
      </div>
    </div>

    <!-- 邮箱输入步骤 -->
    <div class="signup-content" v-if="!showVerificationStep">
      <h1 class="signup-title">Enter Email Address</h1>

      <div class="input-section">
        <div class="email-input-container">
          <input
            type="email"
            class="email-input"
            placeholder="Enter your email address"
            v-model="emailInput"
            @input="validateEmail"
          >
        </div>
      </div>

      <!-- 协议文本 -->
      <div class="agreement-section">
        <div class="checkbox-container">
          <input
            type="checkbox"
            id="agree-checkbox"
            class="agreement-checkbox"
            v-model="agreedToTerms"
          >
          <label for="agree-checkbox" class="checkbox-label">
            <span class="checkmark"></span>
          </label>
        </div>
        <p class="agreement-text">
          I agree to
          <span class="link" @click="showPrivacyPolicy">Privacy Policy</span>
          and
          <span class="link" @click="showTermsOfService">Terms of Service</span>
        </p>
      </div>

      <!-- 继续按钮 -->
      <div class="button-container">
        <button
          class="continue-button"
          :class="{ disabled: !canProceed }"
          @click="proceedToVerification"
          :disabled="!canProceed"
        >
          Continue
        </button>
      </div>

      <!-- 底部登录方式 -->
      <!-- <div class="bottom-options">
        <div class="option-item" @click="loginWithWechat">
          <div class="option-icon wechat-icon"></div>
        </div>
        <div class="option-item" @click="loginWithDingTalk">
          <div class="option-icon dingtalk-icon"></div>
        </div>
        <div class="option-item" @click="loginWithApple">
          <div class="option-icon apple-icon"></div>
        </div>
        <div class="more-options" @click="showMoreOptions">
          <span>···</span>
        </div>
      </div> -->
    </div>

    <!-- 验证码输入步骤 -->
    <div class="verification-content" v-if="showVerificationStep">
      <h1 class="verification-title">Enter Verification Code</h1>

      <p class="verification-info">
        The code has been sent to <span class="email-text">{{ emailInput }}</span>
      </p>

      <!-- 验证码输入框 -->
      <div class="code-input-section">
        <div class="code-input-container">
          <input
            v-for="(digit, index) in verificationCode"
            :key="index"
            type="text"
            maxlength="1"
            class="code-input"
            :class="{ filled: digit }"
            v-model="verificationCode[index]"
            @input="handleCodeInput(index, $event)"
            @keydown="handleKeyDown(index, $event)"
            :ref="`codeInput${index}`"
          >
        </div>
      </div>

      <!-- 重发选项 -->
      <div class="resend-section">
        <span class="resend-text" @click="showResendOptions">Didn't Get a Code</span>
        <span class="resend-timer" v-if="resendTimer > 0">Resend in {{ resendTimer }}s</span>
        <span class="resend-link" v-else @click="resendCode">Resend</span>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'
import * as api from '@/axios/api'

export default {
  name: 'FutuSignUp',
  data() {
    return {
      emailInput: '',
      agreedToTerms: false,
      showVerificationStep: false,
      verificationCode: ['', '', '', '', '', ''],
      resendTimer: 0,
      timerInterval: null
    }
  },
  computed: {
    canProceed() {
      return this.isValidEmail(this.emailInput) && this.agreedToTerms
    }
  },
  beforeDestroy() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval)
    }
  },
  methods: {
    goBack() {
      if (this.showVerificationStep) {
        this.showVerificationStep = false
        this.verificationCode = ['', '', '', '', '', '']
      } else {
        this.$router.push('/futu-welcome')
      }
    },

    showHelp() {
      Toast('For help, please contact customer service')
    },

    validateEmail() {
      // 实时验证邮箱格式
    },

    isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(email.trim())
    },

    showPrivacyPolicy() {
      Toast('Privacy Policy')
    },

    showTermsOfService() {
      Toast('Terms of Service')
    },

        async proceedToVerification() {
      if (!this.canProceed) return

      try {
        // 调用发送注册邮箱验证码的API
        const result = await api.sendRegEmail({ email: this.emailInput.trim() })

        if (result.status === 0 || result.code === 200) {
          this.showVerificationStep = true
          this.startResendTimer()

          // 自动聚焦第一个验证码输入框
          this.$nextTick(() => {
            this.$refs.codeInput0[0].focus()
          })

          Toast.success('Verification code sent to your email')
        } else {
          Toast.fail(result.msg || 'Failed to send verification code, please try again')
        }
      } catch (error) {
        console.error('Send email error:', error)
        Toast.fail('Failed to send verification code, please try again')
      }
    },

    handleCodeInput(index, event) {
      const value = event.target.value

      if (value && index < 5) {
        // 自动跳转到下一个输入框
        this.$refs[`codeInput${index + 1}`][0].focus()
      }

      // 检查是否所有验证码都已输入
      if (this.verificationCode.every(digit => digit)) {
        this.verifyCode()
      }
    },

    handleKeyDown(index, event) {
      // 处理退格键
      if (event.key === 'Backspace' && !this.verificationCode[index] && index > 0) {
        this.$refs[`codeInput${index - 1}`][0].focus()
      }
    },

        async verifyCode() {
      const code = this.verificationCode.join('')

      if (code.length !== 6) {
        Toast.fail('Please enter complete verification code')
        return
      }

      try {
        // 调用邮箱注册API
        const result = await api.registerWithEmail({
          email: this.emailInput.trim(),
          yzmCode: code,
          userPwd: 'TempPassword123!', // 这里可以让用户设置密码，或者在注册成功后跳转到设置密码页面
          agentCode: '' // 推荐码，可选
        })

        if (result.status === 0 || result.code === 200) {
          Toast.success('Registration successful!')

          // 跳转到登录页面
          this.$router.push('/futu-login')
        } else {
          Toast.fail(result.msg || 'Registration failed, please try again')
          this.verificationCode = ['', '', '', '', '', '']
          this.$refs.codeInput0[0].focus()
        }
      } catch (error) {
        console.error('Registration error:', error)
        Toast.fail('Invalid verification code, please try again')
        this.verificationCode = ['', '', '', '', '', '']
        this.$refs.codeInput0[0].focus()
      }
    },

    startResendTimer() {
      this.resendTimer = 60
      this.timerInterval = setInterval(() => {
        this.resendTimer--
        if (this.resendTimer <= 0) {
          clearInterval(this.timerInterval)
        }
      }, 1000)
    },

    showResendOptions() {
      Toast('If you didn\'t receive the code, please check your spam folder')
    },

        async resendCode() {
      if (this.resendTimer > 0) return

      try {
        // 重新发送注册邮箱验证码
        const result = await api.sendRegEmail({ email: this.emailInput.trim() })

        if (result.status === 0 || result.code === 200) {
          Toast.success('Verification code resent successfully')
          this.startResendTimer()
        } else {
          Toast.fail(result.msg || 'Failed to resend verification code, please try again')
        }
      } catch (error) {
        console.error('Resend email error:', error)
        Toast.fail('Failed to resend verification code, please try again')
      }
    },

    loginWithWechat() {
      Toast('WeChat login feature under development')
    },

    loginWithDingTalk() {
      Toast('DingTalk login feature under development')
    },

    loginWithApple() {
      Toast('Apple login feature under development')
    },

    showMoreOptions() {
      Toast('More login options')
    }
  }
}
</script>

<style scoped>
.futu-signup {
  min-height: 100vh;
  background: #fff;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  height: 44px;
}

.nav-left {
  cursor: pointer;
}

.back-arrow {
  font-size: 24px;
  color: #000;
  font-weight: 300;
}

.help-link {
  font-size: 16px;
  color: #000;
  text-decoration: underline;
  cursor: pointer;
}

/* 邮箱输入步骤 */
.signup-content {
  padding: 40px 20px;
}

.signup-title {
  font-size: 34px;
  font-weight: bold;
  margin-bottom: 60px;
  color: #000;
}

.input-section {
  margin-bottom: 40px;
}

.email-input-container {
  position: relative;
}

.email-input {
  width: 100%;
  padding: 16px 0;
  border: none;
  border-bottom: 1px solid #E5E5E7;
  font-size: 16px;
  background: transparent;
  outline: none;
  color: #000;
}

.email-input:focus {
  border-bottom-color: #FF6600;
}

.email-input::placeholder {
  color: #C7C7CC;
}

/* 协议部分 */
.agreement-section {
  display: flex;
  align-items: flex-start;
  margin-bottom: 60px;
  gap: 12px;
}

.checkbox-container {
  position: relative;
  margin-top: 2px;
}

.agreement-checkbox {
  opacity: 0;
  position: absolute;
  cursor: pointer;
}

.checkbox-label {
  display: block;
  cursor: pointer;
}

.checkmark {
  display: block;
  width: 20px;
  height: 20px;
  background: #fff;
  border: 2px solid #E5E5E7;
  border-radius: 4px;
  position: relative;
}

.agreement-checkbox:checked + .checkbox-label .checkmark {
  background: #000;
  border-color: #000;
}

.agreement-checkbox:checked + .checkbox-label .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.agreement-text {
  font-size: 12px;
  color: #8E8E93;
  line-height: 1.4;
  margin: 0;
}

.link {
  color: #007AFF;
  text-decoration: none;
  cursor: pointer;
}

.link:hover {
  text-decoration: underline;
}

/* 继续按钮 */
.button-container {
  margin-bottom: 60px;
}

.continue-button {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  background: #000;
  cursor: pointer;
  transition: all 0.2s;
}

.continue-button:hover:not(.disabled) {
  background: #333;
}

.continue-button.disabled {
  background: #C7C7CC;
  cursor: not-allowed;
}

/* 底部选项 */
.bottom-options {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  padding: 0 20px;
}

.option-item {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.option-item:hover {
  background: #f0f0f0;
}

.option-icon {
  width: 32px;
  height: 32px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.wechat-icon {
  background-color: #1AAD19;
  border-radius: 6px;
}

.dingtalk-icon {
  background-color: #0089FF;
  border-radius: 6px;
}

.apple-icon {
  background-color: #000;
  border-radius: 6px;
}

.more-options {
  font-size: 24px;
  color: #8E8E93;
  cursor: pointer;
  padding: 12px;
}

/* 验证码输入步骤 */
.verification-content {
  padding: 40px 20px;
}

.verification-title {
  font-size: 34px;
  font-weight: bold;
  margin-bottom: 40px;
  color: #000;
}

.verification-info {
  font-size: 16px;
  color: #8E8E93;
  margin-bottom: 60px;
  line-height: 1.5;
}

.email-text {
  color: #000;
  font-weight: 500;
}

/* 验证码输入 */
.code-input-section {
  margin-bottom: 40px;
}

.code-input-container {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 40px;
}

.code-input {
  width: 48px;
  height: 56px;
  border: 2px solid #E5E5E7;
  border-radius: 8px;
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  background: transparent;
  outline: none;
  color: #000;
  transition: border-color 0.2s;
}

.code-input:focus {
  border-color: #FF6600;
}

.code-input.filled {
  border-color: #000;
  background: #F2F2F7;
}

/* 重发部分 */
.resend-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.resend-text {
  color: #000;
  text-decoration: underline;
  cursor: pointer;
  font-size: 16px;
}

.resend-timer {
  color: #8E8E93;
  font-size: 16px;
}

.resend-link {
  color: #8E8E93;
  text-decoration: underline;
  cursor: pointer;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .signup-content,
  .verification-content {
    padding: 30px 16px;
  }

  .signup-title,
  .verification-title {
    font-size: 28px;
    margin-bottom: 40px;
  }

  .bottom-options {
    gap: 30px;
    padding: 0 16px;
  }
}
</style>
