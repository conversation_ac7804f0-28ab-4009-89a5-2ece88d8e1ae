<template>
  <div class="futu-stock-detail">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <div class="nav-left">
        <van-icon name="arrow-left" @click="$router.back()" />
      </div>
      <div class="nav-center">
        <div class="ai-button">
          <van-icon name="robot" />
          <span>AI</span>
          <van-icon name="arrow" />
        </div>
      </div>
      <div class="nav-right">
        <van-icon name="search" />
        <van-icon name="like-o" />
      </div>
    </div>

    <!-- Tab栏 -->
    <div class="main-tabs">
      <div class="tab-item" :class="{ active: activeTab === 'chart' }" @click="switchTab('chart')">Chart</div>
      <div class="tab-item" :class="{ active: activeTab === 'options' }" @click="switchTab('options')">Options</div>
      <div class="tab-item" :class="{ active: activeTab === 'etf' }" @click="switchTab('etf')">ETF</div>
      <div class="tab-item" :class="{ active: activeTab === 'comments' }" @click="switchTab('comments')">Comments</div>
      <div class="tab-item" :class="{ active: activeTab === 'news' }" @click="switchTab('news')">News</div>
      <div class="tab-item" :class="{ active: activeTab === 'analysis' }" @click="switchTab('analysis')">Analysis</div>
      <div class="tab-item" :class="{ active: activeTab === 'company' }" @click="switchTab('company')">Company</div>
    </div>

        <!-- 股票信息区域 -->
    <div class="stock-info">
      <!-- 股票基本信息（在资讯和公司页面隐藏） -->
      <div v-if="activeTab !== 'news' && activeTab !== 'company'" class="stock-basic-info">
        <div class="stock-header">
          <div class="stock-name">
            <h2>{{ stockData.symbol }} {{ stockData.name }}</h2>
            <p class="market-time">Close Price {{ stockData.closeTime }} ({{ stockData.market }})</p>
          </div>
          <div class="market-icons">
            <div class="icon-us">🇺🇸</div>
            <div class="icon-realtime">📊</div>
            <div class="icon-24h">24</div>
            <div class="icon-history">📈</div>
            <div class="icon-alert">⚡</div>
            <div class="icon-more">📋</div>
          </div>
        </div>

        <div class="price-section">
          <div class="main-price">
            <span class="price" :class="{ 'up': stockData.change > 0, 'down': stockData.change < 0 }">
              {{ stockData.price }}
            </span>
            <span class="change-icon">↑</span>
          </div>
          <div class="change-info">
            <span class="change-amount">{{ stockData.change > 0 ? '+' : '' }}{{ parseFloat(stockData.change).toFixed(2) }}</span>
            <span class="change-percent">{{ stockData.changePercent > 0 ? '+' : '' }}{{ stockData.changePercent }}%</span>
          </div>
        </div>

        <div class="stock-metrics">
          <!-- 第一行 -->
          <div class="metric-row">
            <div class="metric-item">
              <span class="label">High</span>
              <span class="value red">{{ stockData.high }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Open</span>
              <span class="value green">{{ stockData.open }}</span>
            </div>
          </div>
          <!-- 第二行 -->
          <div class="metric-row">
            <div class="metric-item">
              <span class="label">Low</span>
              <span class="value green">{{ stockData.low }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Prev Close</span>
              <span class="value">{{ stockData.prevClose }}</span>
            </div>
          </div>
          <!-- 第三行 -->
          <div class="metric-row">
            <div class="metric-item">
              <span class="label">Turnover</span>
              <span class="value">{{ stockData.tradeAmount || '--' }}</span>
            </div>
            <div class="metric-item">
              <span class="label">P/E TTM</span>
              <span class="value">{{ stockData.peRatio || stockData.pe || '--' }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Market Cap</span>
              <span class="value">{{ stockData.marketCap }}</span>
            </div>
          </div>
          <!-- 第四行 -->
          <div class="metric-row">
            <div class="metric-item">
              <span class="label">Volume</span>
              <span class="value">{{ stockData.volume }}</span>
            </div>
            <div class="metric-item">
              <span class="label">P/E Static</span>
              <span class="value">{{ stockData.peStatic || '--' }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Shares Out</span>
              <span class="value">{{ stockData.sharesOutstanding || '--' }}</span>
            </div>
          </div>
          <!-- 第五行 -->
          <div class="metric-row">
            <div class="metric-item">
              <span class="label">Turnover Rate</span>
              <span class="value">{{ stockData.turnoverRate }}</span>
            </div>
            <div class="metric-item">
              <span class="label">P/B Ratio</span>
              <span class="value">{{ stockData.pbRatio || '--' }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Float Cap</span>
              <span class="value">{{ stockData.floatMarketCap || '--' }}</span>
            </div>
          </div>
          <!-- 第六行 -->
          <div class="metric-row">
            <div class="metric-item">
              <span class="label">52W High</span>
              <span class="value">{{ stockData.weekHigh52 || '--' }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Bid/Ask</span>
              <span class="value">{{ stockData.askBidRatio }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Float Shares</span>
              <span class="value">{{ stockData.shareFloat || '--' }}</span>
            </div>
          </div>
          <!-- 第七行 -->
          <div class="metric-row">
            <div class="metric-item">
              <span class="label">52W Low</span>
              <span class="value">{{ stockData.weekLow52 || '--' }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Vol Ratio</span>
              <span class="value">{{ stockData.volumeRatio }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Amplitude</span>
              <span class="value">{{ stockData.amplitude }}</span>
            </div>
          </div>
          <!-- 第八行 -->
          <div class="metric-row">
            <div class="metric-item">
              <span class="label">All Time High</span>
              <span class="value">{{ stockData.allTimeHigh || stockData.weekHigh52 || '--' }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Dividend TTM</span>
              <span class="value">{{ stockData.dividend || '--' }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Avg Price</span>
              <span class="value">{{ stockData.avgPrice }}</span>
            </div>
          </div>
          <!-- 第九行 -->
          <div class="metric-row">
            <div class="metric-item">
              <span class="label">All Time Low</span>
              <span class="value">{{ stockData.allTimeLow || stockData.weekLow52 || '--' }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Div Yield TTM</span>
              <span class="value">{{ stockData.dividendYield || '--' }}</span>
            </div>
            <div class="metric-item">
              <span class="label">Lot Size</span>
              <span class="value">{{ stockData.lotSize || '1 Share' }}</span>
            </div>
          </div>

          <!-- Polygon API 数据验证区域（可选显示） -->
          <div class="polygon-data-debug" v-if="showPolygonDebug">
            <h4>Polygon API 数据验证</h4>
            <div class="debug-row" v-if="polygonData.dailyOpenClose">
              <span class="debug-label">日开收API数据:</span>
              <span class="debug-value">
                开{{ polygonData.dailyOpenClose.open }}
                收{{ polygonData.dailyOpenClose.close }}
                高{{ polygonData.dailyOpenClose.high }}
                低{{ polygonData.dailyOpenClose.low }}
              </span>
            </div>
            <div class="debug-row" v-if="polygonData.previousClose">
              <span class="debug-label">前收盘API数据:</span>
              <span class="debug-value">{{ polygonData.previousClose.c }}</span>
            </div>
          </div>
        </div>

        <!-- 盘前数据 -->
        <div class="premarket-info">
          <div class="premarket-icon">⏰</div>
          <span class="premarket-label">Pre-Market</span>
          <span class="premarket-price green">143.050</span>
          <span class="premarket-change green">-1.950</span>
          <span class="premarket-percent green">-1.34%</span>
          <span class="premarket-time">08:12 (EST)</span>
          <div class="premarket-arrow">⌄</div>
        </div>

        <!-- 财报提醒 -->
        <div class="earnings-alert">
          <div class="earnings-icon">⏰</div>
          <span>2025/08/27 (EST) Earnings Report After Market</span>
          <div class="earnings-calendar">📅</div>
          <div class="earnings-expand">⌄</div>
        </div>

        <!-- AI资讯推荐 -->
        <div class="ai-news-section" v-if="activeTab === 'chart'">
          <div class="ai-news-icon">📰</div>
          <span class="ai-news-text">下一波AI催化剂来了？大摩详解英伟达GTC三大亮点：欧洲投资...</span>
        </div>
      </div>

      <!-- 股票新闻（单独显示） -->
      <div class="stock-news-section" v-if="activeTab === 'news'">
        <div class="news-header">
          <h3>{{ stockData.symbol }} {{ stockData.name }} - Related News</h3>
          <span class="news-count" v-if="polygonData.tickerNews.length > 0">{{ polygonData.tickerNews.length }} items</span>
        </div>
        <div class="news-list" v-if="polygonData.tickerNews.length > 0">
          <div
            class="news-item"
            v-for="(news, index) in polygonData.tickerNews"
            :key="index"
            @click="openNews(news)"
          >
            <div class="news-content">
              <h4 class="news-title">{{ news.title }}</h4>
              <p class="news-description">{{ news.description }}</p>
              <div class="news-meta">
                <span class="news-author">{{ news.author }}</span>
                <span class="news-time">{{ formatNewsTime(news.published_utc) }}</span>
              </div>
            </div>
            <div class="news-image" v-if="news.image_url">
              <img :src="news.image_url" :alt="news.title" />
            </div>
          </div>
        </div>
        <div class="no-news" v-else>
          <p>No related news available</p>
        </div>
      </div>

      <!-- 公司信息（单独显示） -->
      <div class="company-info-section" v-if="activeTab === 'company'">
        <div class="company-header">
          <h3>{{ stockData.symbol }} {{ stockData.name }} - Company Information</h3>
        </div>
        <div class="company-details" v-if="polygonData.tickerDetails">
          <div class="company-row" v-if="polygonData.tickerDetails.description">
            <span class="company-label">Company Description</span>
            <p class="company-value company-description">{{ polygonData.tickerDetails.description }}</p>
          </div>
          <div class="company-row" v-if="polygonData.tickerDetails.homepage_url">
            <span class="company-label">Official Website</span>
            <a class="company-value company-link" :href="polygonData.tickerDetails.homepage_url" target="_blank">
              {{ polygonData.tickerDetails.homepage_url }}
            </a>
          </div>
          <div class="company-row" v-if="polygonData.tickerDetails.total_employees">
            <span class="company-label">Employees</span>
            <span class="company-value">{{ formatNumber(polygonData.tickerDetails.total_employees) }} employees</span>
          </div>
          <div class="company-row" v-if="polygonData.tickerDetails.list_date">
            <span class="company-label">IPO Date</span>
            <span class="company-value">{{ polygonData.tickerDetails.list_date }}</span>
          </div>
          <div class="company-row" v-if="stockData.phoneNumber">
            <span class="company-label">Phone</span>
            <span class="company-value">{{ stockData.phoneNumber }}</span>
          </div>
          <div class="company-row" v-if="stockData.sicCode">
            <span class="company-label">Industry Code</span>
            <span class="company-value">{{ stockData.sicCode }} - {{ stockData.sicDescription }}</span>
          </div>
          <div class="company-row" v-if="stockData.currencyName">
            <span class="company-label">Currency</span>
            <span class="company-value">{{ stockData.currencyName }}</span>
          </div>
          <div class="company-row" v-if="stockData.roundLot">
            <span class="company-label">Round Lot</span>
            <span class="company-value">{{ stockData.roundLot }} shares</span>
          </div>
          <div class="company-row" v-if="polygonData.tickerDetails && polygonData.tickerDetails.address">
            <span class="company-label">Address</span>
            <span class="company-value">
              {{ polygonData.tickerDetails.address.address1 }},
              {{ polygonData.tickerDetails.address.city }},
              {{ polygonData.tickerDetails.address.state }}
              {{ polygonData.tickerDetails.address.postal_code }}
            </span>
          </div>
        </div>
        <div class="no-company-info" v-else>
          <p>No company information available</p>
        </div>
      </div>
    </div>

    <!-- 期权页面 -->
    <div class="options-container" v-if="activeTab === 'options'">
      <!-- 期权链子标签 -->
      <div class="options-subtabs">
        <div class="subtab-item" :class="{ active: optionsSubTab === 'chain' }" @click="switchOptionsTab('chain')">Options Chain</div>
        <div class="subtab-item" :class="{ active: optionsSubTab === 'analysis' }" @click="switchOptionsTab('analysis')">Analysis</div>
        <div class="subtab-item" :class="{ active: optionsSubTab === 'activity' }" @click="switchOptionsTab('activity')">Unusual Activity</div>
        <div class="subtab-item" :class="{ active: optionsSubTab === 'ranking' }" @click="switchOptionsTab('ranking')">Rankings</div>
        <div class="options-settings">
          <van-icon name="setting-o" />
        </div>
      </div>

      <!-- 期权筛选条件 -->
      <div class="options-filters">
        <div class="filter-row">
          <div class="filter-group">
            <span class="filter-label">All</span>
            <span class="filter-label active" @click="switchOptionsFilter('call')">Calls</span>
            <span class="filter-label" @click="switchOptionsFilter('put')">Puts</span>
          </div>
          <div class="filter-icons">
            <van-icon name="volume-o" />
            <van-icon name="notes-o" />
            <van-icon name="minus" />
          </div>
        </div>

        <!-- 期权到期日期选择 -->
        <div class="expiry-dates">
          <div
            class="date-item"
            v-for="(date, index) in expiryDates"
            :key="index"
            :class="{ active: selectedExpiry === date.value }"
            @click="selectExpiry(date.value)"
          >
            <div class="date-text">{{ date.label }}</div>
            <div class="date-super" v-if="date.super">{{ date.super }}</div>
          </div>
        </div>
      </div>

      <!-- Call/Put 统计信息 -->
      <div class="options-stats">
        <div class="stats-left">
          <span class="stats-label red">Call</span>
          <span class="stats-value">80.74万</span>
        </div>
        <div class="stats-center">
          <span class="stats-time">58:42</span>
        </div>
        <div class="stats-right">
          <span class="stats-value">57.98万</span>
          <span class="stats-label green">Put</span>
        </div>
      </div>

      <!-- 期权链表头 -->
      <div class="options-table-header">
        <div class="header-column">最新价</div>
        <div class="header-column">卖盘</div>
        <div class="header-column">买盘</div>
        <div class="header-column">行权价</div>
        <div class="header-column">买盘</div>
        <div class="header-column">卖盘</div>
        <div class="header-column">最新价</div>
      </div>

      <!-- 期权链数据 -->
      <div class="options-table">
        <div
          class="options-row"
          v-for="(option, index) in optionsData"
          :key="index"
          @click="goToOptionDetail(option)"
        >
          <!-- Call 侧数据 -->
          <div class="call-side">
            <div class="option-price" :class="option.call.priceClass">{{ option.call.lastPrice }}</div>
            <div class="option-volume">
              <div class="ask-price">{{ option.call.askPrice }}</div>
              <div class="ask-size">x{{ option.call.askSize }}</div>
            </div>
            <div class="option-volume">
              <div class="bid-price">{{ option.call.bidPrice }}</div>
              <div class="bid-size">x{{ option.call.bidSize }}</div>
            </div>
          </div>

          <!-- 行权价 -->
          <div class="strike-price">{{ option.strikePrice }}</div>

          <!-- Put 侧数据 -->
          <div class="put-side">
            <div class="option-volume">
              <div class="bid-price">{{ option.put.bidPrice }}</div>
              <div class="bid-size">x{{ option.put.bidSize }}</div>
            </div>
            <div class="option-volume">
              <div class="ask-price">{{ option.put.askPrice }}</div>
              <div class="ask-size">x{{ option.put.askSize }}</div>
            </div>
            <div class="option-price" :class="option.put.priceClass">{{ option.put.lastPrice }}</div>
          </div>
        </div>
      </div>

      <!-- 单腿期权按钮 -->
      <div class="single-leg-options">
        <button class="single-leg-btn">
          单腿期权 <van-icon name="arrow-down" />
        </button>
      </div>
    </div>

    <!-- 图表页面内容 -->
    <div class="chart-content" v-if="activeTab === 'chart'">

      <!-- K线图控制栏 -->
      <div class="chart-controls">
        <div class="time-controls">
          <span class="control-btn chart-icon">📊</span>
          <span
            v-for="period in timePeriods"
            :key="period.key"
            :class="['control-btn', { active: currentTimePeriod === period.key }]"
            @click="changeTimePeriod(period.key)"
          >
            {{ period.label }}
          </span>
          <span class="control-btn more-btn">⋯</span>
        </div>
      </div>

      <!-- MA指标信息 -->
      <div class="ma-indicators">
        <span class="ma-item">MA</span>
        <span class="ma-value ma5">MA20:137.860</span>
        <span class="ma-value ma10">MA250:126.194</span>
      </div>

      <!-- K线图区域 -->
      <div class="chart-container">
        <KLineComponent
          :symbol="stockData.symbol"
          :name="stockData.name"
          :activePeriod="currentTimePeriod"
          @priceUpdate="handlePriceUpdate"
        />
      </div>

      <!-- 技术指标栏 -->
      <div class="tech-indicators">
        <div class="indicator-tabs">
          <span
            class="indicator-tab"
            :class="{ active: activeIndicators.includes('MA') }"
            @click="toggleIndicator('MA')"
          >MA</span>
          <span
            class="indicator-tab"
            :class="{ active: activeIndicators.includes('BOLL') }"
            @click="toggleIndicator('BOLL')"
          >BOLL</span>
          <span
            class="indicator-tab"
            :class="{ active: activeIndicators.includes('EMA') }"
            @click="toggleIndicator('EMA')"
          >EMA</span>
          <span
            class="indicator-tab"
            :class="{ active: activeIndicators.includes('SAR') }"
            @click="toggleIndicator('SAR')"
          >SAR</span>
          <span
            class="indicator-tab"
            :class="{ active: activeIndicators.includes('KC') }"
            @click="toggleIndicator('KC')"
          >KC</span>
          <span
            class="indicator-tab"
            :class="{ active: activeIndicators.includes('VOL') }"
            @click="toggleIndicator('VOL')"
          >Volume</span>
          <span
            class="indicator-tab"
            :class="{ active: activeIndicators.includes('MACD') }"
            @click="toggleIndicator('MACD')"
          >MACD</span>
          <span
            class="indicator-tab"
            :class="{ active: activeIndicators.includes('KDJ') }"
            @click="toggleIndicator('KDJ')"
          >KDJ</span>
          <span
            class="indicator-tab"
            :class="{ active: activeIndicators.includes('ARBR') }"
            @click="toggleIndicator('ARBR')"
          >ARBR</span>
          <span
            class="indicator-tab"
            :class="{ active: activeIndicators.includes('CCI') }"
            @click="toggleIndicator('CCI')"
          >C</span>
        </div>

        <!-- 技术指标数据显示 -->
        <div class="indicators-data" v-if="activeIndicators.length > 0">
          <!-- MACD指标数据 -->
          <div class="indicator-data" v-if="activeIndicators.includes('MACD')">
            <span class="indicator-name">MACD(12,26,9)</span>
            <span class="indicator-value macd-dif">DIF:{{ indicatorData.MACD.DIF }}</span>
            <span class="indicator-value macd-dea">DEA:{{ indicatorData.MACD.DEA }}</span>
            <span class="indicator-value macd-macd">MACD:{{ indicatorData.MACD.MACD }}</span>
          </div>

          <!-- KDJ指标数据 -->
          <div class="indicator-data" v-if="activeIndicators.includes('KDJ')">
            <span class="indicator-name">KDJ(9,3,3)</span>
            <span class="indicator-value kdj-k">K:{{ indicatorData.KDJ.K }}</span>
            <span class="indicator-value kdj-d">D:{{ indicatorData.KDJ.D }}</span>
            <span class="indicator-value kdj-j">J:{{ indicatorData.KDJ.J }}</span>
          </div>

          <!-- BOLL指标数据 -->
          <div class="indicator-data" v-if="activeIndicators.includes('BOLL')">
            <span class="indicator-name">BOLL(20,2)</span>
            <span class="indicator-value boll-upper">UPPER:{{ indicatorData.BOLL.UPPER }}</span>
            <span class="indicator-value boll-mid">MID:{{ indicatorData.BOLL.MID }}</span>
            <span class="indicator-value boll-lower">LOWER:{{ indicatorData.BOLL.LOWER }}</span>
          </div>

          <!-- EMA指标数据 -->
          <div class="indicator-data" v-if="activeIndicators.includes('EMA')">
            <span class="indicator-name">EMA</span>
            <span class="indicator-value ema-12">EMA12:{{ indicatorData.EMA.EMA12 }}</span>
            <span class="indicator-value ema-26">EMA26:{{ indicatorData.EMA.EMA26 }}</span>
          </div>

          <!-- SAR指标数据 -->
          <div class="indicator-data" v-if="activeIndicators.includes('SAR')">
            <span class="indicator-name">SAR(4,0.02,0.2)</span>
            <span class="indicator-value sar-value">SAR:{{ indicatorData.SAR.VALUE }}</span>
          </div>

          <!-- KC指标数据 -->
          <div class="indicator-data" v-if="activeIndicators.includes('KC')">
            <span class="indicator-name">KC(20,2)</span>
            <span class="indicator-value kc-upper">UPPER:{{ indicatorData.KC.UPPER }}</span>
            <span class="indicator-value kc-mid">MID:{{ indicatorData.KC.MID }}</span>
            <span class="indicator-value kc-lower">LOWER:{{ indicatorData.KC.LOWER }}</span>
          </div>

          <!-- ARBR指标数据 -->
          <div class="indicator-data" v-if="activeIndicators.includes('ARBR')">
            <span class="indicator-name">ARBR(26)</span>
            <span class="indicator-value arbr-ar">AR:{{ indicatorData.ARBR.AR }}</span>
            <span class="indicator-value arbr-br">BR:{{ indicatorData.ARBR.BR }}</span>
          </div>
        </div>
      </div>

      <!-- 深度盘口组件 -->
      <MarketDepth :ticker="stockData.symbol" />
    </div>

    <!-- 底部操作按钮 -->
    <div class="bottom-actions">
      <div class="action-buttons">
        <button class="action-btn primary" @click="goBuy()">Trade</button>
        <button class="action-btn secondary">Paper Trade</button>
      </div>
      <div class="action-icons">
        <van-icon name="bell-o" />
        <van-icon name="share-o" />
        <van-icon name="ellipsis" />
      </div>
    </div>
  </div>
</template>

<script>
import KLineComponent from '../page/kline/components/KLineChartComponent.vue'
import MarketDepth from '@/components/MarketDepth.vue'
import * as api from '@/axios/api'
import { Toast } from 'mint-ui'
import {
  getSingleStock,
  getUsDetail,
  isOption,
  addOption,
  delOption,
  // Polygon API
  getTickerDetails,
  getTickerSnapshot,
  getRelatedTickers,
  getTickerNews,
  getAggregates,
  getDailyOpenClose,
  getPreviousClose
} from '@/axios/api'
import {
  calculateMACD,
  calculateKDJ,
  calculateBOLL,
  calculateEMA,
  calculateSAR,
  calculateKC,
  calculateARBR,
  generateMockKLineData
} from '@/utils/technicalIndicators'
import {
  connectIndicatorWebSocket,
  subscribeIndicators,
  disconnectIndicatorWebSocket
} from '@/utils/websocketIndicators'
import AggregatesService from '@/services/aggregatesService'
import {
  connectRealTimeWS,
  disconnectRealTimeWS,
  subscribeRealTime,
  unsubscribeRealTime
} from '@/services/realTimeWebSocket'

export default {
  name: 'futu-stock-detail',
  data () {
    return {
      activeTab: 'chart', // 当前激活的主标签
      optionsSubTab: 'chain', // 期权子标签
      selectedExpiry: '06/20', // 选中的到期日
      optionsFilter: 'all', // 期权筛选: all, call, put

      // K线时间周期管理
      currentTimePeriod: '5min', // 当前选中的时间周期
      timePeriods: [
        { key: '5min', label: '5分' },
        { key: 'day', label: '日K' },
        { key: 'week', label: '周K' },
        { key: 'month', label: '月K' },
        { key: '1min', label: '1分' },
        { key: '15min', label: '15分' },
        { key: '30min', label: '30分' },
        { key: '1hour', label: '1小时' }
      ],

      stockData: {
        symbol: 'NVDA',
        name: '英伟达',
        price: '145.000',
        change: 2.170,
        changePercent: 1.52,
        high: '145.000',
        low: '141.850',
        open: '141.970',
        prevClose: '142.830',
        volume: '234.2亿',
        pe: '46.77',
        marketCap: '3.54万亿',
        closeTime: '06/12 16:00:00',
        market: '美东',
        preMarket: {
          price: '143.050',
          change: '-1.950',
          changePercent: '-1.34',
          time: '07:54 (美东)'
        },
        type: 'US',
        // 扩展字段用于存储 Polygon API 数据
        marketCapValue: null,
        sharesOutstanding: null,
        shareFloat: null,
        weekHigh52: null,
        weekLow52: null,
        allTimeHigh: null,
        allTimeLow: null,
        dividend: null,
        dividendYield: null,
        peRatio: null,
        pbRatio: null,
        employees: null,
        listDate: null,
        description: null,
        homepage: null,
        // 新增的公司详细信息字段
        sicCode: null,
        sicDescription: null,
        currencyName: 'USD',
        roundLot: 100,
        phoneNumber: null,
        // 计算字段
        turnoverRate: '0.69%', // 换手率
        askBidRatio: '0.00%', // 委比
        volumeRatio: '0.90', // 量比
        amplitude: '2.20%', // 振幅
        avgPrice: '144.246' // 平均价
      },
      singDetails: {},
      isOptionOpt: false,

      // 调试开关 - 用于显示 Polygon API 原始数据验证
      showPolygonDebug: false, // 设置为 true 可查看API原始数据

      // Polygon API 数据
      polygonData: {
        tickerDetails: null,
        tickerSnapshot: null,
        relatedTickers: [],
        tickerNews: [],
        aggregatesData: null,
        dailyOpenClose: null,
        previousClose: null
      },

      // Polygon API 加载状态
      polygonLoading: {
        details: false,
        snapshot: false,
        related: false,
        news: false,
        aggregates: false,
        weeklyAggregates: false,
        openClose: false,
        prevClose: false
      },

      // 技术指标相关
      activeIndicators: ['MA', 'VOL'], // 默认激活的指标
      indicatorData: {
        MACD: {
          DIF: '4.075',
          DEA: '2.987',
          MACD: '2.176'
        },
        KDJ: {
          K: '75.42',
          D: '68.33',
          J: '89.60'
        },
        BOLL: {
          UPPER: '127.85',
          MID: '121.69',
          LOWER: '115.53'
        },
        EMA: {
          EMA12: '120.45',
          EMA26: '118.32'
        },
        SAR: {
          VALUE: '119.85'
        },
        KC: {
          UPPER: '125.40',
          MID: '121.69',
          LOWER: '117.98'
        },
        ARBR: {
          AR: '105.6',
          BR: '89.3'
        }
      },

      // 期权到期日期
      expiryDates: [
        { label: '06/20', value: '06/20' },
        { label: '06/27', value: '06/27', super: 'W' },
        { label: '07/03', value: '07/03', super: 'W' },
        { label: '07/11', value: '07/11', super: 'W' },
        { label: '07/18', value: '07/18' },
        { label: '07/25', value: '07/25', super: 'W' },
        { label: '08/01', value: '08/01' }
      ],

      // 期权链数据
      optionsData: [
        {
          strikePrice: '0.5',
          call: { lastPrice: '144.40', askPrice: '144.10', askSize: '99', bidPrice: '143.15', bidSize: '100', priceClass: 'green' },
          put: { lastPrice: '0.01', askPrice: '--', askSize: '0', bidPrice: '0.01', bidSize: '1.004K', priceClass: '' }
        },
        {
          strikePrice: '1',
          call: { lastPrice: '142.61', askPrice: '144.05', askSize: '103', bidPrice: '142.35', bidSize: '101', priceClass: '' },
          put: { lastPrice: '0.01', askPrice: '--', askSize: '0', bidPrice: '0.01', bidSize: '754', priceClass: '' }
        },
        {
          strikePrice: '1.5',
          call: { lastPrice: '102.20', askPrice: '143.55', askSize: '101', bidPrice: '141.85', bidSize: '101', priceClass: '' },
          put: { lastPrice: '0.01', askPrice: '--', askSize: '0', bidPrice: '0.01', bidSize: '754', priceClass: '' }
        },
        {
          strikePrice: '2',
          call: { lastPrice: '140.00', askPrice: '143.05', askSize: '103', bidPrice: '141.40', bidSize: '101', priceClass: '' },
          put: { lastPrice: '0.01', askPrice: '--', askSize: '0', bidPrice: '0.01', bidSize: '754', priceClass: '' }
        },
        {
          strikePrice: '2.5',
          call: { lastPrice: '101.39', askPrice: '142.55', askSize: '101', bidPrice: '140.85', bidSize: '101', priceClass: '' },
          put: { lastPrice: '0.01', askPrice: '--', askSize: '0', bidPrice: '0.01', bidSize: '754', priceClass: '' }
        },
        {
          strikePrice: '3',
          call: { lastPrice: '138.90', askPrice: '142.05', askSize: '103', bidPrice: '140.40', bidSize: '101', priceClass: '' },
          put: { lastPrice: '0.01', askPrice: '--', askSize: '0', bidPrice: '0.01', bidSize: '754', priceClass: '' }
        },
        {
          strikePrice: '3.5',
          call: { lastPrice: '91.80', askPrice: '141.55', askSize: '101', bidPrice: '139.85', bidSize: '101', priceClass: '' },
          put: { lastPrice: '0.01', askPrice: '--', askSize: '0', bidPrice: '0.01', bidSize: '754', priceClass: '' }
        },
        {
          strikePrice: '4',
          call: { lastPrice: '138.86', askPrice: '141.05', askSize: '101', bidPrice: '139.35', bidSize: '101', priceClass: '' },
          put: { lastPrice: '0.02', askPrice: '--', askSize: '0', bidPrice: '0.01', bidSize: '754', priceClass: '' }
        },
        {
          strikePrice: '4.5',
          call: { lastPrice: '109.47', askPrice: '140.55', askSize: '101', bidPrice: '138.90', bidSize: '101', priceClass: '' },
          put: { lastPrice: '0.01', askPrice: '--', askSize: '0', bidPrice: '0.01', bidSize: '754', priceClass: '' }
        },
        {
          strikePrice: '5',
          call: { lastPrice: '139.41', askPrice: '140.00', askSize: '142', bidPrice: '138.40', bidSize: '130', priceClass: 'green' },
          put: { lastPrice: '0.01', askPrice: '--', askSize: '0', bidPrice: '0.01', bidSize: '754', priceClass: '' }
        }
      ]
    }
  },
  components: {
    KLineComponent,
    MarketDepth
  },
  created () {
    const { query } = this.$route
    this.kLineDetails = query

    // 使用mock数据
    this.stockData = {
      ...this.stockData,
      ...query
    }

    this.getSingDetails()

    // 加载 Polygon API 数据
    if (query.symbol) {
      this.loadPolygonStockData(query.symbol)
    }
    this.calculateIndicators()

    // 连接技术指标WebSocket
    this.connectIndicatorWebSocket()
  },

  beforeDestroy () {
    // 断开技术指标WebSocket连接
    disconnectIndicatorWebSocket()

    // 断开实时数据WebSocket连接
    this.disconnectRealTimeData()
  },
  methods: {
    // 加载 Polygon API 股票数据
    async loadPolygonStockData (ticker) {
      try {
        // 设置加载状态
        this.polygonLoading = {
          details: true,
          snapshot: true,
          related: true,
          news: true,
          aggregates: true,
          weeklyAggregates: true,
          openClose: true,
          prevClose: true
        }

        // 并发调用多个 Polygon API 接口
        const [
          tickerDetails,
          tickerSnapshot,
          relatedTickers,
          tickerNews,
          aggregatesData,
          weeklyAggregatesData,
          dailyOpenClose,
          previousClose
        ] = await Promise.all([
          // 获取股票详细信息
          getTickerDetails({ ticker }).catch(e => {
            return { error: e }
          }),

          // 获取股票快照
          getTickerSnapshot({ ticker }).catch(e => {
            return { error: e }
          }),

          // 获取相关股票
          getRelatedTickers({ ticker }).catch(e => {
            return { error: e }
          }),

          // 获取股票新闻
          getTickerNews({
            ticker,
            order: 'desc',
            limit: 10,
            sort: 'published_utc'
          }).catch(e => {
            return { error: e }
          }),

          // 获取K线数据 (30天)
          getAggregates({
            ticker,
            multiplier: 1,
            timespan: 'day',
            from: this.getDateDaysAgo(30), // 30天前
            to: this.getTodayDate(),
            adjusted: true,
            sort: 'asc',
            limit: 30
          }).catch(e => {
            return { error: e }
          }),

          // 获取52周K线数据 (用于计算52周高低点)
          getAggregates({
            ticker,
            multiplier: 1,
            timespan: 'week',
            from: this.getDateDaysAgo(365), // 365天前
            to: this.getTodayDate(),
            adjusted: true,
            sort: 'asc',
            limit: 52
          }).catch(e => {
            return { error: e }
          }),

          // 获取日开收数据
          getDailyOpenClose({
            ticker,
            date: this.getYesterdayDate(),
            adjusted: true
          }).catch(e => {
            return { error: e }
          }),

          // 获取前收盘价
          getPreviousClose({
            ticker,
            adjusted: true
          }).catch(e => {
            return { error: e }
          })
        ])

        // 处理股票详细信息
        if (tickerDetails && !tickerDetails.error && tickerDetails.data && tickerDetails.data.results) {
          this.polygonData.tickerDetails = tickerDetails.data.results
          const details = tickerDetails.data.results

          // 更新页面显示的股票信息
          this.stockData = {
            ...this.stockData,
            name: details.name || this.stockData.name,
            symbol: details.ticker || this.stockData.symbol,
            market: details.primary_exchange || this.stockData.market,
            description: details.description || null,
            homepage: details.homepage_url || null,
            employees: details.total_employees || null,
            listDate: details.list_date || null,
            // 市值相关
            marketCapValue: details.market_cap || null,
            marketCap: details.market_cap ? this.formatMarketCap(details.market_cap) : this.stockData.marketCap,
            sharesOutstanding: details.share_class_shares_outstanding ? this.formatShares(details.share_class_shares_outstanding) : null,
            shareFloat: details.weighted_shares_outstanding ? this.formatShares(details.weighted_shares_outstanding) : null,
            // 其他公司信息
            sicCode: details.sic_code || null,
            sicDescription: details.sic_description || null,
            currencyName: details.currency_name || 'USD',
            roundLot: details.round_lot || 100,
            phoneNumber: details.phone_number || null,
            // 注意：52周高低点数据在 ticker details 中不存在，需要从 snapshot 或其他API获取
            weekHigh52: null, // 将在后续的 snapshot 处理中更新
            weekLow52: null // 将在后续的 snapshot 处理中更新
          }
        }

        // 处理股票快照数据
        if (tickerSnapshot && !tickerSnapshot.error && tickerSnapshot.data && tickerSnapshot.data.results) {
          this.polygonData.tickerSnapshot = tickerSnapshot.data.results
          const snapshot = tickerSnapshot.data.results

          // 更新实时价格信息
          if (snapshot.lastTrade) {
            this.stockData.price = snapshot.lastTrade.p.toFixed(3)
          }
          if (snapshot.prevDailyBar) {
            this.stockData.prevClose = snapshot.prevDailyBar.c.toFixed(3)
            this.stockData.open = snapshot.prevDailyBar.o.toFixed(3)
            this.stockData.high = snapshot.prevDailyBar.h.toFixed(3)
            this.stockData.low = snapshot.prevDailyBar.l.toFixed(3)
            this.stockData.volume = this.formatVolume(snapshot.prevDailyBar.v)

            // 计算振幅 = (最高价 - 最低价) / 昨收盘价 * 100%
            if (snapshot.prevDailyBar.h && snapshot.prevDailyBar.l && snapshot.prevDailyBar.c) {
              const amplitude = ((snapshot.prevDailyBar.h - snapshot.prevDailyBar.l) / snapshot.prevDailyBar.c * 100).toFixed(2)
              this.stockData.amplitude = amplitude + '%'
            }

            // 平均价 ≈ (最高+最低+收盘)/3
            if (snapshot.prevDailyBar.h && snapshot.prevDailyBar.l && snapshot.prevDailyBar.c) {
              const avgPrice = ((snapshot.prevDailyBar.h + snapshot.prevDailyBar.l + snapshot.prevDailyBar.c) / 3).toFixed(3)
              this.stockData.avgPrice = avgPrice
            }
          }
          if (snapshot.todaysChange && snapshot.todaysChangePerc) {
            this.stockData.change = snapshot.todaysChange.toFixed(3)
            this.stockData.changePercent = snapshot.todaysChangePerc.toFixed(2)
          }

          // 处理盘前/盘后数据
          if (snapshot.session && snapshot.session.close) {
            this.stockData.closeTime = this.formatMarketTime(snapshot.session.close)
          }
        }

        // 处理相关股票数据
        if (relatedTickers && !relatedTickers.error && relatedTickers.data && relatedTickers.data.results) {
          this.polygonData.relatedTickers = relatedTickers.data.results
        }

        // 处理股票新闻数据
        if (tickerNews && !tickerNews.error && tickerNews.data && tickerNews.data.results) {
          this.polygonData.tickerNews = tickerNews.data.results
        }

        // 处理K线数据
        if (aggregatesData && !aggregatesData.error && aggregatesData.data && aggregatesData.data.results) {
          this.polygonData.aggregatesData = aggregatesData.data
          const results = aggregatesData.data.results

          if (results.length > 0) {
            const latestBar = results[results.length - 1]

            // 从聚合数据计算交易额 (成交量 * 平均价格)
            if (latestBar.v && latestBar.vw) {
              const tradeAmount = latestBar.v * latestBar.vw
              this.stockData.tradeAmount = this.formatMarketCap(tradeAmount)
            }

            // 从30天历史数据中计算最高和最低（作为短期参考）
            let periodHigh = Math.max(...results.map(bar => bar.h))
            let periodLow = Math.min(...results.map(bar => bar.l))

            // 注意：优先使用52周数据，如果52周数据还未处理完成，临时使用30天数据
            if (!this.stockData.weekHigh52) {
              this.stockData.weekHigh52 = periodHigh.toFixed(3)
            }
            if (!this.stockData.weekLow52) {
              this.stockData.weekLow52 = periodLow.toFixed(3)
            }
          }
        }

        // 处理日开收数据
        if (dailyOpenClose && !dailyOpenClose.error && dailyOpenClose.data) {
          this.polygonData.dailyOpenClose = dailyOpenClose.data
          const dailyData = dailyOpenClose.data

          // 使用更精确的日开收数据更新股票信息
          if (dailyData.open) {
            this.stockData.open = dailyData.open.toFixed(3)
          }
          if (dailyData.close) {
            this.stockData.price = dailyData.close.toFixed(3)
          }
          if (dailyData.high) {
            this.stockData.high = dailyData.high.toFixed(3)
          }
          if (dailyData.low) {
            this.stockData.low = dailyData.low.toFixed(3)
          }
          if (dailyData.volume) {
            this.stockData.volume = this.formatVolume(dailyData.volume)
          }
        }

        // 处理前收盘价数据
        if (previousClose && !previousClose.error && previousClose.data && previousClose.data.results) {
          this.polygonData.previousClose = previousClose.data.results[0]
          const prevData = previousClose.data.results[0]

          // 使用前收盘价数据
          if (prevData.c) {
            this.stockData.prevClose = prevData.c.toFixed(3)

            // 重新计算涨跌幅（如果有当前价格的话）
            if (this.stockData.price) {
              const currentPrice = parseFloat(this.stockData.price)
              const prevClosePrice = prevData.c
              this.stockData.change = (currentPrice - prevClosePrice).toFixed(3)
              this.stockData.changePercent = ((currentPrice - prevClosePrice) / prevClosePrice * 100).toFixed(2)
            }
          }
        }

        // 处理52周K线数据
        if (weeklyAggregatesData && !weeklyAggregatesData.error && weeklyAggregatesData.data && weeklyAggregatesData.data.results) {
          const weeklyResults = weeklyAggregatesData.data.results

          if (weeklyResults.length > 0) {
            // 计算真正的52周最高和最低价
            const week52High = Math.max(...weeklyResults.map(bar => bar.h))
            const week52Low = Math.min(...weeklyResults.map(bar => bar.l))

            // 更新52周高低点数据
            this.stockData.weekHigh52 = week52High.toFixed(3)
            this.stockData.weekLow52 = week52Low.toFixed(3)
            this.stockData.allTimeHigh = week52High.toFixed(3) // 使用52周最高作为历史最高的近似值
            this.stockData.allTimeLow = week52Low.toFixed(3) // 使用52周最低作为历史最低的近似值
          }
        }

        // 重置加载状态
        this.polygonLoading = {
          details: false,
          snapshot: false,
          related: false,
          news: false,
          aggregates: false,
          weeklyAggregates: false,
          openClose: false,
          prevClose: false
        }
      } catch (error) {
        // 重置加载状态
        this.polygonLoading = {
          details: false,
          snapshot: false,
          related: false,
          news: false,
          aggregates: false,
          weeklyAggregates: false,
          openClose: false,
          prevClose: false
        }
      }
    },

    // 工具方法：获取今天日期
    getTodayDate () {
      return new Date().toISOString().split('T')[0]
    },

    // 工具方法：获取昨天日期
    getYesterdayDate () {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      return yesterday.toISOString().split('T')[0]
    },

    // 工具方法：获取N天前的日期
    getDateDaysAgo (days) {
      const date = new Date()
      date.setDate(date.getDate() - days)
      return date.toISOString().split('T')[0]
    },

    // 工具方法：格式化成交量
    formatVolume (volume) {
      if (volume >= 1e9) {
        return (volume / 1e9).toFixed(1) + '亿'
      } else if (volume >= 1e8) {
        return (volume / 1e8).toFixed(1) + '亿'
      } else if (volume >= 1e4) {
        return (volume / 1e4).toFixed(1) + '万'
      }
      return volume.toString()
    },

    // 工具方法：格式化市值
    formatMarketCap (marketCap) {
      if (!marketCap) return '--'
      if (marketCap >= 1e12) {
        return (marketCap / 1e12).toFixed(2) + '万亿'
      } else if (marketCap >= 1e8) {
        return (marketCap / 1e8).toFixed(2) + '亿'
      } else if (marketCap >= 1e4) {
        return (marketCap / 1e4).toFixed(2) + '万'
      }
      return marketCap.toString()
    },

    // 工具方法：格式化股份数
    formatShares (shares) {
      if (!shares) return '--'
      if (shares >= 1e8) {
        return (shares / 1e8).toFixed(2) + '亿股'
      } else if (shares >= 1e4) {
        return (shares / 1e4).toFixed(2) + '万股'
      }
      return shares.toString() + '股'
    },

    // 工具方法：格式化数字
    formatNumber (number) {
      if (!number) return '--'
      return number.toLocaleString()
    },

    // 工具方法：格式化市场时间
    formatMarketTime (timestamp) {
      if (!timestamp) return '--'
      const date = new Date(timestamp)
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${month}/${day} ${hours}:${minutes}:${seconds}`
    },

    // 工具方法：格式化新闻时间
    formatNewsTime (timestamp) {
      if (!timestamp) return '--'
      const date = new Date(timestamp)
      const now = new Date()
      const diffMs = now - date
      const diffMins = Math.floor(diffMs / (1000 * 60))
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

      if (diffMins < 60) {
        return `${diffMins}分钟前`
      } else if (diffHours < 24) {
        return `${diffHours}小时前`
      } else if (diffDays < 7) {
        return `${diffDays}天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    },

    // 打开新闻
    openNews (news) {
      if (news.article_url) {
        window.open(news.article_url, '_blank')
      }
    },

    // 切换主标签
    switchTab (tab) {
      this.activeTab = tab
      if (tab === 'options') {
        this.loadOptionsData()
      }
    },

    // 切换期权子标签
    switchOptionsTab (subTab) {
      this.optionsSubTab = subTab
      this.loadOptionsData()
    },

    // 选择到期日
    selectExpiry (expiry) {
      this.selectedExpiry = expiry
      this.loadOptionsData()
    },

    // 切换期权筛选
    switchOptionsFilter (filter) {
      this.optionsFilter = filter
    },

    // 加载期权数据
    async loadOptionsData () {
      try {
        // 如果api.js中有期权接口，可以在这里调用
        // 现在使用模拟数据
      } catch (error) {
      }
    },

    // 跳转到期权详情
    goToOptionDetail (option) {
      // 可以跳转到期权交易页面
    },

    // 切换技术指标
    toggleIndicator (indicator) {
      const index = this.activeIndicators.indexOf(indicator)
      if (index > -1) {
        // 如果指标已激活，则移除
        this.activeIndicators.splice(index, 1)
      } else {
        // 如果指标未激活，则添加
        this.activeIndicators.push(indicator)
      }

      // 更新技术指标数据
      this.updateIndicatorData()
    },

    // 更新技术指标数据
    async updateIndicatorData () {
      try {
        // 这里可以调用API获取技术指标数据
        // 现在使用模拟数据

        // 模拟计算技术指标
        this.calculateIndicators()
      } catch (error) {
      }
    },

    // 计算技术指标（使用真实算法）
    calculateIndicators () {
      try {
        // 生成模拟K线数据用于计算技术指标
        const currentPrice = parseFloat(this.stockData.price)
        const mockData = generateMockKLineData(100, currentPrice)

        // 计算MACD指标
        const macdResult = calculateMACD(mockData.closes)
        this.indicatorData.MACD = {
          DIF: macdResult.DIF.toFixed(3),
          DEA: macdResult.DEA.toFixed(3),
          MACD: macdResult.MACD.toFixed(3)
        }

        // 计算KDJ指标
        const kdjResult = calculateKDJ(mockData.highs, mockData.lows, mockData.closes)
        this.indicatorData.KDJ = {
          K: kdjResult.K.toFixed(2),
          D: kdjResult.D.toFixed(2),
          J: kdjResult.J.toFixed(2)
        }

        // 计算BOLL指标
        const bollResult = calculateBOLL(mockData.closes)
        this.indicatorData.BOLL = {
          UPPER: bollResult.UPPER.toFixed(2),
          MID: bollResult.MID.toFixed(2),
          LOWER: bollResult.LOWER.toFixed(2)
        }

        // 计算EMA指标
        const emaResult = calculateEMA(mockData.closes)
        this.indicatorData.EMA = {
          EMA12: emaResult.EMA12.toFixed(2),
          EMA26: emaResult.EMA26.toFixed(2)
        }

        // 计算SAR指标
        const sarResult = calculateSAR(mockData.highs, mockData.lows)
        this.indicatorData.SAR = {
          VALUE: sarResult.VALUE.toFixed(2)
        }

        // 计算KC指标
        const kcResult = calculateKC(mockData.highs, mockData.lows, mockData.closes)
        this.indicatorData.KC = {
          UPPER: kcResult.UPPER.toFixed(2),
          MID: kcResult.MID.toFixed(2),
          LOWER: kcResult.LOWER.toFixed(2)
        }

        // 计算ARBR指标
        const arbrResult = calculateARBR(mockData.highs, mockData.lows, mockData.closes, mockData.opens)
        this.indicatorData.ARBR = {
          AR: arbrResult.AR.toFixed(1),
          BR: arbrResult.BR.toFixed(1)
        }
      } catch (error) {
        // 如果计算失败，使用默认值
        this.useDefaultIndicatorData()
      }
    },

    // 使用默认技术指标数据
    useDefaultIndicatorData () {
      this.indicatorData = {
        MACD: { DIF: '4.075', DEA: '2.987', MACD: '2.176' },
        KDJ: { K: '75.42', D: '68.33', J: '89.60' },
        BOLL: { UPPER: '127.85', MID: '121.69', LOWER: '115.53' },
        EMA: { EMA12: '120.45', EMA26: '118.32' },
        SAR: { VALUE: '119.85' },
        KC: { UPPER: '125.40', MID: '121.69', LOWER: '117.98' },
        ARBR: { AR: '105.6', BR: '89.3' }
      }
    },

    // 连接技术指标WebSocket
    connectIndicatorWebSocket () {
      const symbol = this.stockData.symbol || 'FUTU'

      // 连接WebSocket
      connectIndicatorWebSocket(symbol)

      // 订阅技术指标数据
      subscribeIndicators(this.activeIndicators, this.onIndicatorDataUpdate)
    },

    // 断开实时数据WebSocket连接
    disconnectRealTimeData () {
      try {
        const symbol = this.stockData.symbol || 'FUTU'

        // 取消订阅实时数据
        if (symbol) {
          unsubscribeRealTime(symbol)
        }

        // 断开WebSocket连接
        disconnectRealTimeWS()
      } catch (error) {
      }
    },

    // 连接实时数据WebSocket
    connectRealTimeData () {
      try {
        const symbol = this.stockData.symbol || 'FUTU'
        const userId = this.$store.getters.userId || 'anonymous'

        // 连接WebSocket
        connectRealTimeWS(userId)

        // 订阅实时价格更新
        subscribeRealTime(symbol, (data) => {
          try {
            // 更新股票价格数据
            if (data.data && data.data.price) {
              this.stockData.price = data.data.price
              this.stockData.change = data.data.change
              this.stockData.changePercent = data.data.changePercent

              // 更新显示
              this.$forceUpdate()
            }
          } catch (error) {
          }
        })
      } catch (error) {
      }
    },

    // 技术指标数据更新回调
    onIndicatorDataUpdate (data) {
      const { indicator, data: indicatorData } = data

      if (this.activeIndicators.includes(indicator)) {
        // 更新对应的技术指标数据
        if (this.indicatorData[indicator]) {
          // 格式化数据
          switch (indicator) {
            case 'MACD':
              this.indicatorData.MACD = {
                DIF: indicatorData.DIF.toFixed(3),
                DEA: indicatorData.DEA.toFixed(3),
                MACD: indicatorData.MACD.toFixed(3)
              }
              break
            case 'KDJ':
              this.indicatorData.KDJ = {
                K: indicatorData.K.toFixed(2),
                D: indicatorData.D.toFixed(2),
                J: indicatorData.J.toFixed(2)
              }
              break
            case 'BOLL':
              this.indicatorData.BOLL = {
                UPPER: indicatorData.UPPER.toFixed(2),
                MID: indicatorData.MID.toFixed(2),
                LOWER: indicatorData.LOWER.toFixed(2)
              }
              break
            case 'EMA':
              this.indicatorData.EMA = {
                EMA12: indicatorData.EMA12.toFixed(2),
                EMA26: indicatorData.EMA26.toFixed(2)
              }
              break
            case 'SAR':
              this.indicatorData.SAR = {
                VALUE: indicatorData.VALUE.toFixed(2)
              }
              break
            case 'KC':
              this.indicatorData.KC = {
                UPPER: indicatorData.UPPER.toFixed(2),
                MID: indicatorData.MID.toFixed(2),
                LOWER: indicatorData.LOWER.toFixed(2)
              }
              break
            case 'ARBR':
              this.indicatorData.ARBR = {
                AR: indicatorData.AR.toFixed(1),
                BR: indicatorData.BR.toFixed(1)
              }
              break
          }
        }
      }
    },

    goBuy () {
      this.$router.push({
        path: '/futu-trade',
        query: {
          name: this.stockData.name,
          code: this.stockData.symbol,
          price: this.stockData.price,
          symbol: this.stockData.symbol
        }
      })
    },
    async getSingDetails () {
      try {
        const stockCode = this.stockData.symbol || this.kLineDetails.code

        // 测试个股详情接口
        let response
        if (this.stockData.type === 'US') {
          // 美股使用美股详情接口
          response = await getUsDetail({ code: stockCode })
        } else {
          // 其他股票使用通用接口
          response = await getSingleStock({ code: stockCode })
        }

        if (response && response.success && response.data) {
          this.singDetails = this.adaptStockDetailData(response.data)
          this.updateStockData(response.data)
        } else {
          this.useDefaultStockDetail()
        }

        // 检查是否已添加自选
        this.checkIsOption(stockCode)
      } catch (error) {
        this.useDefaultStockDetail()
      }
    },

    // 适配股票详情数据
    adaptStockDetailData (data) {
      return {
        name: data.name || data.stockName || this.stockData.name,
        gid: data.code || data.stockCode || this.stockData.symbol,
        nowPrice: data.price || data.nowPrice || data.currentPrice,
        hcrate: data.changePercent || data.changeRate,
        open_px: data.open || data.openPrice,
        today_max: data.high || data.highPrice,
        today_min: data.low || data.lowPrice,
        preclose_px: data.preClose || data.preClosePrice,
        business_amount: data.volume || data.tradeVolume,
        business_balance: data.amount || data.tradeAmount,
        type: data.type || this.stockData.type,
        pe: data.pe,
        pb: data.pb,
        marketCap: data.marketCap,
        eps: data.eps,
        dividend: data.dividend,
        dividendYield: data.dividendYield
      }
    },

    // 更新股票基本数据
    updateStockData (data) {
      // 更新页面显示的股票基本信息
      if (data.price || data.nowPrice) {
        this.stockData.price = data.price || data.nowPrice
      }
      if (data.change || data.changeAmount) {
        this.stockData.change = data.change || data.changeAmount
      }
      if (data.changePercent || data.changeRate) {
        this.stockData.changePercent = data.changePercent || data.changeRate
      }
      if (data.high || data.highPrice) {
        this.stockData.high = data.high || data.highPrice
      }
      if (data.low || data.lowPrice) {
        this.stockData.low = data.low || data.lowPrice
      }
      if (data.open || data.openPrice) {
        this.stockData.open = data.open || data.openPrice
      }
      if (data.preClose || data.preClosePrice) {
        this.stockData.prevClose = data.preClose || data.preClosePrice
      }
      if (data.volume || data.tradeVolume) {
        this.stockData.volume = data.volume || data.tradeVolume
      }
    },

    // 使用默认股票详情数据
    useDefaultStockDetail () {
      this.singDetails = this.getDefaultStockDetail()
    },

    // 获取默认股票详情数据
    getDefaultStockDetail () {
      return {
        name: this.stockData.name,
        gid: this.stockData.symbol,
        nowPrice: this.stockData.price,
        hcrate: this.stockData.changePercent,
        open_px: this.stockData.open,
        today_max: this.stockData.high,
        today_min: this.stockData.low,
        preclose_px: this.stockData.prevClose,
        business_amount: '2342000000',
        business_balance: '354000000000',
        type: this.stockData.type
      }
    },

    // 检查是否已添加自选
    async checkIsOption (stockCode) {
      try {
        const response = await isOption({ code: stockCode })
        this.isOptionOpt = response && response.success && response.data
      } catch (error) {
      }
    },

    // 处理K线组件的价格更新
    handlePriceUpdate (priceData) {
      if (priceData) {
        this.stockData.price = priceData.price || this.stockData.price
        this.stockData.change = priceData.change || this.stockData.change
        this.stockData.changePercent = priceData.changePercent || this.stockData.changePercent
      }
    },

    // 切换K线时间周期
    changeTimePeriod (periodKey) {
      if (this.currentTimePeriod === periodKey) return

      console.log('🔄 [FutuStockDetail] 切换时间周期:', periodKey)
      this.currentTimePeriod = periodKey
    }
  }
}
</script>

<style scoped>
.futu-stock-detail {
  background: #ffffff;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 通用颜色类 */
.red {
  color: #ff4444 !important;
}

.green {
  color: #00aa44 !important;
}

/* 状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  background: #000;
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signal-bars {
  display: flex;
  gap: 2px;
}

.signal-bars .bar {
  width: 3px;
  height: 12px;
  background: #666;
  border-radius: 1px;
}

.signal-bars .bar.active {
  background: white;
}

.battery {
  display: flex;
  align-items: center;
  gap: 4px;
}

.battery-icon {
  width: 20px;
  height: 10px;
  border: 1px solid white;
  border-radius: 2px;
  position: relative;
}

.battery-icon::after {
  content: '';
  position: absolute;
  right: -3px;
  top: 3px;
  width: 2px;
  height: 4px;
  background: white;
  border-radius: 0 1px 1px 0;
}

/* 顶部导航栏 */
.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.nav-left, .nav-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.ai-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #000;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
}

/* Tab栏 */
.main-tabs {
  display: flex;
  padding: 0 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  padding: 12px 16px;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  color: #000;
  border-bottom-color: #ff4444;
}

/* 股票信息区域 */
.stock-info {
  padding: 20px;
  background: white;
}

.stock-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.stock-name h2 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #000;
}

.market-time {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.market-icons {
  display: flex;
  gap: 6px;
}

.market-icons > div {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 500;
}

.icon-us {
  font-size: 14px;
}

.icon-realtime {
  background: #007AFF;
  color: white;
  font-size: 8px;
}

.icon-24h {
  background: #34C759;
  color: white;
  font-size: 8px;
}

.icon-history {
  background: #FF9500;
  color: white;
  font-size: 8px;
}

.icon-alert {
  background: #FF3B30;
  color: white;
  font-size: 8px;
}

.icon-more {
  background: #8E8E93;
  color: white;
  font-size: 8px;
}

.price-section {
  margin-bottom: 20px;
}

.main-price {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.price {
  font-size: 32px;
  font-weight: 600;
  color: #ff4444;
}

.price.up {
  color: #ff4444;
}

.price.down {
  color: #00aa44;
}

.change-icon {
  font-size: 16px;
  color: #ff4444;
}

.change-info {
  display: flex;
  gap: 12px;
}

.change-amount, .change-percent {
  font-size: 14px;
  color: #ff4444;
}

.stock-metrics {
  margin-bottom: 16px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  flex: 1;
  padding: 0 4px;
  min-width: 0;
}

.metric-item .label {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
}

.metric-item .value {
  font-size: 13px;
  color: #000;
  font-weight: 500;
  text-align: right;
  margin-left: 8px;
}

.metric-item .value.green {
  color: #00aa44;
}

.metric-item .value.red {
  color: #ff4444;
}

.premarket-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  font-size: 14px;
}

.premarket-icon {
  font-size: 16px;
}

.premarket-label {
  color: #666;
}

.premarket-price {
  color: #00aa44;
  font-weight: 500;
}

.premarket-price.green {
  color: #00aa44;
}

.premarket-change, .premarket-percent {
  color: #00aa44;
}

.premarket-change.green, .premarket-percent.green {
  color: #00aa44;
}

.premarket-time {
  color: #666;
  margin-left: auto;
}

.premarket-arrow {
  color: #666;
  font-size: 16px;
}

.earnings-alert {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  font-size: 14px;
  color: #0066cc;
}

.earnings-icon {
  font-size: 16px;
}

.earnings-calendar {
  margin-left: auto;
  font-size: 16px;
}

.earnings-expand {
  color: #666;
  font-size: 16px;
}

.ai-news-section {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  font-size: 14px;
  color: #666;
}

.ai-news-icon {
  font-size: 16px;
  color: #ff4444;
}

.ai-news-text {
  flex: 1;
  line-height: 1.4;
}

/* K线图控制栏 */
.chart-controls {
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.time-controls {
  display: flex;
  gap: 16px;
  overflow-x: auto;
}

.control-btn {
  padding: 6px 12px;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  border-radius: 4px;
}

.control-btn.active {
  background: #f0f0f0;
  color: #000;
  font-weight: 600;
}

.control-btn.chart-icon {
  color: #666;
  font-size: 16px;
}

.control-btn.more-btn {
  color: #999;
  font-weight: bold;
}

/* MA指标信息 */
.ma-indicators {
  padding: 8px 20px;
  background: white;
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.ma-item {
  color: #000;
  font-weight: 500;
}

.ma-value {
  color: #666;
}

.ma-value.ma5 {
  color: #ff6600;
}

.ma-value.ma10 {
  color: #9966ff;
}

/* K线图区域 */
.chart-container {
  height: 400px;
  background: white;
  margin: 0;
  position: relative;
  display: flex;
  flex-direction: column;
}

.chart-volume {
  position: absolute;
  bottom: 10px;
  left: 10px;
  display: flex;
  gap: 8px;
  font-size: 12px;
  align-items: center;
}

.chart-volume .volume-label {
  color: #666;
  font-weight: 500;
}

.chart-volume .volume-value {
  color: #ff4444;
  font-weight: 500;
}

.chart-volume .volume-value.red {
  color: #ff4444;
}

.chart-volume .volume-shares {
  color: #666;
}

/* 技术指标栏 */
.tech-indicators {
  padding: 12px 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.indicator-tabs {
  display: flex;
  gap: 16px;
  overflow-x: auto;
}

.indicator-tab {
  padding: 6px 12px;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  border-radius: 4px;
}

.indicator-tab.active {
  background: #f0f0f0;
  color: #ff6600;
}

/* 技术指标数据显示 */
.indicators-data {
  padding: 8px 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
  max-height: 120px;
  overflow-y: auto;
}

.indicator-data {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 12px;
}

.indicator-name {
  color: #000;
  font-weight: 500;
  min-width: 80px;
}

.indicator-value {
  color: #666;
  font-weight: 500;
}

/* MACD指标颜色 */
.macd-dif {
  color: #ff6600;
}

.macd-dea {
  color: #00aa00;
}

.macd-macd {
  color: #0066ff;
}

/* KDJ指标颜色 */
.kdj-k {
  color: #ff6600;
}

.kdj-d {
  color: #00aa00;
}

.kdj-j {
  color: #9966ff;
}

/* BOLL指标颜色 */
.boll-upper {
  color: #ff4444;
}

.boll-mid {
  color: #666;
}

.boll-lower {
  color: #00aa00;
}

/* EMA指标颜色 */
.ema-12 {
  color: #ff6600;
}

.ema-26 {
  color: #0066ff;
}

/* SAR指标颜色 */
.sar-value {
  color: #9966ff;
}

/* KC指标颜色 */
.kc-upper {
  color: #ff4444;
}

.kc-mid {
  color: #666;
}

.kc-lower {
  color: #00aa00;
}

/* ARBR指标颜色 */
.arbr-ar {
  color: #ff6600;
}

.arbr-br {
  color: #00aa00;
}

/* 买卖盘 */
.order-book {
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.order-title {
  font-size: 16px;
  font-weight: 500;
  color: #000;
}

.order-icon {
  font-size: 20px;
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-left {
  display: flex;
  align-items: center;
}

.order-name {
  font-size: 14px;
  color: #000;
  font-weight: 500;
}

.order-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-price {
  font-size: 16px;
  font-weight: 600;
  color: #000;
}

.order-change {
  font-size: 14px;
  color: #ff4444;
  font-weight: 500;
}

.order-change.red {
  color: #ff4444;
}

.order-percent {
  font-size: 14px;
  color: #ff4444;
  font-weight: 500;
}

.order-percent.red {
  color: #ff4444;
}

.order-trend {
  font-size: 16px;
  color: #666;
}

/* 底部操作按钮 */
.bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 12px 24px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
}

.action-btn.primary {
  background: #ff6600;
  color: white;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #000;
}

.action-icons {
  display: flex;
  gap: 20px;
  color: #666;
  font-size: 20px;
}

/* 期权页面样式 */
.options-container {
  background: white;
  margin-top: 8px;
}

/* 期权子标签 */
.options-subtabs {
  display: flex;
  align-items: center;
  padding: 0 20px;
  background: #f8f8f8;
  border-bottom: 1px solid #e8e8e8;
}

.subtab-item {
  padding: 12px 16px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.subtab-item.active {
  color: #000;
  font-weight: 500;
  border-bottom-color: #ff6600;
}

.options-settings {
  margin-left: auto;
  padding: 12px;
  color: #666;
  font-size: 16px;
}

/* 期权筛选条件 */
.options-filters {
  padding: 12px 20px;
  background: white;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.filter-group {
  display: flex;
  gap: 16px;
}

.filter-label {
  padding: 6px 12px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  border-radius: 4px;
}

.filter-label.active {
  background: #fff5f0;
  color: #ff6600;
  font-weight: 500;
}

.filter-icons {
  display: flex;
  gap: 16px;
  color: #666;
  font-size: 16px;
}

/* 期权到期日期 */
.expiry-dates {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 4px 0;
}

.date-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  min-width: 60px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  background: white;
}

.date-item.active {
  border-color: #ff6600;
  background: #fff5f0;
}

.date-text {
  font-size: 14px;
  color: #000;
  font-weight: 500;
}

.date-super {
  font-size: 10px;
  color: #666;
  margin-top: 2px;
}

/* Call/Put 统计信息 */
.options-stats {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: #f8f8f8;
  margin: 0 20px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.stats-left, .stats-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-center {
  flex: 1;
  text-align: center;
}

.stats-label {
  font-size: 14px;
  font-weight: 500;
}

.stats-label.red {
  color: #ff4444;
}

.stats-label.green {
  color: #00aa00;
}

.stats-value {
  font-size: 16px;
  font-weight: 600;
  color: #000;
}

.stats-time {
  font-size: 18px;
  font-weight: 600;
  color: #000;
}

/* 期权链表头 */
.options-table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  padding: 12px 20px;
  background: #f8f8f8;
  border-bottom: 1px solid #e8e8e8;
  font-size: 12px;
  color: #666;
  text-align: center;
}

.header-column {
  font-weight: 500;
}

/* 期权链数据表格 */
.options-table {
  max-height: 400px;
  overflow-y: auto;
}

.options-row {
  display: grid;
  grid-template-columns: 3fr 1fr 3fr;
  padding: 8px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.options-row:hover {
  background: #f8f8f8;
}

.call-side, .put-side {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  align-items: center;
}

.put-side {
  grid-template-columns: 1fr 1fr 1fr;
}

.strike-price {
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #000;
  padding: 0 8px;
}

.option-price {
  font-size: 14px;
  font-weight: 500;
  color: #000;
  text-align: center;
}

.option-price.green {
  color: #00aa00;
}

.option-price.red {
  color: #ff4444;
}

.option-volume {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
}

.ask-price, .bid-price {
  color: #000;
  font-weight: 500;
}

.ask-size, .bid-size {
  color: #666;
  font-size: 11px;
}

/* 单腿期权按钮 */
.single-leg-options {
  padding: 16px 20px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}

.single-leg-btn {
  padding: 12px 24px;
  background: #ff6600;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
}

.single-leg-btn:hover {
  background: #e55a00;
}

/* 股票新闻样式 */
.stock-news-section {
  padding: 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.news-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.news-count {
  font-size: 12px;
  color: #666;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.news-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.news-item:hover {
  background: #f8f8f8;
}

.news-content {
  flex: 1;
  min-width: 0;
}

.news-title {
  font-size: 14px;
  font-weight: 500;
  color: #000;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-description {
  font-size: 12px;
  color: #666;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #999;
}

.news-author {
  font-weight: 500;
}

.news-time {
  margin-left: auto;
}

.news-image {
  width: 80px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 公司信息样式 */
.company-info-section {
  padding: 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.company-header {
  margin-bottom: 16px;
}

.company-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.company-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.company-row {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.company-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.company-value {
  font-size: 14px;
  color: #000;
  line-height: 1.4;
}

.company-description {
  text-align: justify;
}

.company-link {
  color: #0066cc;
  text-decoration: none;
}

.company-link:hover {
  text-decoration: underline;
}

/* 暂无内容提示样式 */
.no-news, .no-company-info {
  padding: 40px 20px;
  text-align: center;
  color: #999;
  background: #f8f8f8;
  border-radius: 8px;
  margin: 20px 0;
}

.no-news p, .no-company-info p {
  margin: 0;
  font-size: 14px;
}

/* 资讯和公司页面的特殊样式 */
.stock-news-section {
  min-height: 60vh;
}

.company-info-section {
  min-height: 60vh;
}

/* Polygon API 数据调试样式 */
.polygon-data-debug {
  margin-top: 16px;
  padding: 12px;
  background: #f0f8ff;
  border: 1px solid #d0e7ff;
  border-radius: 6px;
}

.polygon-data-debug h4 {
  font-size: 12px;
  color: #0066cc;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.debug-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 11px;
}

.debug-label {
  color: #666;
  font-weight: 500;
}

.debug-value {
  color: #0066cc;
  font-family: monospace;
}
</style>
