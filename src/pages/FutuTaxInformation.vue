<template>
  <div class="futu-tax-information">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left">
        <div class="back-icon" @click="goBack">‹</div>
        <span class="title">网上转账开户</span>
      </div>
      <div class="header-right">
        <div class="help-icon">🎧</div>
        <div class="more-icon">⋯</div>
        <div class="message-icon">
          <div class="message-icon-inner">✉️</div>
          <span class="badge">3</span>
        </div>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar">
      <div class="progress-fill" :style="{ width: '95%' }"></div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <h1 class="page-title">税务信息</h1>
      <p class="subtitle">请填写税务相关信息以完成开户申请</p>

      <!-- 税务信息表单 -->
      <div class="section">
        <h2 class="section-title">税务居民身份</h2>

        <div class="form-group">
          <label class="form-label">您是否为香港税务居民？</label>
          <div class="radio-group">
            <label class="radio-label">
              <input
                type="radio"
                v-model="taxInfo.isHKTaxResident"
                value="yes"
                class="radio-input"
              />
              <span class="radio-custom"></span>
              是
            </label>
            <label class="radio-label">
              <input
                type="radio"
                v-model="taxInfo.isHKTaxResident"
                value="no"
                class="radio-input"
              />
              <span class="radio-custom"></span>
              否
            </label>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">您是否为美国税务居民？</label>
          <div class="radio-group">
            <label class="radio-label">
              <input
                type="radio"
                v-model="taxInfo.isUSTaxResident"
                value="yes"
                class="radio-input"
              />
              <span class="radio-custom"></span>
              是
            </label>
            <label class="radio-label">
              <input
                type="radio"
                v-model="taxInfo.isUSTaxResident"
                value="no"
                class="radio-input"
              />
              <span class="radio-custom"></span>
              否
            </label>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">您是否为其他国家或地区的税务居民？</label>
          <div class="radio-group">
            <label class="radio-label">
              <input
                type="radio"
                v-model="taxInfo.isOtherTaxResident"
                value="yes"
                class="radio-input"
                @change="onOtherTaxResidentChange"
              />
              <span class="radio-custom"></span>
              是
            </label>
            <label class="radio-label">
              <input
                type="radio"
                v-model="taxInfo.isOtherTaxResident"
                value="no"
                class="radio-input"
                @change="onOtherTaxResidentChange"
              />
              <span class="radio-custom"></span>
              否
            </label>
          </div>
        </div>

        <!-- 其他税务居民详情 -->
        <div v-if="taxInfo.isOtherTaxResident === 'yes'" class="other-tax-details">
          <div class="form-group">
            <label class="form-label">请选择国家或地区</label>
            <select v-model="taxInfo.otherCountry" class="form-select">
              <option value="">请选择</option>
              <option value="中国大陆">中国大陆</option>
              <option value="澳门">澳门</option>
              <option value="台湾">台湾</option>
              <option value="新加坡">新加坡</option>
              <option value="英国">英国</option>
              <option value="加拿大">加拿大</option>
              <option value="澳洲">澳洲</option>
              <option value="其他">其他</option>
            </select>
          </div>

          <div v-if="taxInfo.otherCountry === '其他'" class="form-group">
            <label class="form-label">请输入国家或地区名称</label>
            <input
              type="text"
              v-model="taxInfo.otherCountryName"
              placeholder="请输入国家或地区名称"
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label class="form-label">税务识别号码（如有）</label>
            <input
              type="text"
              v-model="taxInfo.taxIdentificationNumber"
              placeholder="请输入税务识别号码"
              class="form-input"
            />
          </div>
        </div>
      </div>

      <!-- 纳税人识别号 -->
      <div class="section">
        <h2 class="section-title">纳税人识别号</h2>

        <div class="form-group">
          <label class="form-label">纳税人识别号（通常为身份证号码）</label>
          <input
            type="text"
            v-model="taxInfo.taxpayerIdNumber"
            placeholder="请输入纳税人识别号"
            class="form-input"
          />
        </div>
      </div>

      <!-- W-8表格（如适用）-->
      <div v-if="taxInfo.isUSTaxResident === 'no'" class="section">
        <h2 class="section-title">W-8表格确认</h2>

        <div class="info-note">
          <p>由于您不是美国税务居民，您需要填写W-8表格以享受税务优惠。</p>
        </div>

        <div class="checkbox-group">
          <label class="checkbox-label">
            <input
              type="checkbox"
              v-model="taxInfo.agreeW8Form"
              class="checkbox-input"
            />
            <span class="checkbox-custom"></span>
            我同意在账户开通后填写并提交W-8表格
          </label>
        </div>
      </div>

      <!-- FATCA声明 -->
      <div class="section">
        <h2 class="section-title">FATCA声明</h2>

        <div class="info-note">
          <p>FATCA（海外账户税收合规法案）要求金融机构识别和报告美国税务居民的账户信息。</p>
        </div>

        <div class="checkbox-group">
          <label class="checkbox-label">
            <input
              type="checkbox"
              v-model="taxInfo.understandFATCA"
              class="checkbox-input"
            />
            <span class="checkbox-custom"></span>
            我了解FATCA的相关要求和规定
          </label>
        </div>

        <div class="checkbox-group">
          <label class="checkbox-label">
            <input
              type="checkbox"
              v-model="taxInfo.confirmTaxInfo"
              class="checkbox-input"
            />
            <span class="checkbox-custom"></span>
            我确认所提供的税务信息真实、准确、完整
          </label>
        </div>
      </div>

      <!-- 其他资料披露 -->
      <div class="section">
        <h2 class="section-title">其他资料披露</h2>

        <div class="form-group">
          <label class="form-label">您是否为政治敏感人士（PEP）？</label>
          <div class="radio-group">
            <label class="radio-label">
              <input
                type="radio"
                v-model="taxInfo.isPEP"
                value="yes"
                class="radio-input"
              />
              <span class="radio-custom"></span>
              是
            </label>
            <label class="radio-label">
              <input
                type="radio"
                v-model="taxInfo.isPEP"
                value="no"
                class="radio-input"
              />
              <span class="radio-custom"></span>
              否
            </label>
          </div>
        </div>

        <div class="info-note">
          <p><strong>政治敏感人士（PEP）</strong>是指担任或曾经担任重要公共职务的人士，包括但不限于政府高级官员、政党高级成员、法官、军队高级军官等。</p>
        </div>

        <div class="form-group">
          <label class="form-label">您开设账户的目的是什么？</label>
          <select v-model="taxInfo.accountPurpose" class="form-select">
            <option value="">请选择</option>
            <option value="个人投资">个人投资</option>
            <option value="退休规划">退休规划</option>
            <option value="资产配置">资产配置</option>
            <option value="教育储备">教育储备</option>
            <option value="其他">其他</option>
          </select>
        </div>

        <div v-if="taxInfo.accountPurpose === '其他'" class="form-group">
          <label class="form-label">请说明其他目的</label>
          <textarea
            v-model="taxInfo.otherPurpose"
            placeholder="请详细说明开户目的"
            class="form-textarea"
            rows="3"
          ></textarea>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-container">
        <button
          class="submit-btn"
          :class="{ 'disabled': !canSubmit }"
          @click="submitApplication"
          :disabled="!canSubmit"
        >
          提交开户申请
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FutuTaxInformation',
  data () {
    return {
      taxInfo: {
        isHKTaxResident: '',
        isUSTaxResident: '',
        isOtherTaxResident: '',
        otherCountry: '',
        otherCountryName: '',
        taxIdentificationNumber: '',
        taxpayerIdNumber: '',
        agreeW8Form: false,
        understandFATCA: false,
        confirmTaxInfo: false,
        isPEP: '',
        accountPurpose: '',
        otherPurpose: ''
      }
    }
  },
  computed: {
    canSubmit () {
      const basicFields = [
        this.taxInfo.isHKTaxResident,
        this.taxInfo.isUSTaxResident,
        this.taxInfo.isOtherTaxResident,
        this.taxInfo.taxpayerIdNumber,
        this.taxInfo.isPEP,
        this.taxInfo.accountPurpose
      ].every(field => field !== '')

      // 检查其他税务居民相关字段
      const otherTaxFields = this.taxInfo.isOtherTaxResident === 'yes'
        ? this.taxInfo.otherCountry !== '' &&
          (this.taxInfo.otherCountry !== '其他' || this.taxInfo.otherCountryName !== '')
        : true

      // 检查其他目的字段
      const otherPurposeField = this.taxInfo.accountPurpose === '其他'
        ? this.taxInfo.otherPurpose !== ''
        : true

      // 检查必需的确认项
      const requiredChecks = [
        this.taxInfo.understandFATCA,
        this.taxInfo.confirmTaxInfo
      ].every(checkbox => checkbox === true)

      // 检查W-8表格确认（如适用）
      const w8FormCheck = this.taxInfo.isUSTaxResident === 'no'
        ? this.taxInfo.agreeW8Form === true
        : true

      return basicFields && otherTaxFields && otherPurposeField && requiredChecks && w8FormCheck
    }
  },
  methods: {
    goBack () {
      this.$router.go(-1)
    },
    onOtherTaxResidentChange () {
      if (this.taxInfo.isOtherTaxResident === 'no') {
        this.taxInfo.otherCountry = ''
        this.taxInfo.otherCountryName = ''
        this.taxInfo.taxIdentificationNumber = ''
      }
    },
    submitApplication () {
      if (!this.canSubmit) return


      // 显示成功提示
      alert('开户申请已成功提交！我们将在1-2个工作日内完成审核。')

      // 返回首页或开户成功页面
      this.$router.push('/')
    }
  }
}
</script>

<style scoped>
.futu-tax-information {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部导航 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f5a 100%);
  color: white;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-icon {
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  padding: 4px;
}

.title {
  font-size: 18px;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.help-icon, .more-icon {
  font-size: 20px;
  cursor: pointer;
}

.message-icon {
  position: relative;
  cursor: pointer;
}

.message-icon-inner {
  font-size: 20px;
}

.badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: #ff3b30;
  color: white;
  border-radius: 10px;
  font-size: 12px;
  padding: 2px 6px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 进度条 */
.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  position: relative;
}

.progress-fill {
  height: 100%;
  background: white;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 主要内容 */
.main-content {
  padding: 24px 16px;
  max-width: 600px;
  margin: 0 auto;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  margin-bottom: 8px;
  text-align: center;
}

.subtitle {
  font-size: 16px;
  color: #666;
  text-align: center;
  margin-bottom: 32px;
}

/* 表单区块 */
.section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #ff6b35;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  background: white;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  outline: none;
  border-color: #ff6b35;
  box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1);
}

.form-select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 单选框 */
.radio-group {
  display: flex;
  gap: 24px;
  margin-top: 8px;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 16px;
  color: #333;
}

.radio-input {
  display: none;
}

.radio-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 50%;
  background: white;
  position: relative;
  transition: all 0.2s ease;
}

.radio-input:checked + .radio-custom {
  border-color: #ff6b35;
}

.radio-input:checked + .radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #ff6b35;
  border-radius: 50%;
}

/* 复选框 */
.checkbox-group {
  margin-bottom: 16px;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 4px;
  background: white;
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
  transition: all 0.2s ease;
}

.checkbox-input:checked + .checkbox-custom {
  background: #ff6b35;
  border-color: #ff6b35;
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 其他税务详情 */
.other-tax-details {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

/* 信息提示 */
.info-note {
  background: #f0f8ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.info-note p {
  margin: 0;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

/* 提交按钮 */
.submit-container {
  text-align: center;
  margin-top: 32px;
}

.submit-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f5a 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 48px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
  min-width: 200px;
}

.submit-btn:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4);
}

.submit-btn.disabled {
  background: #ccc;
  cursor: not-allowed;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .main-content {
    padding: 20px 12px;
  }

  .section {
    padding: 20px 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .radio-group {
    flex-direction: column;
    gap: 12px;
  }

  .submit-btn {
    width: 100%;
    min-width: unset;
  }
}
</style>
</code_block_to_apply_changes_from>
