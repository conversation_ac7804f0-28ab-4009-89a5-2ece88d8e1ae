<template>
  <div class="futu-trade">
    <!-- 顶部导航 -->
    <div class="top-nav">
      <div class="nav-left" @click="goBack">
        <i class="back-icon">‹</i>
      </div>
      <div class="nav-title">Paper Trading</div>
      <div class="nav-right" @click="refresh">
        <i class="refresh-icon">↻</i>
      </div>
    </div>

    <!-- 账户信息 -->
    <div class="account-info">
      <div class="account-header">
        <span class="flag">🇺🇸</span>
        <span class="account-type">US Stock Margin Paper Account</span>
      </div>
      <div class="profit-info">
        <span class="profit-label">Today's P&L Ratio</span>
        <span class="profit-value">{{ todayProfitRate }}</span>
        <span class="view-detail" @click="viewDetail">View</span>
      </div>
      <div class="asset-info" v-if="showAssetInfo">
        <span class="asset-label">Net Asset Value · USD</span>
        <span class="asset-value">{{ totalAsset }}</span>
      </div>
    </div>

    <!-- 股票信息 -->
    <div class="stock-info">
      <div class="stock-header">
        <div class="stock-left">
          <div class="stock-symbol">{{ currentStock.symbol }}</div>
          <div class="stock-name">{{ currentStock.name }}</div>
        </div>
        <div class="stock-right">
          <div class="stock-price">{{ currentStock.price }}</div>
          <div class="stock-change" :class="currentStock.changeClass">
            {{ currentStock.change }} {{ currentStock.changePercent }}
          </div>
        </div>
        <div class="stock-chart">
          <svg width="60" height="30" viewBox="0 0 60 30">
            <polyline points="0,25 10,20 20,15 30,18 40,12 50,8 60,10"
                      fill="none" stroke="#00D4AA" stroke-width="1"/>
          </svg>
        </div>
      </div>

      <div class="order-book">
        <div class="bid-ask">
          <div class="bid">
            <span class="label">买盘</span>
            <span class="price">{{ orderBook.bid.price }}</span>
            <span class="volume">{{ orderBook.bid.volume }}</span>
          </div>
          <div class="ask">
            <span class="label">卖盘</span>
            <span class="price">{{ orderBook.ask.price }}</span>
            <span class="volume">{{ orderBook.ask.volume }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 交易方向选择 -->
    <div class="trade-direction">
      <span class="direction-label">方向</span>
      <div class="direction-buttons">
        <button
          class="direction-btn buy-btn"
          :class="{ active: tradeDirection === 'buy' }"
          @click="setTradeDirection('buy')"
        >
          模拟买入
        </button>
        <button
          class="direction-btn sell-btn"
          :class="{ active: tradeDirection === 'sell' }"
          @click="setTradeDirection('sell')"
        >
          模拟卖出
        </button>
      </div>
    </div>

    <!-- 交易表单 -->
    <div class="trade-form">
      <div class="form-row">
        <span class="form-label">类型 <i class="info-icon">ⓘ</i></span>
        <div class="form-input">
          <select v-model="orderType" class="order-type-select">
            <option value="market">市价单</option>
            <option value="limit">限价单</option>
          </select>
          <i class="dropdown-icon">▼</i>
        </div>
      </div>

      <div class="form-row">
        <span class="form-label">数量</span>
        <div class="form-input quantity-input">
          <button class="quantity-btn minus" @click="decreaseQuantity">−</button>
          <input type="number" v-model="quantity" class="quantity-field" min="1">
          <button class="quantity-btn plus" @click="increaseQuantity">+</button>
        </div>
      </div>

      <div class="form-row">
        <span class="form-label">金额</span>
        <div class="form-input">
          <span class="amount-value">{{ totalAmount }}</span>
        </div>
      </div>

      <div class="form-row">
        <span class="form-label">期限</span>
        <div class="form-input">
          <span class="validity-value">当日有效</span>
        </div>
      </div>

      <div class="fund-info">
        <div class="fund-row">
          <span class="fund-label">现金可买</span>
          <span class="fund-value available">{{ availableCash }}</span>
          <span class="fund-label">持仓可卖</span>
          <span class="fund-value position">{{ availablePosition }}</span>
        </div>
        <div class="fund-row">
          <span class="fund-label">最大可买</span>
          <span class="fund-value max-buy">{{ maxBuyable }}</span>
          <span class="fund-label">可卖空</span>
          <span class="fund-value short-sell">{{ shortSellable }} <i class="info-icon">ⓘ</i></span>
        </div>
      </div>
    </div>

    <!-- 交易按钮 -->
    <div class="trade-button-container">
      <button
        class="trade-button"
        :class="tradeDirection === 'buy' ? 'buy-button' : 'sell-button'"
        @click="submitOrder"
      >
        {{ tradeDirection === 'buy' ? '模拟买入' : '模拟卖出' }}
      </button>
      <div class="calculator-icon" @click="showCalculator">
        <i class="calc-icon">🖩</i>
      </div>
    </div>

    <!-- 底部Tab -->
    <div class="bottom-tabs">
      <div class="tab-buttons">
        <button class="tab-btn" :class="{ active: activeTab === 'position' }" @click="setActiveTab('position')">
          Positions({{ positionCount }})
        </button>
        <button class="tab-btn" :class="{ active: activeTab === 'orders' }" @click="setActiveTab('orders')">
          Orders({{ orderCount }})
        </button>
      </div>
      <button class="real-account-btn" @click="switchToRealAccount">Live Account</button>
    </div>

    <!-- 订单状态Tab -->
    <div class="order-status-tabs">
      <button class="status-tab" :class="{ active: orderStatus === 'pending' }" @click="setOrderStatus('pending')">
        Pending({{ pendingOrders.length }})
      </button>
      <button class="status-tab" :class="{ active: orderStatus === 'completed' }" @click="setOrderStatus('completed')">
        Filled/Cancelled({{ completedOrders.length }})
      </button>
    </div>

    <!-- 订单列表 -->
    <div class="order-list">
      <div class="order-header">
        <span class="header-item">Order Status</span>
        <span class="header-item">Name/Symbol</span>
        <span class="header-item">Qty/Price</span>
        <span class="header-item">Order Time</span>
      </div>

      <div class="order-item" v-for="order in currentOrders" :key="order.id">
        <div class="order-status">
          <span class="status-text" :class="order.statusClass">{{ order.statusText }}</span>
          <span class="status-icon" v-if="order.statusIcon">{{ order.statusIcon }}</span>
        </div>
        <div class="order-stock">
          <div class="stock-name">{{ order.stockName }}</div>
          <div class="stock-code">{{ order.stockCode }}</div>
        </div>
        <div class="order-details">
          <div class="order-quantity">{{ order.quantity }}</div>
          <div class="order-price">{{ order.price }}</div>
        </div>
        <div class="order-time">{{ order.time }}</div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="bottom-actions" v-if="orderStatus === 'pending'">
              <button class="action-btn" @click="showQuote">
        <i class="icon">📊</i>
        <span>Quote</span>
      </button>
      <button class="action-btn" @click="modifyOrder">
        <i class="icon">✏️</i>
        <span>Modify</span>
      </button>
      <button class="action-btn" @click="cancelOrder">
        <i class="icon">↻</i>
        <span>Cancel</span>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FutuTrade',
  data () {
    return {
      todayProfitRate: '0.00%',
      totalAsset: '1,000,000.00',
      showAssetInfo: false,

      currentStock: {
        symbol: 'FUTU',
        name: '富途控股',
        price: '111.500',
        change: '-2.620',
        changePercent: '-2.30%',
        changeClass: 'negative'
      },

      orderBook: {
        bid: { price: '111.300', volume: '200' },
        ask: { price: '111.700', volume: '300' }
      },

      tradeDirection: 'buy',
      orderType: 'market',
      quantity: 1,

      availableCash: '7,472',
      availablePosition: '0',
      maxBuyable: '14,943',
      shortSellable: '12,453',

      activeTab: 'orders',
      orderStatus: 'pending',
      positionCount: 0,
      orderCount: 1,

      pendingOrders: [
        {
          id: 1,
          statusText: '模拟买入',
          statusClass: 'buy-status',
          statusIcon: '⏳',
          stockName: '富途控股',
          stockCode: 'FUTU',
          quantity: '1',
          price: '133.80',
          time: '06/14\n13:56:54'
        }
      ],
      completedOrders: []
    }
  },
  computed: {
    totalAmount () {
      return (parseFloat(this.currentStock.price.replace(',', '')) * this.quantity).toFixed(2)
    },
    currentOrders () {
      return this.orderStatus === 'pending' ? this.pendingOrders : this.completedOrders
    }
  },
  created () {
    // 从路由参数获取股票信息
    const { query } = this.$route
    if (query.symbol) {
      this.currentStock = {
        symbol: query.symbol || 'FUTU',
        name: query.name || '富途控股',
        price: query.price || '111.500',
        change: '-2.620',
        changePercent: '-2.30%',
        changeClass: 'negative'
      }
    }
  },
  methods: {
    goBack () { this.$router.go(-1) },
    viewDetail () { this.showAssetInfo = !this.showAssetInfo },
    setTradeDirection (direction) {
      this.tradeDirection = direction
      if (direction === 'sell') {
        this.availableCash = '10,759'
        this.maxBuyable = '21,517'
      } else {
        this.availableCash = '7,472'
        this.maxBuyable = '14,943'
      }
    },
    increaseQuantity () { this.quantity++ },
    decreaseQuantity () { if (this.quantity > 1) this.quantity-- },
    submitOrder () {
      const newOrder = {
        id: Date.now(),
        statusText: this.tradeDirection === 'buy' ? '模拟买入' : '模拟卖出',
        statusClass: this.tradeDirection === 'buy' ? 'buy-status' : 'sell-status',
        statusIcon: '⏳',
        stockName: this.currentStock.name,
        stockCode: this.currentStock.symbol,
        quantity: this.quantity.toString(),
        price: this.totalAmount,
        time: new Date().toLocaleString('zh-CN', {
          month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit'
        }).replace(/\//g, '/').replace(' ', '\n')
      }
      this.pendingOrders.unshift(newOrder)
      this.orderCount = this.pendingOrders.length
      alert('订单提交成功')
    },
    setActiveTab (tab) { this.activeTab = tab },
    setOrderStatus (status) { this.orderStatus = status },
    switchToRealAccount () { alert('切换到实盘账户') },
    showCalculator () { alert('打开计算器') },
    showQuote () { alert('查看报价') },
    modifyOrder () { alert('修改订单') },
    cancelOrder () { alert('撤销订单') }
  }
}
</script>

<style scoped>
.futu-trade {
  background: #f8f8f8;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  background: #000;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}

.status-icons {
  display: flex;
  gap: 5px;
  font-size: 14px;
}

.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
}

.nav-left, .nav-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.nav-left i, .nav-right i {
  font-size: 20px;
  color: #333;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.account-info {
  background: #fff;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5e5;
}

.account-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.account-type {
  font-size: 16px;
  color: #333;
}

.profit-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.profit-label {
  font-size: 14px;
  color: #666;
}

.profit-value {
  font-size: 14px;
  color: #333;
}

.view-detail {
  font-size: 14px;
  color: #00D4AA;
  cursor: pointer;
}

.asset-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.asset-label {
  font-size: 14px;
  color: #666;
}

.asset-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.stock-info {
  background: #fff;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5e5;
}

.stock-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.stock-symbol {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stock-name {
  font-size: 14px;
  color: #666;
}

.stock-right {
  text-align: right;
}

.stock-price {
  font-size: 20px;
  font-weight: 600;
  color: #00D4AA;
  margin-bottom: 4px;
}

.stock-change {
  font-size: 14px;
}

.stock-change.negative {
  color: #ff4444;
}

.stock-change.positive {
  color: #00D4AA;
}

.stock-chart {
  margin-left: 16px;
}

.bid-ask {
  display: flex;
  justify-content: space-between;
  background: #f5f5f5;
  padding: 12px 16px;
  border-radius: 8px;
}

.bid, .ask {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bid .label, .ask .label {
  font-size: 14px;
  color: #666;
}

.bid .price, .ask .price {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.bid .volume, .ask .volume {
  font-size: 14px;
  color: #666;
}

.trade-direction {
  background: #fff;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  gap: 16px;
}

.direction-label {
  font-size: 16px;
  color: #333;
  min-width: 40px;
}

.direction-buttons {
  display: flex;
  flex: 1;
  border-radius: 25px;
  overflow: hidden;
}

.direction-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  background: #f5f5f5;
  color: #666;
}

.direction-btn.buy-btn.active {
  background: #ff4444;
  color: #fff;
}

.direction-btn.sell-btn.active {
  background: #00D4AA;
  color: #fff;
}

.trade-form {
  background: #fff;
}

.form-row {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.form-label {
  font-size: 16px;
  color: #333;
  min-width: 60px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-icon {
  font-size: 14px;
  color: #999;
}

.form-input {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.order-type-select {
  border: none;
  background: none;
  font-size: 16px;
  color: #333;
  text-align: right;
  appearance: none;
}

.dropdown-icon {
  margin-left: 8px;
  color: #999;
}

.quantity-input {
  gap: 12px;
}

.quantity-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #ddd;
  background: #f8f8f8;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #333;
  cursor: pointer;
}

.quantity-btn:hover {
  background: #e8e8e8;
}

.quantity-field {
  width: 60px;
  text-align: center;
  border: none;
  font-size: 16px;
  color: #333;
}

.amount-value, .validity-value {
  font-size: 16px;
  color: #333;
}

.fund-info {
  padding: 16px 20px;
  background: #f8f8f8;
}

.fund-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.fund-row:last-child {
  margin-bottom: 0;
}

.fund-label {
  font-size: 14px;
  color: #666;
}

.fund-value {
  font-size: 14px;
}

.fund-value.available {
  color: #ff4444;
}

.fund-value.position {
  color: #333;
}

.fund-value.max-buy {
  color: #ff4444;
}

.fund-value.short-sell {
  color: #00D4AA;
}

.trade-button-container {
  padding: 20px;
  background: #fff;
  display: flex;
  align-items: center;
  gap: 12px;
}

.trade-button {
  flex: 1;
  padding: 16px;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  cursor: pointer;
}

.trade-button.buy-button {
  background: #ff4444;
}

.trade-button.sell-button {
  background: #00D4AA;
}

.calculator-icon {
  width: 48px;
  height: 48px;
  border: 1px solid #ddd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.calc-icon {
  font-size: 20px;
}

.bottom-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
}

.tab-buttons {
  display: flex;
  gap: 24px;
}

.tab-btn {
  background: none;
  border: none;
  font-size: 16px;
  color: #666;
  cursor: pointer;
  padding-bottom: 4px;
}

.tab-btn.active {
  color: #333;
  border-bottom: 2px solid #333;
}

.real-account-btn {
  background: none;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.order-status-tabs {
  display: flex;
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
}

.status-tab {
  flex: 1;
  padding: 12px;
  background: none;
  border: none;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.status-tab.active {
  color: #333;
  border-bottom-color: #333;
}

.order-list {
  background: #fff;
}

.order-header {
  display: flex;
  padding: 12px 20px;
  background: #f8f8f8;
  border-bottom: 1px solid #e5e5e5;
}

.header-item {
  flex: 1;
  font-size: 12px;
  color: #666;
  text-align: center;
}

.header-item:first-child {
  text-align: left;
}

.header-item:last-child {
  text-align: right;
}

.order-item {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.order-item > div {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.order-status {
  align-items: flex-start;
}

.status-text {
  font-size: 14px;
  margin-bottom: 4px;
}

.status-text.buy-status {
  color: #ff4444;
}

.status-text.sell-status {
  color: #00D4AA;
}

.status-icon {
  font-size: 12px;
  color: #ffa500;
}

.order-stock {
  align-items: center;
}

.stock-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.stock-code {
  font-size: 12px;
  color: #666;
}

.order-details {
  align-items: center;
}

.order-quantity {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.order-price {
  font-size: 12px;
  color: #666;
}

.order-time {
  align-items: flex-end;
  font-size: 12px;
  color: #666;
  text-align: right;
  white-space: pre-line;
}

.bottom-actions {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background: #fff;
  border-top: 1px solid #e5e5e5;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  cursor: pointer;
}

.action-btn .icon {
  font-size: 24px;
}

.action-btn span {
  font-size: 12px;
  color: #666;
}
</style>
