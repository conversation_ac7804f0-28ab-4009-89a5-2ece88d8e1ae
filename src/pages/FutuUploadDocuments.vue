<template>
  <div class="futu-upload-documents">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left">
        <div class="back-icon" @click="goBack">‹</div>
        <span class="title">网上转账开户</span>
      </div>
      <div class="header-right">
        <div class="help-icon">🎧</div>
        <div class="more-icon">⋯</div>
        <div class="message-icon">
          <div class="message-icon-inner">✉️</div>
          <span class="badge">3</span>
        </div>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar">
      <div class="progress-fill" :style="{ width: '45%' }"></div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <h1 class="page-title">上传身份证件</h1>

      <!-- 身份信息表单 -->
      <div class="identity-form">
        <h2 class="form-title">身份信息</h2>

        <!-- 国家/地区选择 -->
        <div class="form-field">
          <label class="field-label">国家/地区</label>
          <div class="select-field" @click="showCountryPicker = true">
            <span class="select-value">中国香港</span>
            <span class="select-arrow">›</span>
          </div>
        </div>

        <!-- 永久居民确认 -->
        <div class="checkbox-field">
          <div class="checkbox" :class="{ checked: isPermanentResident }" @click="isPermanentResident = !isPermanentResident">
            <span v-if="isPermanentResident" class="checkmark">✓</span>
          </div>
          <span class="checkbox-label">本人是所选国家/地区的永久居民。</span>
        </div>

        <!-- 身份证件类型选择 -->
        <div class="form-field">
          <label class="field-label">身份证件类型</label>
          <div class="select-field" @click="showDocumentPicker = true">
            <span class="select-value">香港永久性居民身份证</span>
            <span class="select-arrow">›</span>
          </div>
        </div>

        <!-- 提示文字 -->
        <div class="form-note">
          您可选择任意的证件组合进行开户，对能否开户无影响
        </div>
      </div>

      <!-- 上传身份证件 -->
      <div class="upload-section">
        <h2 class="upload-title">上传香港永久性居民身份证</h2>
        <div class="upload-instruction">
          拍摄时请将证件平放在<span class="highlight-text">浅色背景下</span>，手机横向拍摄。
        </div>
        <div class="upload-warning">
          以下情况会导致审核驳回，请注意拍摄
        </div>

        <!-- 错误示例 -->
        <div class="error-examples">
          <div class="example-row">
            <div class="example-item">
              <img src="@/assets/img/id1.jpg" alt="正面窗口号码模糊" class="example-image" />
              <div class="example-label error">✗ 正面窗口号码模糊</div>
            </div>
            <div class="example-item">
              <img src="@/assets/img/id2.jpg" alt="背面窗口号码模糊" class="example-image" />
              <div class="example-label error">✗ 背面窗口号码模糊</div>
            </div>
          </div>
          <div class="example-row">
            <div class="example-item">
              <img src="@/assets/img/id3.jpg" alt="反光强烈" class="example-image" />
              <div class="example-label error">✗ 反光强烈</div>
            </div>
            <div class="example-item">
              <img src="@/assets/img/id4.jpg" alt="边角缺失" class="example-image" />
              <div class="example-label error">✗ 边角缺失</div>
            </div>
          </div>
        </div>

        <!-- 上传区域 -->
        <div class="upload-areas">
          <!-- 上传证件人像面照片 -->
          <div class="upload-area" @click="uploadPhoto('front')" v-if="!uploadedPhotos.front">
            <div class="upload-icon">📤</div>
            <div class="upload-text">上传证件人像面照片</div>
          </div>

          <!-- 已上传的人像面照片 -->
          <div class="uploaded-photo" v-if="uploadedPhotos.front">
            <img :src="uploadedPhotos.front" alt="身份证人像面" class="uploaded-image" />
            <div class="photo-actions">
              <button class="delete-btn" @click="deletePhoto('front')">🗑️</button>
            </div>
            <div class="photo-label">请确认证件背面窗口号码清晰可见（旧版本证件请忽略此提示）</div>
          </div>

          <!-- 上传证件反面照片 -->
          <div class="upload-area" @click="uploadPhoto('back')" v-if="!uploadedPhotos.back">
            <div class="upload-icon">📤</div>
            <div class="upload-text">上传证件反面照片</div>
          </div>

          <!-- 已上传的反面照片 -->
          <div class="uploaded-photo" v-if="uploadedPhotos.back">
            <img :src="uploadedPhotos.back" alt="身份证反面" class="uploaded-image" />
            <div class="photo-actions">
              <button class="delete-btn" @click="deletePhoto('back')">🗑️</button>
            </div>
          </div>
        </div>

        <!-- 证件信息表单 - 只有上传照片后才显示 -->
        <div v-if="uploadedPhotos.front" class="document-form">
          <h3 class="form-section-title">证件信息</h3>

          <!-- 证件姓名 -->
          <div class="form-group">
            <label class="form-label">证件姓名</label>
            <input
              type="text"
              v-model="documentInfo.name"
              placeholder="请输入中文姓名"
              class="form-input"
              :class="{ 'error': nameError }"
              @blur="validateName"
              @input="clearNameError"
            />
            <div v-if="nameError" class="error-message">Please enter your name in Chinese</div>
          </div>

          <!-- 身份证号码 -->
          <div class="form-group">
            <label class="form-label">身份证号码</label>
            <input
              type="text"
              v-model="documentInfo.idNumber"
              placeholder="如：A123456(7) 或 AB123456(7)"
              class="form-input"
              :class="{ 'error': idNumberError }"
              @blur="validateIdNumber"
              @input="clearIdNumberError"
              maxlength="10"
            />
            <div v-if="idNumberError" class="error-message">Please enter correct Hong Kong ID format</div>
            <div class="input-hint">支持格式：A123456(7)、AB123456(7) 或 A1234567</div>
          </div>

                    <!-- 出生日期 -->
          <div class="form-group">
            <label class="form-label">出生日期</label>
            <el-date-picker
              v-model="documentInfo.birthDate"
              type="date"
              placeholder="请选择出生日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :picker-options="datePickerOptions"
              class="form-date-picker"
            />
          </div>

          <!-- 性别 -->
          <div class="form-group">
            <label class="form-label">性别</label>
            <div class="select-container">
              <select v-model="documentInfo.gender" class="form-select">
                <option value="男">男</option>
                <option value="女">女</option>
              </select>
              <div class="select-arrow">›</div>
            </div>
          </div>

          <!-- 出生地 -->
          <div class="form-group">
            <label class="form-label">出生地</label>
            <div class="select-container">
              <select v-model="documentInfo.birthPlace" class="form-select">
                <option value="中国香港">中国香港</option>
                <option value="中国澳门">中国澳门</option>
                <option value="中国台湾">中国台湾</option>
                <option value="其他">其他</option>
              </select>
              <div class="select-arrow">›</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-buttons">
      <button class="prev-btn" @click="goBack">
        上一步
      </button>
      <button class="next-btn" @click="nextStep">
        下一步
      </button>
    </div>

    <!-- 国家选择弹窗 -->
    <div v-if="showCountryPicker" class="modal-overlay" @click="showCountryPicker = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>选择国家/地区</h3>
          <button class="close-btn" @click="showCountryPicker = false">✕</button>
        </div>
        <div class="country-list">
          <div class="country-item" @click="selectCountry('中国香港')">中国香港</div>
          <div class="country-item" @click="selectCountry('中国澳门')">中国澳门</div>
          <div class="country-item" @click="selectCountry('其他')">其他</div>
        </div>
      </div>
    </div>

    <!-- 证件类型选择弹窗 -->
    <div v-if="showDocumentPicker" class="modal-overlay" @click="showDocumentPicker = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>选择证件类型</h3>
          <button class="close-btn" @click="showDocumentPicker = false">✕</button>
        </div>
        <div class="document-list">
          <div class="document-item" @click="selectDocument('香港永久性居民身份证')">香港永久性居民身份证</div>
          <div class="document-item" @click="selectDocument('香港非永久性居民身份证')">香港非永久性居民身份证</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FutuUploadDocuments',
  data () {
    return {
      isPermanentResident: true,
      selectedCountry: '中国香港',
      selectedDocument: '香港永久性居民身份证',
      showCountryPicker: false,
      showDocumentPicker: false,
      showDatePicker: false,
      uploadedPhotos: {
        front: null,
        back: null
      },
      documentInfo: {
        name: '',
        idNumber: '',
        birthDate: '',
        gender: '男',
        birthPlace: '中国香港'
      },
      // 验证错误状态
      nameError: false,
      idNumberError: false,
      // Element UI 日期选择器配置
      datePickerOptions: {
        disabledDate (time) {
          // 禁用未来日期和18岁以下的日期
          const today = new Date()
          const eighteenYearsAgo = new Date()
          eighteenYearsAgo.setFullYear(eighteenYearsAgo.getFullYear() - 18)

          return time.getTime() > today.getTime() || time.getTime() > eighteenYearsAgo.getTime()
        }
      }
    }
  },
  computed: {
    canProceed () {
      return this.uploadedPhotos.front &&
             this.documentInfo.name &&
             this.documentInfo.idNumber &&
             this.documentInfo.birthDate &&
             !this.nameError &&
             !this.idNumberError
    }
  },
  methods: {
    goBack () {
      this.$router.go(-1)
    },
    nextStep () {
      // 这里可以添加表单验证
      if (!this.canProceed) return


      // 跳转到地址证明页面
      this.$router.push('/open-account/address-proof')
    },
    selectCountry (country) {
      this.selectedCountry = country
      this.showCountryPicker = false
    },
    selectDocument (document) {
      this.selectedDocument = document
      this.showDocumentPicker = false
    },
    uploadPhoto (type) {
      // 模拟文件选择
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/*'
      input.onchange = (e) => {
        const file = e.target.files[0]
        if (file) {
          // 创建预览URL
          const reader = new FileReader()
          reader.onload = (event) => {
            this.uploadedPhotos[type] = event.target.result
          }
          reader.readAsDataURL(file)
        }
      }
      input.click()
    },
    deletePhoto (type) {
      this.uploadedPhotos[type] = null
    },
    // 验证姓名（只能是中文）
    validateName () {
      const chineseNameRegex = /^[\u4e00-\u9fa5]{2,10}$/
      this.nameError = !chineseNameRegex.test(this.documentInfo.name)
    },
    clearNameError () {
      this.nameError = false
    },
    // 验证身份证号码（香港身份证格式）
    validateIdNumber () {
      // 香港身份证格式：
      // 1. 新格式：A123456(7) - 1个字母 + 6个数字 + 括号中的校验位
      // 2. 旧格式：AB123456(7) - 2个字母 + 6个数字 + 括号中的校验位
      // 3. 简化格式：A1234567 - 1个字母 + 7个数字（最后一位是校验位）
      const hkIdRegex = /^[A-Z]{1,2}\d{6}[\(\d\)|\d]$/

      const idNumber = this.documentInfo.idNumber.trim().toUpperCase()

      if (!hkIdRegex.test(idNumber)) {
        this.idNumberError = true
        return
      }

      this.idNumberError = false
    },
    clearIdNumberError () {
      this.idNumberError = false
    }
  }
}
</script>

<style scoped>
.futu-upload-documents {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: white;
  border-bottom: 1px solid #eee;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #333;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.help-icon, .more-icon, .message-icon-inner {
  font-size: 18px;
  cursor: pointer;
  color: #333;
}

.message-icon {
  position: relative;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-bar {
  height: 4px;
  background: #f0f0f0;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: #333;
  transition: width 0.3s ease;
}

.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0 0 30px 0;
}

/* 身份信息表单 */
.identity-form {
  margin-bottom: 40px;
}

.form-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px 0;
}

.form-field {
  margin-bottom: 20px;
}

.field-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.select-field {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f8f8;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s;
}

.select-field:hover {
  background: #f0f0f0;
}

.select-value {
  font-size: 16px;
  color: #333;
}

.select-arrow {
  font-size: 18px;
  color: #999;
}

.checkbox-field {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 4px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.checkbox.checked {
  background: #333;
  border-color: #333;
}

.checkmark {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-label {
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.form-note {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
}

/* 上传区域 */
.upload-section {
  margin-bottom: 40px;
}

.upload-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.upload-instruction {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.highlight-text {
  color: #ff8c00;
}

.upload-warning {
  font-size: 14px;
  color: #333;
  margin-bottom: 20px;
  font-weight: 600;
}

.error-examples {
  margin-bottom: 30px;
}

.example-row {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.example-item {
  flex: 1;
  text-align: center;
}

.example-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 8px;
}

.example-label {
  font-size: 12px;
  font-weight: 500;
}

.example-label.error {
  color: #ff4444;
}

.upload-areas {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upload-area {
  border: 2px dashed #ddd;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #333;
  background: #f8f8f8;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.upload-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 底部按钮 */
.bottom-buttons {
  display: flex;
  gap: 12px;
  padding: 20px;
  background: white;
  border-top: 1px solid #eee;
}

.prev-btn {
  flex: 1;
  height: 50px;
  background: white;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.prev-btn:hover {
  background: #f8f8f8;
}

.next-btn {
  flex: 1;
  height: 50px;
  background: #333;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
}

.next-btn:hover {
  background: #555;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 0;
  width: 80%;
  max-width: 400px;
  max-height: 70vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.country-list, .document-list {
  max-height: 300px;
  overflow-y: auto;
}

.country-item, .document-item {
  padding: 16px 20px;
  cursor: pointer;
  transition: background 0.3s;
  border-bottom: 1px solid #f0f0f0;
}

.country-item:hover, .document-item:hover {
  background: #f8f8f8;
}

.country-item:last-child, .document-item:last-child {
  border-bottom: none;
}

/* 已上传照片样式 */
.uploaded-photo {
  position: relative;
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
  background: #f8f8f8;
}

.uploaded-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.photo-actions {
  position: absolute;
  top: 12px;
  right: 12px;
}

.delete-btn {
  width: 36px;
  height: 36px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-label {
  padding: 12px 16px;
  font-size: 12px;
  color: #ff8c00;
  background: #fff;
  text-align: center;
}

/* 证件信息表单样式 */
.document-form {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.form-section-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 24px 0;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 50px;
  padding: 0 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  color: #333;
  background: #fff;
  box-sizing: border-box;
  transition: border-color 0.3s;
}

.form-input:focus {
  outline: none;
  border-color: #333;
}

.form-input.error {
  border-color: #ff4444;
}

.form-input::placeholder {
  color: #999;
}

.error-message {
  margin-top: 6px;
  font-size: 12px;
  color: #ff4444;
}

.date-input-container {
  position: relative;
}

.date-input {
  padding-right: 50px;
}

.calendar-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #999;
  pointer-events: none;
}

.select-container {
  position: relative;
}

.form-select {
  width: 100%;
  height: 50px;
  padding: 0 40px 0 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  color: #333;
  background: #fff;
  box-sizing: border-box;
  appearance: none;
  cursor: pointer;
}

.form-select:focus {
  outline: none;
  border-color: #333;
}

.select-arrow {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #999;
  pointer-events: none;
}

/* Element UI 日期选择器样式 */
.form-date-picker {
  width: 100%;
}

.form-date-picker .el-input__inner {
  height: 50px;
  line-height: 50px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  color: #333;
  padding: 0 16px;
  transition: border-color 0.3s;
}

.form-date-picker .el-input__inner:focus {
  border-color: #333;
  box-shadow: none;
}

.form-date-picker .el-input__inner::placeholder {
  color: #999;
  font-size: 16px;
}

.form-date-picker .el-input__suffix {
  right: 12px;
}

.form-date-picker .el-input__suffix .el-input__suffix-inner {
  color: #999;
}

/* 输入提示样式 */
.input-hint {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}
</style>
