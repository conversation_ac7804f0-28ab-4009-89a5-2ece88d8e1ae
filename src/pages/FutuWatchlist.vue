<template>
  <div class="futu-watchlist">
    <!-- 顶部标题栏 -->
    <div class="watchlist-header">
      <div class="header-left">
        <div class="logo-icon">
          <img src="/static/img/favicon-futunn.ico" alt="Futu Logo" />
        </div>
        <span class="title">Watchlists</span>
      </div>
      <div class="header-right">
        <div class="ai-icon" @click="showAI">
          <div class="ai-avatar">AI</div>
        </div>
        <van-icon name="search" class="search-icon" @click="goToSearch" />
        <van-icon name="envelop-o" class="message-icon" @click="showMessages" />
      </div>
    </div>

    <!-- 市场标签栏 -->
    <div class="market-tabs">
      <div
        class="tab-item"
        :class="{ active: activeTab === 'all' }"
        @click="switchTab('all')"
      >
        All
      </div>
      <div
        class="tab-item"
        :class="{ active: activeTab === 'us' }"
        @click="switchTab('us')"
      >
        US Stocks
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="view-controls">
        <div class="view-item" :class="{ active: viewMode === 'list' }" @click="setViewMode('list')">
          <van-icon name="list-switch" />
        </div>
        <div class="view-item" :class="{ active: viewMode === 'chart' }" @click="setViewMode('chart')">
          <van-icon name="bar-chart-o" />
        </div>
        <div class="view-item" :class="{ active: viewMode === 'detail' }" @click="setViewMode('detail')">
          <van-icon name="apps-o" />
        </div>
        <div class="view-item" :class="{ active: viewMode === 'settings' }" @click="setViewMode('settings')">
          <van-icon name="setting-o" />
        </div>
      </div>
      <div class="sort-controls">
        <span class="sort-label">Price</span>
        <van-icon name="arrow-down" class="sort-icon" @click="toggleSort" />
        <span class="sort-label">Change</span>
        <van-icon name="arrow-down" class="sort-icon" @click="toggleSort" />
      </div>
    </div>

    <!-- 自选股列表 -->
    <div class="watchlist-content">
      <!-- 加载状态 -->
      <div class="loading-state" v-if="loading">
        <van-loading type="spinner" color="#FF6B35" />
        <div class="loading-text">Loading...</div>
      </div>
      <!-- 股票列表 -->
      <div class="stock-list" v-else-if="filteredWatchlistData.length > 0">
        <div
          class="stock-item"
          v-for="stock in filteredWatchlistData"
          :key="stock.code"
          @click="goToStockDetail(stock)"
        >
          <div class="stock-info">
            <div class="stock-name">{{ stock.name }}</div>
            <div class="stock-code">{{ stock.code }}</div>
          </div>
          <div class="stock-chart">
            <div class="mini-chart">
              <svg viewBox="0 0 60 20" class="chart-svg">
                <polyline
                  :points="stock.chartPoints"
                  fill="none"
                  :stroke="stock.changeType === 'up' ? '#00c853' : (stock.changeType === 'down' ? '#ff1744' : '#999')"
                  stroke-width="1"
                />
              </svg>
            </div>
          </div>
          <div class="stock-price">
            <div class="current-price">{{ stock.price }}</div>
            <div class="price-info">
              <span class="previous-price">{{ stock.previousPrice }}</span>
              <span class="change-percent" :class="stock.changeType">{{ stock.changePercent }}%</span>
            </div>
          </div>
          <div class="price-change-badge" :class="stock.changeType">
            {{ stock.changePercent }}%
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div class="error-state" v-else-if="error">
        <div class="error-icon">⚠️</div>
        <div class="error-text">{{ error }}</div>
        <van-button
          class="retry-button"
          type="primary"
          @click="loadWatchlist"
          :style="{ backgroundColor: '#FF6B35' }"
        >
          Retry
        </van-button>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <div class="empty-icon">📈</div>
        <div class="empty-text">No stocks in watchlist</div>
        <div class="empty-desc">Add stocks you're interested in to track their prices</div>
        <van-button
          class="add-button"
          type="primary"
          @click="goToMarket"
          :style="{ backgroundColor: '#FF6B35' }"
        >
          Add Stocks
        </van-button>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions" v-if="filteredWatchlistData.length > 0 && !isEditMode">
      <div class="actions-container">
        <div class="action-item" @click="addToWatchlist">
          <van-icon name="like-o" />
          <span>Add Symbol</span>
        </div>
        <div class="action-divider"></div>
        <div class="action-item" @click="editWatchlist">
          <van-icon name="edit" />
          <span>Edit List</span>
        </div>
      </div>
    </div>

    <!-- 编辑模式页面 -->
    <div class="edit-mode-overlay" v-if="isEditMode">
      <div class="edit-header">
        <div class="edit-nav">
          <van-icon name="arrow-left" @click="cancelEdit" />
          <span class="edit-title">全部</span>
          <span class="edit-done" @click="finishEdit">完成</span>
        </div>
        <div class="edit-search">
          <van-icon name="search" />
          <input type="text" placeholder="搜索" class="edit-search-input" />
        </div>
      </div>

      <div class="edit-content">
        <div class="edit-toolbar">
          <div class="edit-sort">
            <span class="sort-label">代码/名称</span>
            <span class="sort-action">移到最前</span>
            <span class="sort-action">排序</span>
          </div>
        </div>

        <div class="edit-stock-list">
          <div
            class="edit-stock-item"
            v-for="stock in filteredWatchlistData"
            :key="stock.code"
          >
                         <div class="stock-checkbox">
               <input
                 type="checkbox"
                 :id="'stock-' + stock.code"
                 :value="stock.code"
                 :checked="selectedStocks.includes(stock.code)"
                 @change="toggleStockSelection(stock.code)"
               />
               <label :for="'stock-' + stock.code" class="checkbox-label">
                 <div class="checkbox-custom"></div>
               </label>
             </div>
            <div class="stock-info">
              <div class="stock-name">{{ stock.name }}</div>
              <div class="stock-code">{{ stock.code }}</div>
            </div>
            <div class="stock-actions">
              <van-icon name="arrow-up" class="move-icon" />
              <van-icon name="bars" class="drag-icon" />
            </div>
          </div>
        </div>
      </div>

      <div class="edit-bottom-actions">
                 <div class="edit-action-item">
           <input
             type="checkbox"
             id="select-all"
             :checked="isAllSelected"
             @change="toggleSelectAll"
           />
           <label for="select-all" class="checkbox-label">
             <div class="checkbox-custom"></div>
           </label>
           <span>Select All</span>
         </div>
        <div class="edit-action-buttons">
          <span class="delete-btn" @click="confirmDelete" v-if="selectedStocks.length > 0">
            Delete({{ selectedStocks.length }})
          </span>
          <span class="add-btn" @click="addToGroup" v-if="selectedStocks.length > 0">
            Add to Group({{ selectedStocks.length }})
          </span>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <van-dialog
      v-model="showDeleteDialog"
      :title="`Confirm deletion of ${selectedStocks.length} stocks?`"
      :show-cancel-button="true"
      cancel-button-text="Cancel"
      confirm-button-text="Confirm"
      @confirm="deleteSelected"
      @cancel="showDeleteDialog = false"
    />

    <!-- 财务数据测试区域 -->
    <div class="financial-test-panel" v-if="financialTestData">
      <div class="test-header">
        <h4>财务数据接口测试结果</h4>
        <span class="close-btn" @click="financialTestData = null">×</span>
      </div>
      <div class="test-content">
        <pre>{{ JSON.stringify(financialTestData, null, 2) }}</pre>
      </div>
    </div>

    <!-- 底部导航 -->
    <FutuTabbar />
  </div>
</template>

<script>
import FutuTabbar from '@/components/FutuTabbar.vue'
import {
  getMyList,
  delOption,
  getStockFinancials,
  getDividends
} from '@/axios/api'

export default {
  name: 'FutuWatchlist',
  components: {
    FutuTabbar
  },
  data () {
    return {
      watchlistData: [
        {
          name: '英伟达',
          code: 'NVDA',
          price: '164.920',
          previousPrice: '164.900',
          change: '+0.020',
          changePercent: '+0.50',
          changeType: 'up',
          market: 'US',
          chartPoints: '0,15 15,12 30,8 45,6 60,3'
        },
        {
          name: '苹果',
          code: 'AAPL',
          price: '211.160',
          previousPrice: '210.530',
          change: '+0.630',
          changePercent: '-0.59',
          changeType: 'down',
          market: 'US',
          chartPoints: '0,10 15,8 30,12 45,6 60,9'
        },
        {
          name: '特斯拉',
          code: 'TSLA',
          price: '313.510',
          previousPrice: '312.750',
          change: '+0.760',
          changePercent: '+1.17',
          changeType: 'up',
          market: 'US',
          chartPoints: '0,18 15,14 30,10 45,7 60,4'
        },
        {
          name: '富途控股',
          code: 'FUTU',
          price: '149.990',
          previousPrice: '149.970',
          change: '+0.020',
          changePercent: '+4.53',
          changeType: 'up',
          market: 'US',
          chartPoints: '0,12 15,8 30,6 45,4 60,2'
        },
        {
          name: '标普500ETF-SPDR',
          code: 'SPY',
          price: '623.620',
          previousPrice: '623.040',
          change: '+0.580',
          changePercent: '-0.35',
          changeType: 'down',
          market: 'US',
          chartPoints: '0,8 15,10 30,12 45,8 60,6'
        },
        {
          name: '腾讯控股',
          code: '00700',
          price: '496.600',
          previousPrice: '496.600',
          change: '0.000',
          changePercent: '+0.00',
          changeType: 'neutral',
          market: 'HK',
          chartPoints: '0,10 15,10 30,10 45,10 60,10'
        },
        {
          name: '恒生指数',
          code: '800000',
          price: '24139.57',
          previousPrice: '24028.37',
          change: '+111.20',
          changePercent: '+0.46',
          changeType: 'up',
          market: 'HK',
          chartPoints: '0,15 15,12 30,8 45,6 60,3'
        }
      ],
      loading: false,
      error: null,
      activeTab: 'all',
      viewMode: 'list',
      sortBy: 'name',
      sortOrder: 'asc',
      isEditMode: false,
      selectedStocks: [],
      showDeleteDialog: false,
      financialTestData: null,
      dividendsTestData: null
    }
  },

  computed: {
    filteredWatchlistData () {
      if (this.activeTab === 'all') {
        return this.watchlistData
      } else if (this.activeTab === 'us') {
        return this.watchlistData.filter(stock => stock.market === 'US')
      }
      return this.watchlistData
    },

    isAllSelected () {
      return this.selectedStocks.length === this.filteredWatchlistData.length && this.filteredWatchlistData.length > 0
    }
  },

  mounted () {
    this.loadWatchlist()
    this.testStockFinancials()
  },

  methods: {
    async loadWatchlist () {
      try {
        this.loading = true
        this.error = null

        // 调用自选股列表接口
        // let opt = {
        //   keyWords: '',
        //   pageNum: 1,
        //   pageSize: 15
        // };
        const response = await getMyList()
        if (response && response.code === 200 && response.data && response.data.list) {
          this.watchlistData = this.adaptWatchlistData(response.data.list)
        } else {
          // API调用成功但没有数据，使用默认数据
        }
      } catch (error) {
        this.error = error.message || 'Failed to load watchlist data'
      } finally {
        this.loading = false
      }
    },

    // 适配自选股数据格式
    adaptWatchlistData (data) {

      if (Array.isArray(data)) {
        return data.map(item => {
          // 计算涨跌幅和涨跌额
          const currentPrice = parseFloat(item.nowPrice) || 0
          const changeRate = parseFloat(item.hcrate) || 0
          const changeAmount = currentPrice * (changeRate / 100)

          return {
            name: item.stockName || item.name,
            code: item.stockCode || item.code,
            price: currentPrice > 0 ? currentPrice.toFixed(3) : '--',
            previousPrice: (currentPrice - changeAmount).toFixed(3),
            change: changeAmount !== 0 ? (changeAmount > 0 ? '+' : '') + changeAmount.toFixed(3) : '--',
            changePercent: changeRate !== 0 ? (changeRate > 0 ? '+' : '') + changeRate.toFixed(2) : '--',
            changeType: changeRate > 0 ? 'up' : (changeRate < 0 ? 'down' : 'neutral'),
            market: this.getMarketByCode(item.stockCode || item.code),
            chartPoints: this.generateChartPoints(changeRate),
            stockGid: item.stockGid,
            stock_type: item.stock_type,
            stock_plate: item.stock_plate,
            addTime: item.addTime || item.createTime
          }
        })
      }

      return []
    },

    // 生成图表点位
    generateChartPoints (changeRate) {
      const trend = changeRate > 0 ? 'up' : (changeRate < 0 ? 'down' : 'flat')

      if (trend === 'up') {
        return '0,15 15,12 30,8 45,6 60,3'
      } else if (trend === 'down') {
        return '0,5 15,8 30,12 45,14 60,17'
      } else {
        return '0,10 15,10 30,10 45,10 60,10'
      }
    },

    // 根据股票代码判断市场
    getMarketByCode (code) {
      if (!code) return 'SH'

      if (code.startsWith('00') || code.startsWith('09')) return 'HK' // 港股
      if (code.match(/^[A-Z]+$/)) return 'US' // 美股
      if (code.startsWith('60')) return 'SH' // 沪市
      if (code.startsWith('00') || code.startsWith('30')) return 'SZ' // 深市

      return 'SH'
    },

    // 切换标签
    switchTab (tab) {
      this.activeTab = tab
    },

    // 设置视图模式
    setViewMode (mode) {
      this.viewMode = mode
      this.$toast(`Switched to ${mode === 'list' ? 'List' : mode === 'chart' ? 'Chart' : mode === 'detail' ? 'Detail' : 'Settings'} view`)
    },

    // 切换排序
    toggleSort () {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc'
      this.$toast('Sort function')
    },

    // 显示AI
    showAI () {
      this.$toast('AI feature')
    },

    // 显示消息
    showMessages () {
      this.$toast('Messages feature')
    },

    goToSearch () {
      this.$router.push('/search')
    },

    goToStockDetail (stock) {
      if (this.isEditMode) return // 编辑模式下不跳转


      this.$router.push({
        path: '/stock-detail',
        query: {
          symbol: stock.code,
          name: stock.name,
          price: stock.price,
          exchange: stock.market,
          type: stock.type,
          currency: stock.market === 'US' ? 'USD' : stock.market === 'HK' ? 'HKD' : 'CNY'
        }
      })
    },

    // 添加自选股
    addToWatchlist () {
      this.$router.push('/search')
    },

    // 编辑自选股
    editWatchlist () {
      this.isEditMode = true
      this.selectedStocks = []
    },

    // 取消编辑
    cancelEdit () {
      this.isEditMode = false
      this.selectedStocks = []
    },

    // 完成编辑
    finishEdit () {
      this.isEditMode = false
      this.selectedStocks = []
    },

    // 切换股票选择状态
    toggleStockSelection (stockCode) {
      const index = this.selectedStocks.indexOf(stockCode)
      if (index > -1) {
        // 如果已选中，则移除
        this.selectedStocks.splice(index, 1)
      } else {
        // 如果未选中，则添加
        this.selectedStocks.push(stockCode)
      }
    },

    // 全选/取消全选
    toggleSelectAll () {
      if (this.isAllSelected) {
        this.selectedStocks = []
      } else {
        this.selectedStocks = this.filteredWatchlistData.map(stock => stock.code)
      }
    },

    // 确认删除
    confirmDelete () {
      if (this.selectedStocks.length === 0) {
        this.$toast('Please select stocks to delete')
        return
      }
      this.showDeleteDialog = true
    },

    // 删除选中的股票
    async deleteSelected () {
      try {

        for (const stockCode of this.selectedStocks) {
          const stock = this.watchlistData.find(s => s.code === stockCode)
          if (stock) {
            await delOption({
              code: stock.code,
              name: stock.name
            })
          }
        }

        // 从本地数据中移除
        this.watchlistData = this.watchlistData.filter(stock => !this.selectedStocks.includes(stock.code))

        this.$toast(`Deleted ${this.selectedStocks.length} stocks from watchlist`)
        this.selectedStocks = []
        this.showDeleteDialog = false
        this.isEditMode = false
      } catch (error) {
        this.$toast('Delete failed, please try again')
      }
    },

    // 添加到分组
    addToGroup () {
      this.$toast('Add to group feature')
    },

    async removeFromWatchlist (stock) {
      try {

        // 调用删除自选股接口
        const response = await delOption({
          code: stock.code,
          name: stock.name
        })


        // 根据其他组件的使用方式，API返回格式应该是 { code: 200 } 表示成功
        if (response && response.code === 200) {
          this.$toast(`Removed ${stock.name} from watchlist`)
          // 从本地列表中移除
          const index = this.watchlistData.findIndex(item => item.code === stock.code)
          if (index > -1) {
            this.watchlistData.splice(index, 1)
          }
        } else {
          const errorMsg = (response && response.msg) || '删除失败，请重试'
          this.$toast(errorMsg)
        }
      } catch (error) {
        this.$toast('Network error, please try again')
      }
    },

    goToMarket () {
      this.$router.push('/market')
    },

    // 测试财务数据接口
    async testStockFinancials () {
      try {
        console.log('=== 开始调用 getStockFinancials 接口 ===')

        // 测试苹果公司的财务数据
        const response = await getStockFinancials({
          ticker: 'AAPL'
        })

        const response4 = await getDividends({
          ticker: 'AAPL'
        })
        console.log('=== getDividends 接口返回数据 ===')
        console.log('完整响应:', response4)
        console.log('响应数据结构:', JSON.stringify(response4, null, 2))

        console.log('=== getStockFinancials 接口返回数据 ===')
        console.log('完整响应:', response)
        console.log('响应数据结构:', JSON.stringify(response, null, 2))

                if (response && response.data) {
          console.log('财务数据:', response.data)
          // 将数据显示在页面上
          this.financialTestData = {
            ticker: 'AAPL',
            response: response
          }
        }

        // 再测试一个英伟达的财务数据
        const response2 = await getStockFinancials({
          ticker: 'NVDA'
        })

        console.log('=== NVDA 财务数据 ===')
        console.log('NVDA 完整响应:', response2)

        if (response2 && response2.data) {
          // 如果有NVDA数据，更新显示
          this.financialTestData = {
            AAPL: response,
            NVDA: response2
          }
        }

      } catch (error) {
        console.error('=== getStockFinancials 接口调用失败 ===')
        console.error('错误信息:', error)
        console.error('错误详情:', error.message)

        if (error.response) {
          console.error('HTTP 响应状态:', error.response.status)
          console.error('HTTP 响应数据:', error.response.data)
        }
      }
    }
  }
}
</script>

<style scoped>
.futu-watchlist {
  min-height: 100vh;
  background: #F5F5F5;
  padding-bottom: 80px;
}

/* 顶部标题栏 */
.watchlist-header {
  background: #fff;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #F0F0F0;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.ai-icon {
  position: relative;
}

.ai-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.search-icon, .message-icon {
  font-size: 20px;
  color: #666;
}

/* 市场标签栏 */
.market-tabs {
  background: #fff;
  padding: 12px 16px;
  display: flex;
  gap: 32px;
  border-bottom: 1px solid #F0F0F0;
}

.tab-item {
  font-size: 16px;
  color: #666;
  cursor: pointer;
  padding: 8px 0;
  position: relative;
  transition: color 0.3s;
}

.tab-item.active {
  color: #333;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: #FF6B35;
}

/* 工具栏 */
.toolbar {
  background: #fff;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #F0F0F0;
}

.view-controls {
  display: flex;
  gap: 16px;
}

.view-item {
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.view-item.active {
  background: #FFF3E0;
  color: #FF6B35;
}

.view-item .van-icon {
  font-size: 16px;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-label {
  font-size: 14px;
  color: #666;
}

.sort-icon {
  font-size: 12px;
  color: #999;
  cursor: pointer;
}

/* 自选股列表 */
.watchlist-content {
  background: #fff;
  margin-top: 8px;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 80px 20px;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
  color: #666;
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: 80px 20px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
}

.retry-button {
  width: 120px;
  height: 40px;
  border-radius: 20px;
}

.stock-list {
  padding: 0;
}

.stock-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #F8F9FA;
  cursor: pointer;
  transition: background-color 0.2s;
}

.stock-item:hover {
  background-color: #F8F9FA;
}

.stock-item:last-child {
  border-bottom: none;
}

.stock-info {
  flex: 1;
  margin-right: 12px;
}

.stock-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.stock-code {
  font-size: 12px;
  color: #666;
}

.stock-chart {
  width: 60px;
  height: 20px;
  margin-right: 12px;
}

.mini-chart {
  width: 100%;
  height: 100%;
}

.chart-svg {
  width: 100%;
  height: 100%;
}

.stock-price {
  text-align: right;
  margin-right: 12px;
  min-width: 80px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.previous-price {
  color: #999;
}

.change-percent {
  font-size: 12px;
}

.change-percent.up {
  color: #00c853;
}

.change-percent.down {
  color: #ff1744;
}

.price-change-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  min-width: 60px;
  text-align: center;
}

.price-change-badge.up {
  background: #00c853;
  color: white;
}

.price-change-badge.down {
  background: #ff1744;
  color: white;
}

.price-change-badge.neutral {
  background: #999;
  color: white;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
}

.add-button {
  width: 120px;
  height: 40px;
  border-radius: 20px;
}

/* 底部操作栏 */
.bottom-actions {
  background: #fff;
  padding: 16px;
  margin-top: 8px;
  border-top: 1px solid #F0F0F0;
}

.actions-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #666;
  font-size: 14px;
  padding: 12px 24px;
}

.action-item .van-icon {
  font-size: 16px;
}

.action-divider {
  width: 1px;
  height: 20px;
  background: #E0E0E0;
  margin: 0 8px;
}

/* 编辑模式覆盖层 */
.edit-mode-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #F5F5F5;
  z-index: 1000;
}

.edit-header {
  background: #fff;
  padding: 10px 16px;
}

.edit-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.edit-nav .van-icon {
  font-size: 20px;
  color: #333;
}

.edit-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.edit-done {
  font-size: 16px;
  color: #FF6B35;
  cursor: pointer;
}

.edit-search {
  display: flex;
  align-items: center;
  background: #F8F9FA;
  border-radius: 8px;
  padding: 8px 12px;
}

.edit-search .van-icon {
  font-size: 16px;
  color: #999;
  margin-right: 8px;
}

.edit-search-input {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  font-size: 14px;
}

.edit-content {
  flex: 1;
  background: #fff;
  margin-top: 8px;
}

.edit-toolbar {
  padding: 12px 16px;
  border-bottom: 1px solid #F0F0F0;
}

.edit-sort {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sort-label {
  font-size: 14px;
  color: #333;
}

.sort-action {
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.edit-stock-list {
  padding: 0;
}

.edit-stock-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #F8F9FA;
}

.stock-checkbox {
  margin-right: 12px;
}

.stock-checkbox input[type="checkbox"] {
  display: none;
}

.checkbox-label {
  cursor: pointer;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 4px;
  position: relative;
  background: #fff;
}

.stock-checkbox input[type="checkbox"]:checked + .checkbox-label .checkbox-custom {
  background: #333;
  border-color: #333;
}

.stock-checkbox input[type="checkbox"]:checked + .checkbox-label .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.edit-stock-item .stock-info {
  flex: 1;
}

.stock-actions {
  display: flex;
  gap: 16px;
}

.move-icon, .drag-icon {
  font-size: 16px;
  color: #999;
  cursor: pointer;
}

.edit-bottom-actions {
  position: fixed;
  bottom: 80px;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16px;
  border-top: 1px solid #F0F0F0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.edit-action-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.edit-action-item input[type="checkbox"] {
  display: none;
}

.edit-action-buttons {
  display: flex;
  gap: 16px;
}

.delete-btn, .add-btn {
  font-size: 14px;
  color: #FF6B35;
  cursor: pointer;
}

/* 财务数据测试面板样式 */
.financial-test-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 600px;
  max-height: 70vh;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 9999;
  overflow: hidden;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #FF6B35;
  color: white;
}

.test-header h4 {
  margin: 0;
  font-size: 16px;
}

.close-btn {
  font-size: 24px;
  cursor: pointer;
  font-weight: bold;
}

.test-content {
  padding: 16px;
  max-height: 50vh;
  overflow-y: auto;
}

.test-content pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
