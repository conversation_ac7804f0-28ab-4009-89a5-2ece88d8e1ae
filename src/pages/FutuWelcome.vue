<template>
  <div class="futu-welcome">
    <!-- FUTUBULL Logo -->
    <div class="logo-container">
      <div class="logo">
        <div class="logo-icon">
          <svg viewBox="0 0 120 120" class="logo-svg">
            <!-- FUTUBULL 橙色牛头图标 -->
            <circle cx="60" cy="60" r="60" fill="#FF6600"/>
            <path d="M30 45 Q45 25 60 45 Q75 25 90 45 Q85 35 75 40 Q70 35 60 40 Q50 35 45 40 Q35 35 30 45" fill="white"/>
            <circle cx="45" cy="50" r="3" fill="white"/>
            <circle cx="75" cy="50" r="3" fill="white"/>
            <path d="M35 65 Q60 85 85 65" stroke="white" stroke-width="4" fill="none"/>
            <circle cx="60" cy="75" r="2" fill="white"/>
          </svg>
        </div>
        <h1 class="logo-text">FUTUBULL</h1>
      </div>
    </div>

    <!-- 按钮区域 -->
    <div class="button-container">
      <button class="login-button" @click="goToLogin">
        Log In
      </button>

      <button class="signup-button" @click="goToSignUp">
        Sign Up
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FutuWelcome',
  methods: {
    goToLogin() {
      this.$router.push('/futu-login')
    },

    goToSignUp() {
      this.$router.push('/futu-signup')
    }
  }
}
</script>

<style scoped>
.futu-welcome {
  min-height: 100vh;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Logo 容器 */
.logo-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 100px;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.logo-icon {
  margin-bottom: 20px;
}

.logo-svg {
  width: 120px;
  height: 120px;
}

.logo-text {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin: 0;
  letter-spacing: 1px;
}

/* 按钮容器 */
.button-container {
  width: 100%;
  max-width: 335px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 40px;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  height: 56px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  background: #333;
  cursor: pointer;
  transition: all 0.2s;
}

.login-button:hover {
  background: #000;
}

.login-button:active {
  transform: scale(0.98);
}

/* 注册按钮 */
.signup-button {
  width: 100%;
  height: 56px;
  border: 2px solid #333;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.signup-button:hover {
  background: #333;
  color: #fff;
}

.signup-button:active {
  transform: scale(0.98);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .futu-welcome {
    padding: 30px 16px;
  }

  .logo-svg {
    width: 100px;
    height: 100px;
  }

  .logo-text {
    font-size: 28px;
  }

  .button-container {
    max-width: none;
    margin: 0 -4px 40px -4px;
  }
}

/* 适配较大屏幕 */
@media (min-width: 768px) {
  .button-container {
    max-width: 400px;
  }
}
</style>
