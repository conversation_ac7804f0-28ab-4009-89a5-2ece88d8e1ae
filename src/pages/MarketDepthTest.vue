<template>
  <div class="market-depth-test">
    <div class="test-header">
      <h1>市场深度组件测试</h1>
      <div class="test-controls">
        <label>
          股票代码：
          <select v-model="selectedTicker" @change="updateTicker">
            <option value="AAPL">苹果 (AAPL)</option>
            <option value="MSFT">微软 (MSFT)</option>
            <option value="GOOGL">谷歌 (GOOGL)</option>
            <option value="NVDA">英伟达 (NVDA)</option>
            <option value="TSLA">特斯拉 (TSLA)</option>
          </select>
        </label>
        <button @click="refreshData" :disabled="loading">
          {{ loading ? '刷新中...' : '手动刷新' }}
        </button>
        <button @click="testApiDirectly">测试 getTrades API</button>
      </div>
    </div>

    <div class="test-content">
      <!-- MarketDepth 组件 -->
      <div class="market-depth-container">
        <h2>市场深度组件</h2>
        <MarketDepth :ticker="selectedTicker" ref="marketDepth" />
      </div>

      <!-- API 测试结果 -->
      <div class="api-test-results" v-if="apiTestResult">
        <h3>getTrades API 测试结果</h3>
        <div class="result-summary">
          <p><strong>状态:</strong> {{ apiTestResult.success ? '✅ 成功' : '❌ 失败' }}</p>
          <p><strong>数据条数:</strong> {{ apiTestResult.dataCount }}</p>
          <p><strong>响应时间:</strong> {{ apiTestResult.responseTime }}ms</p>
        </div>
        <div class="result-data">
          <h4>原始数据预览 (前3条):</h4>
          <pre>{{ JSON.stringify(apiTestResult.preview, null, 2) }}</pre>
        </div>
      </div>

      <!-- 调试信息 -->
      <div class="debug-info">
        <h3>调试信息</h3>
        <div class="debug-logs">
          <div v-for="(log, index) in debugLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span :class="['log-level', log.level]">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <button @click="clearLogs">清空日志</button>
      </div>
    </div>
  </div>
</template>

<script>
import MarketDepth from '@/components/MarketDepth.vue'
import { getTrades, getQuotes, getLastTrade, getLastQuote } from '@/axios/api'

export default {
  name: 'MarketDepthTest',
  components: {
    MarketDepth
  },
  data() {
    return {
      selectedTicker: 'NVDA',
      loading: false,
      apiTestResult: null,
      debugLogs: []
    }
  },
  mounted() {
    this.addLog('info', '页面加载完成，开始测试 MarketDepth 组件')
    this.overrideConsole()
  },
  methods: {
    // 更新股票代码
    updateTicker() {
      this.addLog('info', `切换股票代码到: ${this.selectedTicker}`)
      this.apiTestResult = null
    },

    // 手动刷新数据
    async refreshData() {
      this.loading = true
      this.addLog('info', '手动刷新市场深度数据')

      try {
        if (this.$refs.marketDepth) {
          await this.$refs.marketDepth.loadDepthData()
          this.addLog('success', '数据刷新完成')
        }
      } catch (error) {
        this.addLog('error', `数据刷新失败: ${error.message}`)
      } finally {
        this.loading = false
      }
    },

    // 直接测试 getTrades API
    async testApiDirectly() {
      this.addLog('info', `开始测试 getTrades API - ${this.selectedTicker}`)
      const startTime = Date.now()

      try {
        const response = await getTrades({
          ticker: this.selectedTicker,
          order: 'desc',
          limit: 10,
          sort: 'timestamp'
        })

        const responseTime = Date.now() - startTime

        if (response && response.success && response.data) {
          const results = response.data.results || []
          this.apiTestResult = {
            success: true,
            dataCount: results.length,
            responseTime,
            preview: results.slice(0, 3),
            fullData: response.data
          }
          this.addLog('success', `API 测试成功，获取到 ${results.length} 条交易记录`)
        } else {
          this.apiTestResult = {
            success: false,
            dataCount: 0,
            responseTime,
            error: response.msg || '未知错误'
          }
          this.addLog('error', `API 测试失败: ${response.msg || '未知错误'}`)
        }
      } catch (error) {
        const responseTime = Date.now() - startTime
        this.apiTestResult = {
          success: false,
          dataCount: 0,
          responseTime,
          error: error.message
        }
        this.addLog('error', `API 请求异常: ${error.message}`)
      }
    },

    // 添加日志
    addLog(level, message) {
      const time = new Date().toLocaleTimeString()
      this.debugLogs.unshift({
        time,
        level,
        message
      })

      // 限制日志数量
      if (this.debugLogs.length > 50) {
        this.debugLogs = this.debugLogs.slice(0, 50)
      }
    },

    // 清空日志
    clearLogs() {
      this.debugLogs = []
    },

    // 覆盖 console 方法以捕获组件内的日志
    overrideConsole() {
      const originalLog = console.log
      const originalError = console.error

      console.log = (...args) => {
        const message = args.join(' ')
        if (message.includes('[MarketDepth]')) {
          this.addLog('info', message)
        }
        originalLog.apply(console, args)
      }

      console.error = (...args) => {
        const message = args.join(' ')
        if (message.includes('[MarketDepth]')) {
          this.addLog('error', message)
        }
        originalError.apply(console, args)
      }
    }
  }
}
</script>

<style scoped>
.market-depth-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-header {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.test-header h1 {
  margin: 0 0 15px 0;
  color: #333;
}

.test-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.test-controls label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.test-controls select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.test-controls button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.test-controls button:hover:not(:disabled) {
  background: #0056b3;
}

.test-controls button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.test-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.market-depth-container {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.market-depth-container h2 {
  margin: 0;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
  font-size: 16px;
  color: #333;
}

.api-test-results {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
}

.api-test-results h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.result-summary p {
  margin: 8px 0;
  font-size: 14px;
}

.result-data {
  margin-top: 20px;
}

.result-data h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #666;
}

.result-data pre {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.debug-info {
  grid-column: 1 / -1;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
}

.debug-info h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.debug-logs {
  background: #1e1e1e;
  border-radius: 4px;
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  align-items: baseline;
}

.log-time {
  color: #888;
  margin-right: 10px;
  min-width: 80px;
  font-size: 11px;
}

.log-level {
  margin-right: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  min-width: 50px;
  text-align: center;
}

.log-level.info {
  background: #17a2b8;
  color: white;
}

.log-level.success {
  background: #28a745;
  color: white;
}

.log-level.error {
  background: #dc3545;
  color: white;
}

.log-message {
  color: #f8f9fa;
  flex: 1;
}

.debug-info button {
  margin-top: 10px;
  padding: 6px 12px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.debug-info button:hover {
  background: #5a6268;
}

@media (max-width: 768px) {
  .test-content {
    grid-template-columns: 1fr;
  }

  .test-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .test-controls label {
    justify-content: space-between;
  }
}
</style>
