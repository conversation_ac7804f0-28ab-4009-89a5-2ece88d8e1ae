<template>
  <div class="polygon-websocket-test">
    <!-- 顶部标题 -->
    <div class="test-header">
      <van-icon name="arrow-left" @click="$router.back()" />
      <h1>Polygon WebSocket 测试</h1>
      <van-icon name="info-o" @click="showHelp" />
    </div>

    <!-- 连接状态 -->
    <div class="connection-status">
      <div class="status-card">
        <div class="status-indicator" :class="connectionStatus.isConnected ? 'connected' : 'disconnected'">
          <div class="indicator-dot"></div>
          <span>{{ connectionStatus.isConnected ? 'WebSocket已连接' : 'WebSocket未连接' }}</span>
        </div>
        <div class="status-details">
          <div class="detail-item">
            <span class="label">用户ID:</span>
            <span class="value">{{ connectionStatus.userId }}</span>
          </div>
          <div class="detail-item">
            <span class="label">重连次数:</span>
            <span class="value">{{ connectionStatus.reconnectAttempts }}</span>
          </div>
          <div class="detail-item">
            <span class="label">订阅数量:</span>
            <span class="value">{{ connectionStatus.subscriptions.length }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 股票订阅测试 -->
    <div class="subscription-test">
      <div class="test-card">
        <h3>📈 股票实时数据订阅测试</h3>
        <div class="input-group">
          <van-field
            v-model="testSymbol"
            placeholder="输入股票代码，如：AAPL"
            label="股票代码:"
          />
                    <van-button
            type="primary"
            @click="subscribeStock"
            :disabled="!testSymbol || isSubscribing"
            :loading="isSubscribing"
          >
            {{ isSubscribed ? '取消订阅' : '订阅实时数据' }}
          </van-button>
          <van-button
            type="default"
            @click="querySubscriptions"
            v-if="connectionStatus.isConnected"
          >
            查询订阅状态
          </van-button>
        </div>
      </div>
    </div>

    <!-- 实时价格显示 -->
    <div class="price-display" v-if="latestPriceData">
      <div class="price-card">
        <h3>💰 实时价格数据</h3>
        <div class="price-info">
          <div class="price-main">
            <div class="symbol">{{ latestPriceData.symbol }}</div>
            <div class="price">${{ latestPriceData.price }}</div>
            <div class="volume">成交量: {{ formatVolume(latestPriceData.size) }}</div>
          </div>
          <div class="price-meta">
            <div class="timestamp">更新时间: {{ formatTime(latestPriceData.timestamp) }}</div>
            <div class="update-count">更新次数: {{ updateCount }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- K线图展示 -->
    <div class="kline-display" v-if="isSubscribed">
      <div class="chart-card">
        <h3>📊 实时K线图</h3>
        <KLineChartComponent
          :symbol="testSymbol"
          :name="testSymbol"
          @priceUpdate="handleKLinePriceUpdate"
        />
      </div>
    </div>

    <!-- WebSocket消息日志 -->
    <div class="message-logs">
      <div class="logs-card">
        <div class="logs-header">
          <h3>📝 WebSocket消息日志</h3>
          <div class="log-controls">
            <van-button size="small" @click="clearLogs">清空日志</van-button>
            <van-button size="small" @click="toggleAutoScroll">
              {{ autoScroll ? '停止滚动' : '自动滚动' }}
            </van-button>
          </div>
        </div>
        <div class="logs-container" ref="logsContainer">
          <div
            v-for="(log, index) in messageLogs"
            :key="index"
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-type">{{ log.type.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="messageLogs.length === 0" class="no-logs">
            暂无消息日志
          </div>
        </div>
      </div>
    </div>

    <!-- 测试说明 -->
    <div class="test-documentation">
      <div class="doc-card">
        <h3>📖 测试说明</h3>
        <div class="doc-content">
          <h4>🎯 测试目标</h4>
          <p>验证基于docs/websocket.md文档的新Polygon WebSocket API集成效果。</p>

          <h4>🔧 主要功能</h4>
          <ul>
            <li>✅ 基于 wss://api.coin-tx.exchange/ws/polygon 的WebSocket连接</li>
            <li>✅ 股票实时价格数据订阅和推送</li>
            <li>✅ 实时K线图数据更新</li>
            <li>✅ WebSocket连接状态管理和重连机制</li>
            <li>✅ 心跳检测和错误处理</li>
          </ul>

          <h4>🧪 测试步骤</h4>
          <ol>
            <li>观察WebSocket连接状态</li>
            <li>输入股票代码（推荐：AAPL、NVDA、TSLA、MSFT）</li>
            <li>点击订阅按钮开始接收实时数据</li>
            <li>观察价格更新和K线图变化</li>
            <li>查看WebSocket消息日志</li>
          </ol>

          <h4>🔍 预期结果</h4>
          <ul>
            <li>WebSocket成功连接到服务器</li>
            <li>股票订阅成功，返回订阅确认消息</li>
            <li>实时接收price_update类型的消息</li>
            <li>K线图实时更新显示最新价格</li>
            <li>心跳pong消息定期返回</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import KLineChartComponent from '@/page/kline/components/KLineChartComponent.vue'
import {
  connectPolygonWS,
  disconnectPolygonWS,
  subscribePolygonRealTime,
  unsubscribePolygonRealTime,
  getPolygonWSStatus
} from '@/services/polygonWebSocket'
import polygonWS from '@/services/polygonWebSocket'

export default {
  name: 'PolygonWebSocketTest',
  components: {
    KLineChartComponent
  },
  data() {
    return {
      // 测试配置
      testSymbol: 'AAPL',
      isSubscribing: false,
      isSubscribed: false,

      // 连接状态
      connectionStatus: {
        isConnected: false,
        userId: '',
        reconnectAttempts: 0,
        subscriptions: []
      },

      // 实时数据
      latestPriceData: null,
      updateCount: 0,

      // 订阅管理
      unsubscribeFunction: null,

      // 日志系统
      messageLogs: [],
      autoScroll: true,
      maxLogs: 100,

      // 定时器
      statusTimer: null
    }
  },

  async mounted() {
    this.addLog('info', '页面初始化，准备连接Polygon WebSocket服务')
    await this.initializeConnection()
    this.startStatusPolling()
  },

  beforeDestroy() {
    this.cleanup()
  },

  methods: {
    // 初始化连接
    async initializeConnection() {
      try {
        this.addLog('info', '正在连接Polygon WebSocket...')
        const userId = `test_${Date.now()}`

        const connected = await connectPolygonWS(userId)

        if (connected) {
          this.addLog('success', 'Polygon WebSocket连接成功')
          this.updateConnectionStatus()
        } else {
          this.addLog('error', 'Polygon WebSocket连接失败')
        }
      } catch (error) {
        this.addLog('error', `连接失败: ${error.message}`)
      }
    },

    // 订阅股票
    async subscribeStock() {
      if (this.isSubscribed) {
        // 取消订阅
        await this.unsubscribeStock()
        return
      }

      if (!this.testSymbol.trim()) {
        this.$toast('Please enter stock symbol')
        return
      }

      try {
        this.isSubscribing = true
        const symbol = this.testSymbol.toUpperCase().trim()

        this.addLog('info', `正在订阅股票: ${symbol}`)

        this.unsubscribeFunction = await subscribePolygonRealTime(symbol, (data) => {
          this.handleRealtimeData(data)
        })

        this.isSubscribed = true
        this.addLog('success', `成功订阅股票: ${symbol}`)
        this.updateConnectionStatus()

      } catch (error) {
        this.addLog('error', `订阅失败: ${error.message}`)
        this.$toast('Subscription failed, please check connection status')
      } finally {
        this.isSubscribing = false
      }
    },

    // 取消订阅
    async unsubscribeStock() {
      try {
        if (this.unsubscribeFunction) {
          this.unsubscribeFunction()
          this.unsubscribeFunction = null
        }

        this.isSubscribed = false
        this.latestPriceData = null
        this.updateCount = 0

        this.addLog('info', `已取消订阅股票: ${this.testSymbol}`)
        this.updateConnectionStatus()

      } catch (error) {
        this.addLog('error', `取消订阅失败: ${error.message}`)
      }
    },

    // 处理实时数据
    handleRealtimeData(data) {
      try {
        this.latestPriceData = data
        this.updateCount++

        this.addLog('data', `价格更新: ${data.symbol} $${data.price}`)

        // 自动滚动到最新日志
        if (this.autoScroll) {
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        }
      } catch (error) {
        this.addLog('error', `处理实时数据失败: ${error.message}`)
      }
    },

    // 处理K线图价格更新
    handleKLinePriceUpdate(priceData) {
      this.addLog('kline', `K线更新: $${priceData.price} (${priceData.changePercent}%)`)
    },

    // 查询订阅状态
    querySubscriptions() {
      this.addLog('info', '手动查询订阅状态...')
      polygonWS.querySubscriptions()
    },

    // 更新连接状态
    updateConnectionStatus() {
      try {
        this.connectionStatus = getPolygonWSStatus()
      } catch (error) {
        console.error('获取连接状态失败:', error)
      }
    },

    // 开始状态轮询
    startStatusPolling() {
      this.statusTimer = setInterval(() => {
        this.updateConnectionStatus()
      }, 3000)
    },

    // 添加日志
    addLog(type, message) {
      const log = {
        type,
        message,
        timestamp: Date.now()
      }

      this.messageLogs.push(log)

      // 限制日志数量
      if (this.messageLogs.length > this.maxLogs) {
        this.messageLogs.shift()
      }

      console.log(`[${type.toUpperCase()}] ${message}`)
    },

    // 清空日志
    clearLogs() {
      this.messageLogs = []
      this.addLog('info', '日志已清空')
    },

    // 切换自动滚动
    toggleAutoScroll() {
      this.autoScroll = !this.autoScroll
      if (this.autoScroll) {
        this.scrollToBottom()
      }
    },

    // 滚动到底部
    scrollToBottom() {
      const container = this.$refs.logsContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },

    // 格式化时间
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString()
    },

    // 格式化成交量
    formatVolume(volume) {
      if (!volume) return '0'

      if (volume >= 1000000) {
        return (volume / 1000000).toFixed(1) + 'M'
      } else if (volume >= 1000) {
        return (volume / 1000).toFixed(1) + 'K'
      }

      return volume.toString()
    },

    // 显示帮助
    showHelp() {
      this.$toast('This is the Polygon WebSocket API test page for verifying real-time stock data subscription')
    },

    // 清理资源
    cleanup() {
      // 停止状态轮询
      if (this.statusTimer) {
        clearInterval(this.statusTimer)
        this.statusTimer = null
      }

      // 取消订阅
      if (this.unsubscribeFunction) {
        this.unsubscribeFunction()
        this.unsubscribeFunction = null
      }

      // 断开连接
      disconnectPolygonWS()

      this.addLog('info', '页面资源清理完成')
    }
  }
}
</script>

<style scoped>
.polygon-websocket-test {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0 0 20px 0;
}

/* 顶部标题 */
.test-header {
  background: #fff;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.test-header h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

/* 连接状态 */
.connection-status {
  padding: 16px;
}

.status-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 600;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.connected .indicator-dot {
  background: #52c41a;
}

.status-indicator.disconnected .indicator-dot {
  background: #ff4d4f;
}

.status-details {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
}

.detail-item {
  text-align: center;
}

.detail-item .label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.detail-item .value {
  font-weight: 600;
  color: #333;
}

/* 订阅测试 */
.subscription-test {
  padding: 0 16px 16px;
}

.test-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-card h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 价格显示 */
.price-display {
  padding: 0 16px 16px;
}

.price-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.price-card h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
}

.price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-main {
  flex: 1;
}

.symbol {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.price {
  font-size: 24px;
  font-weight: 700;
  color: #1890ff;
  margin-bottom: 4px;
}

.volume {
  font-size: 14px;
  color: #666;
}

.price-meta {
  text-align: right;
  font-size: 12px;
  color: #999;
}

.price-meta > div {
  margin-bottom: 4px;
}

/* K线图显示 */
.kline-display {
  padding: 0 16px 16px;
}

.chart-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-card h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
}

/* 日志系统 */
.message-logs {
  padding: 0 16px 16px;
}

.logs-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.logs-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.log-controls {
  display: flex;
  gap: 8px;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  background: #fafafa;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #999;
  margin-right: 8px;
  min-width: 80px;
}

.log-type {
  font-weight: 600;
  margin-right: 8px;
  min-width: 60px;
}

.log-item.info .log-type {
  color: #1890ff;
}

.log-item.success .log-type {
  color: #52c41a;
}

.log-item.error .log-type {
  color: #ff4d4f;
}

.log-item.data .log-type {
  color: #722ed1;
}

.log-item.kline .log-type {
  color: #fa8c16;
}

.log-message {
  color: #333;
  flex: 1;
}

.no-logs {
  text-align: center;
  color: #999;
  padding: 20px;
}

/* 测试说明 */
.test-documentation {
  padding: 0 16px;
}

.doc-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.doc-card h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
}

.doc-content h4 {
  margin: 16px 0 8px 0;
  font-size: 14px;
  color: #666;
}

.doc-content p {
  margin: 8px 0;
  line-height: 1.6;
  color: #333;
}

.doc-content ul,
.doc-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.doc-content li {
  margin: 4px 0;
  line-height: 1.6;
  color: #333;
}
</style>
