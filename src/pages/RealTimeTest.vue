<template>
  <div class="realtime-test">
    <!-- 页面标题 -->
    <div class="page-header">
      <van-nav-bar
        title="实时数据测试"
        left-text="返回"
        left-arrow
        @click-left="$router.back()"
      />
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="section">
        <h3>📊 实时数据订阅</h3>
        <div class="input-group">
          <van-field
            v-model="testSymbol"
            label="股票代码"
            placeholder="输入美股代码，如AAPL"
          />
          <van-field
            v-model="pollingInterval"
            label="更新间隔(秒)"
            type="number"
            placeholder="5"
          />
        </div>

        <div class="button-group">
          <van-button
            type="primary"
            size="small"
            @click="startSubscription"
            :disabled="isSubscribed"
          >
            开始订阅
          </van-button>
          <van-button
            type="danger"
            size="small"
            @click="stopSubscription"
            :disabled="!isSubscribed"
          >
            停止订阅
          </van-button>
          <van-button
            type="warning"
            size="small"
            @click="clearLogs"
          >
            清空日志
          </van-button>
        </div>
      </div>

      <!-- 状态显示 -->
      <div class="status-section">
        <div class="status-item">
          <span class="label">订阅状态:</span>
          <span class="value" :class="isSubscribed ? 'success' : 'error'">
            {{ isSubscribed ? '已订阅' : '未订阅' }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">当前股票:</span>
          <span class="value">{{ currentSymbol || '无' }}</span>
        </div>
        <div class="status-item">
          <span class="label">更新间隔:</span>
          <span class="value">{{ currentInterval }}秒</span>
        </div>
        <div class="status-item">
          <span class="label">更新次数:</span>
          <span class="value">{{ updateCount }}</span>
        </div>
      </div>
    </div>

    <!-- 实时价格显示 -->
    <div class="price-display" v-if="latestPrice">
      <div class="price-card">
        <div class="symbol">{{ latestPrice.symbol }}</div>
        <div class="price">${{ latestPrice.price }}</div>
        <div class="change" :class="getPriceChangeClass(latestPrice.change)">
          {{ latestPrice.change > 0 ? '+' : '' }}{{ latestPrice.change.toFixed(2) }}
          ({{ latestPrice.changePercent.toFixed(2) }}%)
        </div>
        <div class="meta">
          <span>成交量: {{ formatVolume(latestPrice.volume) }}</span>
          <span>更新时间: {{ formatTime(latestPrice.timestamp) }}</span>
        </div>
      </div>
    </div>

    <!-- K线图 -->
    <div class="chart-section" v-if="isSubscribed">
      <h3>📈 实时K线图</h3>
      <KLineComponent
        :symbol="currentSymbol"
        :name="currentSymbol"
      />
    </div>

    <!-- API日志 -->
    <div class="logs-section">
      <h3>📝 实时数据日志</h3>
      <div class="logs-container">
        <div
          v-for="(log, index) in logs"
          :key="index"
          class="log-item"
          :class="log.type"
        >
          <span class="timestamp">{{ formatTime(log.timestamp) }}</span>
          <span class="content">{{ log.message }}</span>
        </div>
      </div>
    </div>

    <!-- 说明文档 -->
    <div class="documentation">
      <h3>📖 使用说明</h3>
      <div class="doc-content">
        <h4>🎯 测试目的</h4>
        <p>验证基于真实API的实时股票价格更新功能，无mock数据。</p>

        <h4>🔧 功能特性</h4>
        <ul>
          <li>✅ 使用真实的WebSocket API接口</li>
          <li>✅ 实时价格推送（非轮询）</li>
          <li>✅ 实时价格、涨跌幅、成交量显示</li>
          <li>✅ 完整的WebSocket连接管理</li>
          <li>✅ 详细的WebSocket状态日志</li>
        </ul>

        <h4>🧪 测试建议</h4>
        <ul>
          <li>推荐股票: AAPL, NVDA, TSLA, MSFT, GOOGL</li>
          <li>观察控制台Network标签查看WebSocket API调用</li>
          <li>观察实时价格推送和状态变化</li>
          <li>测试WebSocket连接断开重连功能</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { connectRealTimeWS, subscribeRealTime, unsubscribeRealTime, disconnectRealTimeWS, getRealTimeWSStatus } from '@/services/realTimeWebSocketService'
import KLineComponent from '@/page/kline/components/kLine.vue'

export default {
  name: 'RealTimeTest',
  components: {
    KLineComponent
  },
  data() {
    return {
      testSymbol: 'AAPL',
      pollingInterval: 5,
      currentSymbol: '',
      currentInterval: 5,
      isSubscribed: false,
      updateCount: 0,
      latestPrice: null,
      logs: []
    }
  },

  beforeDestroy() {
    if (this.isSubscribed) {
      this.stopSubscription()
    }
  },

  methods: {
        // 开始订阅
    async startSubscription() {
      if (!this.testSymbol) {
        this.$toast('Please enter stock symbol')
        return
      }

      const symbol = this.testSymbol.toUpperCase()

      try {
        // 生成用户ID
        const userId = 'test_user_' + Math.random().toString(36).substr(2, 9)

        // 连接WebSocket
        this.addLog('info', '正在连接WebSocket服务...')
        const connected = await connectRealTimeWS(userId)

        if (!connected) {
          throw new Error('WebSocket连接失败')
        }

        this.addLog('success', 'WebSocket连接成功')

        // 订阅实时数据
        const subscribed = await subscribeRealTime(symbol, (data) => {
          this.handlePriceUpdate(data)
        })

        if (!subscribed) {
          throw new Error('股票订阅失败')
        }

        this.currentSymbol = symbol
        this.currentInterval = 10 // WebSocket实时更新
        this.isSubscribed = true
        this.updateCount = 0
        this.latestPrice = null

        this.addLog('success', `成功订阅 ${symbol} 实时数据 (WebSocket)`)
      } catch (error) {
        this.addLog('error', `订阅失败: ${error.message}`)
      }
    },

    // 停止订阅
    async stopSubscription() {
      if (this.currentSymbol) {
        await unsubscribeRealTime(this.currentSymbol)
        this.addLog('info', `停止订阅 ${this.currentSymbol}`)
      }

      // 断开WebSocket连接
      await disconnectRealTimeWS()
      this.addLog('info', 'WebSocket连接已断开')

      this.isSubscribed = false
      this.currentSymbol = ''
      this.updateCount = 0
      this.latestPrice = null
    },

    // 处理价格更新
    handlePriceUpdate(data) {
      this.updateCount++
      this.latestPrice = data.data

      this.addLog('info', `(WebSocket) ${data.data.symbol}: $${data.data.price} (${data.data.changePercent.toFixed(2)}%)`)
    },

    // 添加日志
    addLog(type, message) {
      this.logs.unshift({
        type,
        message,
        timestamp: Date.now()
      })

      // 保持最多100条日志
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100)
      }
    },

    // 清空日志
    clearLogs() {
      this.logs = []
    },

    // 获取价格变化样式类
    getPriceChangeClass(change) {
      return change > 0 ? 'positive' : change < 0 ? 'negative' : 'neutral'
    },

    // 格式化成交量
    formatVolume(volume) {
      if (volume >= 1000000) {
        return (volume / 1000000).toFixed(1) + 'M'
      } else if (volume >= 1000) {
        return (volume / 1000).toFixed(1) + 'K'
      }
      return volume.toString()
    },

    // 格式化时间
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString()
    }
  }
}
</script>

<style scoped>
.realtime-test {
  padding: 0 0 20px 0;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.control-panel {
  background: white;
  margin: 10px;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.input-group {
  margin-bottom: 15px;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.status-section {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.status-item .label {
  color: #666;
}

.status-item .value {
  font-weight: bold;
}

.status-item .value.success {
  color: #07c160;
}

.status-item .value.error {
  color: #ee0a24;
}

.price-display {
  margin: 10px;
}

.price-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  text-align: center;
}

.price-card .symbol {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.price-card .price {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.price-card .change {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}

.price-card .change.positive {
  color: #07c160;
}

.price-card .change.negative {
  color: #ee0a24;
}

.price-card .change.neutral {
  color: #666;
}

.price-card .meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.chart-section {
  background: white;
  margin: 10px;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  height: 400px;
}

.chart-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.logs-section {
  background: white;
  margin: 10px;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.logs-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  background: #fafafa;
}

.log-item {
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  font-size: 12px;
  display: flex;
  gap: 10px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.success {
  background: #f0f9ff;
  color: #0369a1;
}

.log-item.error {
  background: #fef2f2;
  color: #dc2626;
}

.log-item.info {
  background: #f9fafb;
  color: #374151;
}

.log-item .timestamp {
  font-weight: bold;
  min-width: 80px;
}

.documentation {
  background: white;
  margin: 10px;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.documentation h3, .documentation h4 {
  color: #333;
  margin: 0 0 10px 0;
}

.documentation h3 {
  font-size: 16px;
}

.documentation h4 {
  font-size: 14px;
  margin-top: 15px;
}

.documentation p {
  color: #666;
  line-height: 1.5;
  margin: 0 0 10px 0;
}

.documentation ul {
  color: #666;
  line-height: 1.5;
  margin: 0;
  padding-left: 20px;
}

.documentation li {
  margin-bottom: 5px;
}
</style>
