<template>
  <div class="stock-search">
    <!-- 搜索头部 -->
    <div class="search-header">
      <div class="search-input-container">
        <van-icon name="search" class="search-icon" />
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search stocks, ETFs, options..."
          class="search-input"
          @input="onSearchInput"
          @keyup.enter="performSearch"
        />
        <van-icon name="scan" class="scan-icon" />
      </div>
      <div class="cancel-btn" @click="goBack">Cancel</div>
    </div>

    <!-- AI助手推荐 -->
    <div class="ai-assistant">
      <div class="ai-avatar">
        <img src="/static/img/ai-avatar.png" alt="AI" />
      </div>
      <div class="ai-content">
        <div class="ai-title">Bull AI, your smart investment assistant</div>
      </div>
      <van-icon name="arrow" class="ai-arrow" />
    </div>

    <!-- 筛选器 -->
    <div class="filter-tabs">
      <div class="filter-tab" :class="{ active: activeFilter === 'stock' }" @click="setActiveFilter('stock')">
        <van-icon name="filter-o" />
        <span>Stock Screener</span>
      </div>
      <div class="filter-tab" :class="{ active: activeFilter === 'etf' }" @click="setActiveFilter('etf')">
        <span>ETF</span>
      </div>
      <div class="filter-tab" :class="{ active: activeFilter === 'option' }" @click="setActiveFilter('option')">
        <van-icon name="bar-chart-o" />
        <span>Options</span>
      </div>
      <div class="filter-tab" :class="{ active: activeFilter === 'more' }" @click="setActiveFilter('more')">
        <van-icon name="apps-o" />
        <span>More</span>
      </div>
    </div>

    <!-- 搜索历史 -->
    <div class="search-history" v-if="searchHistory.length > 0">
      <div class="section-header">
        <span class="section-title">搜索历史</span>
        <van-icon name="delete" class="clear-history" @click="clearHistory" />
      </div>
      <div class="history-tags">
        <div
          v-for="(item, index) in searchHistory"
          :key="index"
          class="history-tag"
          @click="searchFromHistory(item)"
        >
          {{ item }}
        </div>
      </div>
    </div>

    <!-- 美股热度榜 -->
    <div class="hot-stocks">
      <div class="section-header">
        <div class="section-title">
          <img src="/static/img/us-flag.png" alt="美股" class="flag-icon" />
          <span>美股热度榜</span>
        </div>
        <van-icon name="arrow" class="section-arrow" />
      </div>

      <!-- 加载状态 -->
      <div v-if="hotStocksLoading" class="loading-container">
        <van-loading type="spinner" />
        <span>加载热门股票中...</span>
      </div>

      <div v-else class="stock-list">
        <div
          v-for="(stock, index) in hotStocks"
          :key="stock.ticker"
          class="stock-item"
          @click="selectStock(stock)"
        >
          <div class="stock-rank">{{ index + 1 }}</div>
          <div class="stock-info">
            <div class="stock-name" :title="stock.name">{{ stock.name }}</div>
            <div class="stock-ticker">{{ stock.ticker }}</div>
          </div>
          <div class="stock-chart">
            <div class="mini-chart" :class="stock.trend">
              <!-- 简化的走势图 -->
              <svg viewBox="0 0 60 20" class="chart-svg">
                <polyline
                  :points="stock.chartPoints"
                  fill="none"
                  :stroke="stock.trend === 'up' ? '#00c853' : '#ff1744'"
                  stroke-width="1"
                />
              </svg>
            </div>
          </div>
          <div class="stock-price">
            <div class="current-price">{{ stock.price }}</div>
            <div class="price-change" :class="stock.trend">
              {{ stock.change }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索结果 -->
    <div v-if="showSearchResults" class="search-results">
      <div v-if="loading" class="loading-container">
        <van-loading type="spinner" />
        <span>搜索中...</span>
      </div>

      <div v-else-if="searchResults.length > 0" class="results-list">
        <div
          v-for="stock in searchResults"
          :key="stock.ticker"
          class="result-item"
          @click="selectStock(stock)"
        >
          <div class="stock-info">
            <div class="stock-name">{{ stock.name }}</div>
            <div class="stock-details">
              <span class="stock-ticker">{{ stock.ticker }}</span>
              <span class="stock-exchange">{{ stock.primary_exchange }}</span>
            </div>
          </div>
          <div class="stock-actions">
            <van-icon
              :name="isInWatchlist(stock.ticker) ? 'star' : 'star-o'"
              :class="['star-icon', { active: isInWatchlist(stock.ticker) }]"
              @click.stop="toggleWatchlist(stock)"
            />
          </div>
        </div>
      </div>

      <div v-else class="no-results">
        <div class="no-results-icon">🔍</div>
        <div class="no-results-text">未找到相关股票</div>
        <div class="no-results-tip">请尝试其他关键词</div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getTickers,
  getAllTickersSnapshot,
  addOption,
  delOption
} from '@/axios/api'

export default {
  name: 'StockSearch',
  data () {
    return {
      searchQuery: '',
      loading: false,
      showSearchResults: false,
      searchResults: [],
      activeFilter: 'stock',

      // 搜索历史
      searchHistory: ['apple'],

      // 热门股票数据 - 将由API填充
      hotStocks: [],

      // 热门股票加载状态
      hotStocksLoading: false,

      // 自选股列表
      watchlistTickers: [],

      // 搜索防抖定时器
      searchTimer: null
    }
  },

  async mounted () {
    this.loadWatchlistTickers()
    await this.loadHotStocksFromAPI()
  },

  methods: {
    // 搜索输入处理
    onSearchInput () {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }

      this.searchTimer = setTimeout(() => {
        if (this.searchQuery.trim()) {
          this.performSearch()
        } else {
          this.showSearchResults = false
        }
      }, 500)
    },

    // 执行搜索
    async performSearch () {
      if (!this.searchQuery.trim()) return

      try {
        this.loading = true
        this.showSearchResults = true

        const params = {
          search: this.searchQuery.trim(),
          market: 'stocks',
          active: true,
          sort: 'ticker',
          order: 'asc',
          limit: 50
        }

        const response = await getTickers(params)

        if (response && response.data && response.data.results) {
          this.searchResults = response.data.results
          // 添加到搜索历史
          this.addToHistory(this.searchQuery.trim())
        } else {
          this.searchResults = []
        }
      } catch (error) {
        this.searchResults = []
        this.$toast('Search failed, please try again')
      } finally {
        this.loading = false
      }
    },

    // 设置活跃筛选器
    setActiveFilter (filter) {
      this.activeFilter = filter
    },

    // 从历史记录搜索
    searchFromHistory (query) {
      this.searchQuery = query
      this.performSearch()
    },

    // 添加到搜索历史
    addToHistory (query) {
      if (!this.searchHistory.includes(query)) {
        this.searchHistory.unshift(query)
        if (this.searchHistory.length > 10) {
          this.searchHistory.pop()
        }
      }
    },

    // 清空搜索历史
    clearHistory () {
      this.searchHistory = []
    },

    // 选择股票
    selectStock (stock) {
      this.$router.push({
        path: '/stock-detail',
        query: {
          symbol: stock.ticker,
          name: stock.name,
          exchange: stock.primary_exchange || stock.exchange,
          type: stock.type,
          market: stock.market
        }
      })
    },

    // 切换自选股状态
    async toggleWatchlist (stock) {
      try {
        const isAdded = this.isInWatchlist(stock.ticker)

        if (isAdded) {
          await delOption({ code: stock.ticker, name: stock.name })
          this.watchlistTickers = this.watchlistTickers.filter(t => t !== stock.ticker)
          this.$toast('Removed from watchlist')
        } else {
          await addOption({ code: stock.ticker, name: stock.name })
          this.watchlistTickers.push(stock.ticker)
          this.$toast('Added to watchlist')
        }
      } catch (error) {
        this.$toast('Operation failed, please try again')
      }
    },

    // 判断是否在自选股中
    isInWatchlist (ticker) {
      return this.watchlistTickers.includes(ticker)
    },

    // 加载自选股列表
    async loadWatchlistTickers () {
      try {
        this.watchlistTickers = ['AAPL', 'MSFT', 'GOOGL']
      } catch (error) {
      }
    },

        // 加载热门股票数据
    async loadHotStocksFromAPI () {
      try {
        this.hotStocksLoading = true

        // 并发获取快照数据和股票基础信息
        const [snapshotResponse, tickersResponse] = await Promise.all([
          getAllTickersSnapshot({
            include_otc: false
          }).catch(e => ({ error: e })),

          getTickers({
            market: 'stocks',
            active: true,
            sort: 'ticker',
            order: 'asc',
            limit: 1000 // 获取更多股票以便匹配
          }).catch(e => ({ error: e }))
        ])

        if (snapshotResponse && !snapshotResponse.error && snapshotResponse.data && snapshotResponse.data.results) {
          // 创建股票名称映射表
          const tickerNameMap = {}
          if (tickersResponse && !tickersResponse.error && tickersResponse.data && tickersResponse.data.results) {
            tickersResponse.data.results.forEach(ticker => {
              tickerNameMap[ticker.ticker] = ticker.name
            })
          }

          // 对数据按成交量排序，取前10名作为热度榜
          const sortedStocks = snapshotResponse.data.results
            .filter(stock => stock.day && stock.day.v && stock.day.v > 0) // 过滤有成交量的股票
            .sort((a, b) => (b.day.v || 0) - (a.day.v || 0)) // 按成交量降序排序
            .slice(0, 10) // 取前10名

          // 转换数据格式
          this.hotStocks = sortedStocks.map((stock, index) => {
            const dayData = stock.day || {}
            const prevDayData = stock.prevDay || {}

            const currentPrice = dayData.c || stock.value || 0
            const previousClose = prevDayData.c || dayData.o || currentPrice
            const changeAmount = currentPrice - previousClose
            const changePercent = previousClose > 0 ? (changeAmount / previousClose * 100) : 0

            const trend = changeAmount >= 0 ? 'up' : 'down'
            const changeText = changeAmount >= 0 ? `+${changePercent.toFixed(2)}%` : `${changePercent.toFixed(2)}%`

            // 生成简化的图表数据点
            const chartPoints = this.generateMiniChartPoints(trend)

            return {
              name: tickerNameMap[stock.ticker] || stock.ticker, // 优先使用完整公司名，fallback到ticker
              ticker: stock.ticker,
              price: currentPrice.toFixed(3),
              change: changeText,
              trend: trend,
              chartPoints: chartPoints,
              volume: dayData.v || 0, // 保存成交量用于调试
              high: dayData.h || 0,
              low: dayData.l || 0,
              open: dayData.o || 0
            }
          })

        }
      } catch (error) {
        console.error('加载热门股票数据出错：', error)
      } finally {
        this.hotStocksLoading = false
      }
    },

    // 生成迷你图表数据点
    generateMiniChartPoints (trend) {
      // 根据趋势生成简化的SVG路径点
      if (trend === 'up') {
        return '0,15 15,12 30,8 45,6 60,3'
      } else {
        return '0,5 15,8 30,12 45,15 60,18'
      }
    },

    // 返回上一页
    goBack () {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.stock-search {
  background: #f5f5f5;
  min-height: 100vh;
}

.search-header {
  background: #fff;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 20px;
  padding: 8px 16px;
  height: 36px;
}

.search-icon {
  color: #999;
  font-size: 16px;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  font-size: 15px;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.scan-icon {
  color: #999;
  font-size: 18px;
  margin-left: 8px;
}

.cancel-btn {
  color: #333;
  font-size: 16px;
  cursor: pointer;
}

.ai-assistant {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  margin: 8px 16px;
  border-radius: 12px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
}

.ai-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #2196f3;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: bold;
}

.ai-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.ai-content {
  flex: 1;
}

.ai-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.ai-arrow {
  color: #666;
  font-size: 16px;
}

.filter-tabs {
  background: #fff;
  padding: 16px;
  display: flex;
  gap: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s;
}

.filter-tab.active {
  background: #fff3e0;
  color: #ff9800;
}

.filter-tab span {
  font-size: 13px;
  color: #666;
}

.filter-tab.active span {
  color: #ff9800;
}

.search-history {
  background: #fff;
  padding: 16px;
  margin-top: 8px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.flag-icon {
  width: 20px;
  height: 14px;
  border-radius: 2px;
}

.clear-history {
  color: #999;
  font-size: 16px;
  cursor: pointer;
}

.section-arrow {
  color: #ccc;
  font-size: 16px;
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.history-tag {
  background: #f5f5f5;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.hot-stocks {
  background: #fff;
  margin-top: 8px;
}

.section-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.stock-list {
  padding: 0 16px;
}

.stock-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f8f9fa;
  cursor: pointer;
}

.stock-item:last-child {
  border-bottom: none;
}

.stock-rank {
  width: 20px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-right: 12px;
}

.stock-info {
  flex: 1;
  margin-right: 12px;
}

.stock-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.stock-ticker {
  font-size: 13px;
  color: #666;
}

.stock-chart {
  width: 60px;
  height: 20px;
  margin-right: 12px;
}

.mini-chart {
  width: 100%;
  height: 100%;
}

.chart-svg {
  width: 100%;
  height: 100%;
}

.stock-price {
  text-align: right;
  min-width: 80px;
}

.current-price {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.price-change {
  font-size: 12px;
}

.price-change.up {
  color: #00c853;
}

.price-change.down {
  color: #ff1744;
}

.search-results {
  background: #fff;
  margin-top: 8px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #666;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.result-item:hover {
  background: #f8f9fa;
}

.result-item .stock-info {
  flex: 1;
}

.result-item .stock-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.stock-details {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.stock-details .stock-ticker {
  font-weight: 500;
  color: #007bff;
}

.stock-exchange {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
}

.star-icon {
  color: #ddd;
  font-size: 20px;
  cursor: pointer;
}

.star-icon.active {
  color: #ffd700;
}

.no-results {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-results-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-results-text {
  font-size: 18px;
  margin-bottom: 8px;
}

.no-results-tip {
  font-size: 14px;
  color: #999;
}
</style>
