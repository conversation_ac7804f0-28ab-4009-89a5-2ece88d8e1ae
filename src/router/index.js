import Vue from 'vue'
import Router from 'vue-router'

// 富途牛牛页面
import FutuMarket from '@/pages/FutuMarket.vue'
import FutuStockDetail from '@/pages/FutuStockDetail.vue'
import FutuWatchlist from '@/pages/FutuWatchlist.vue'
import FutuProfile from '@/pages/FutuProfile.vue'
import FutuDiscover from '@/pages/FutuDiscover.vue'
import FutuWelcome from '@/pages/FutuWelcome.vue'
import FutuLogin from '@/pages/FutuLogin.vue'
import FutuSignUp from '@/pages/FutuSignUp.vue'
import FutuOpenAccount from '@/pages/FutuOpenAccount.vue'
import FutuOpenAccountSteps from '@/pages/FutuOpenAccountSteps.vue'
import FutuOpenAccountMethods from '@/pages/FutuOpenAccountMethods.vue'
import FutuOpenAccountProcess from '@/pages/FutuOpenAccountProcess.vue'
import FutuIdentitySelection from '@/pages/FutuIdentitySelection.vue'
import FutuUploadDocuments from '@/pages/FutuUploadDocuments.vue'
import FutuAddressProof from '@/pages/FutuAddressProof.vue'
import FutuInvestmentExperience from '@/pages/FutuInvestmentExperience.vue'
import FutuTaxInformation from '@/pages/FutuTaxInformation.vue'
import FutuGainersLosers from '@/pages/FutuGainersLosers.vue'

// 测试页面
import MarketDepthTest from '@/pages/MarketDepthTest.vue'
import PolygonWebSocketTest from '@/pages/PolygonWebSocketTest.vue'

// 用户认证页面
import Forget from '@/page/forget'

// K线图和交易相关
import KLine from '@/page/kline/index.vue'
import KLineTest from '@/page/kline/test.vue'
import buyStock from '@/page/kline/buyStock.vue'
import buyStocksDown from '@/page/kline/buyStocksDown.vue'
import TradingBuy from '@/page/trading/buy.vue'

// 用户中心相关
import Setting from '@/page/user/my'
import Detail from '@/page/user/detail'
import OrderList from '@/page/user/order-list'
import Warehouse from '@/page/user/Warehouse.vue'
import WarehouseDetail from '@/page/user/Warehouse-detail.vue'
import Recharge from '@/page/user/recharge'
import Cash from '@/page/user/cash'
import AddCard from '@/page/user/addCard'
import Card from '@/page/user/card'
import Authentication from '@/page/user/authentication'

Vue.use(Router)

const routerPush = Router.prototype.push
Router.prototype.push = function push (location) {
  return routerPush.call(this, location).catch(error => error)
}

export default new Router({
  routes: [
    // 默认重定向到欢迎页面
    {
      path: '/',
      redirect: '/futu-welcome'
    },

    // ==================== 富途牛牛核心页面 ====================
    {
      path: '/market',
      name: 'FutuMarket',
      meta: {
        title: 'Futu Bull - Market',
        requireAuth: false,
        index: 0
      },
      component: FutuMarket
    },

    {
      path: '/futu-trade',
      name: 'FutuTrade',
      meta: {
        title: 'Paper Trading',
        requireAuth: false,
        index: 1,
        show: true
      },
      component: () => import('@/pages/FutuTrade.vue')
    },
    {
      path: '/stock-detail',
      name: 'FutuStockDetail',
      meta: {
        title: 'Stock Details',
        requireAuth: false,
        index: 1
      },
      component: FutuStockDetail
    },
    {
      path: '/watchlist',
      name: 'FutuWatchlist',
      meta: {
        title: 'Watchlist',
        requireAuth: false,
        index: 1
      },
      component: FutuWatchlist
    },
    {
      path: '/search',
      name: 'StockSearch',
      meta: {
        title: 'Stock Search',
        requireAuth: false,
        index: 1
      },
      component: () => import('@/pages/StockSearch.vue')
    },
    {
      path: '/market-depth-test',
      name: 'MarketDepthTest',
      meta: {
        title: 'Market Depth Test',
        requireAuth: false,
        index: 1
      },
      component: MarketDepthTest
    },
    {
      path: '/polygon-websocket-test',
      name: 'PolygonWebSocketTest',
      meta: {
        title: 'Polygon WebSocket Test',
        requireAuth: false,
        index: 1
      },
      component: PolygonWebSocketTest
    },
    {
      path: '/gainers-losers',
      name: 'FutuGainersLosers',
      meta: {
        title: 'Gainers & Losers',
        requireAuth: false,
        index: 1
      },
      component: FutuGainersLosers
    },
    {
      path: '/profile',
      name: 'FutuProfile',
      meta: {
        title: 'Profile',
        requireAuth: false,
        index: 1
      },
      component: FutuProfile
    },
    // {
    //   path: '/discover',
    //   name: 'FutuDiscover',
    //   meta: {
    //     title: 'Discover',
    //     requireAuth: false,
    //     index: 1
    //   },
    //   component: FutuDiscover
    // },
    {
      path: '/wealth',
      name: 'FutuWealth',
      meta: {
        title: 'Wealth',
        requireAuth: false,
        index: 1
      },
      component: () => import('@/pages/FutuWealth.vue')
    },
    {
      path: '/api-test',
      name: 'ApiTest',
      meta: {
        title: 'API Interface Test',
        requireAuth: false,
        index: 1
      },
      component: () => import('@/pages/ApiTest.vue')
    },
    {
      path: '/auth-test',
      name: 'AuthTest',
      meta: {
        title: 'Auth Interceptor Test',
        requireAuth: false,
        index: 1
      },
      component: () => import('@/pages/AuthTest.vue')
    },
    {
      path: '/open-account',
      name: 'FutuOpenAccount',
      meta: {
        title: '开户',
        requireAuth: false,
        index: 1
      },
      component: FutuOpenAccount
    },
    {
      path: '/open-account/steps',
      name: 'FutuOpenAccountSteps',
      meta: {
        title: '网上转账开户',
        requireAuth: false,
        index: 2
      },
      component: FutuOpenAccountSteps
    },
    {
      path: '/open-account/methods',
      name: 'FutuOpenAccountMethods',
      meta: {
        title: '请选择开户方式',
        requireAuth: false,
        index: 2
      },
      component: FutuOpenAccountMethods
    },
    {
      path: '/open-account/process',
      name: 'FutuOpenAccountProcess',
      meta: {
        title: '网上转账开户',
        requireAuth: false,
        index: 3
      },
      component: FutuOpenAccountProcess
    },
    {
      path: '/open-account/identity-selection',
      name: 'FutuIdentitySelection',
      meta: {
        title: '网上转账开户',
        requireAuth: false,
        index: 4
      },
      component: FutuIdentitySelection
    },
    {
      path: '/open-account/upload-documents',
      name: 'FutuUploadDocuments',
      meta: {
        title: '网上转账开户',
        requireAuth: false,
        index: 5
      },
      component: FutuUploadDocuments
    },
    {
      path: '/open-account/address-proof',
      name: 'FutuAddressProof',
      meta: {
        title: '网上转账开户',
        requireAuth: false,
        index: 6
      },
      component: FutuAddressProof
    },
    {
      path: '/open-account/investment-experience',
      name: 'FutuInvestmentExperience',
      meta: {
        title: '网上转账开户',
        requireAuth: false,
        index: 7
      },
      component: FutuInvestmentExperience
    },
    {
      path: '/open-account/tax-information',
      name: 'FutuTaxInformation',
      meta: {
        title: '网上转账开户',
        requireAuth: false,
        index: 8
      },
      component: FutuTaxInformation
    },

    // ==================== 用户认证页面 ====================
    {
      path: '/futu-welcome',
      name: 'FutuWelcome',
      meta: {
        title: '富途牛牛',
        requireAuth: false,
        index: 0
      },
      component: FutuWelcome
    },
    {
      path: '/futu-login',
      name: 'futuLogin',
      meta: {
        title: '富途登录',
        requireAuth: false,
        index: 1,
        show: true
      },
      component: FutuLogin
    },
    {
      path: '/futu-signup',
      name: 'FutuSignUp',
      meta: {
        title: '富途注册',
        requireAuth: false,
        index: 1
      },
      component: FutuSignUp
    },
    {
      path: '/login',
      redirect: '/futu-welcome'
    },
    {
      path: '/register',
      redirect: '/futu-welcome'
    },
    {
      path: '/forget',
      name: 'forget',
      meta: {
        title: '忘记密码',
        requireAuth: false,
        index: 0
      },
      component: Forget
    },

    // ==================== K线图和交易页面 ====================
    {
      path: '/kline',
      name: 'kline',
      meta: {
        title: 'K线图',
        requireAuth: true,
        index: 1
      },
      component: KLine
    },
    {
      path: '/kline-test',
      name: 'klineTest',
      meta: {
        title: 'KLineCharts美股测试',
        requireAuth: false,
        index: 1
      },
      component: KLineTest
    },
    {
      path: '/realtime-test',
      name: 'realtimeTest',
      meta: {
        title: '实时数据测试',
        requireAuth: false,
        index: 1
      },
      component: () => import('@/pages/RealTimeTest.vue')
    },
    {
      path: '/buyStocks',
      name: 'buyStocks',
      meta: {
        title: '买入',
        requireAuth: true,
        index: 1
      },
      component: buyStock
    },
    {
      path: '/buyStocksDown',
      name: 'buyStocksDown',
      meta: {
        title: '卖出',
        requireAuth: true,
        index: 1
      },
      component: buyStocksDown
    },
    {
      path: '/trading',
      name: 'trading',
      meta: {
        title: '交易',
        requireAuth: true,
        index: 1
      },
      component: TradingBuy
    },

    // ==================== 用户中心页面 ====================
    {
      path: '/user',
      name: 'user',
      meta: {
        title: '个人中心',
        requireAuth: true,
        index: 1
      },
      component: Setting
    },
    {
      path: '/user/detail',
      name: 'userDetail',
      meta: {
        title: '个人资料',
        requireAuth: true,
        index: 2
      },
      component: Detail
    },
    {
      path: '/user/orders',
      name: 'userOrders',
      meta: {
        title: '我的订单',
        requireAuth: true,
        index: 2
      },
      component: OrderList
    },
    {
      path: '/user/warehouse',
      name: 'warehouse',
      meta: {
        title: '我的持仓',
        requireAuth: true,
        index: 2
      },
      component: Warehouse
    },
    {
      path: '/user/warehouse-detail',
      name: 'warehouseDetail',
      meta: {
        title: '持仓详情',
        requireAuth: true,
        index: 3
      },
      component: WarehouseDetail
    },
    {
      path: '/user/recharge',
      name: 'recharge',
      meta: {
        title: '充值',
        requireAuth: true,
        index: 2
      },
      component: Recharge
    },
    {
      path: '/user/withdraw',
      name: 'withdraw',
      meta: {
        title: '提现',
        requireAuth: true,
        index: 2
      },
      component: Cash
    },
    {
      path: '/user/cards',
      name: 'cards',
      meta: {
        title: '银行卡',
        requireAuth: true,
        index: 2
      },
      component: Card
    },
    {
      path: '/user/add-card',
      name: 'addCard',
      meta: {
        title: '添加银行卡',
        requireAuth: true,
        index: 3
      },
      component: AddCard
    },
    {
      path: '/user/auth',
      name: 'authentication',
      meta: {
        title: '实名认证',
        requireAuth: true,
        index: 2
      },
      component: Authentication
    },

    // ==================== 老页面重定向 ====================
    // 将所有老的首页和列表页面重定向到富途牛牛
    {
      path: '/home',
      redirect: '/watchlist'
    },
    {
      path: '/list',
      redirect: '/watchlist'
    },
    {
      path: '/list/:any*',
      redirect: '/watchlist'
    },
    {
      path: '/home/<USER>',
      redirect: '/watchlist'
    }
  ]
})
