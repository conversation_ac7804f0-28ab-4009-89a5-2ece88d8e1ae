/**
 * AGGREGATES API Service
 * 处理K线数据相关的API调用和数据格式化
 */

import * as api from '@/axios/api'
import { Toast } from 'mint-ui'

export const AggregatesService = {
  /**
   * 获取K线数据
   * @param {Object} options - 请求参数
   * @param {string} options.ticker - 股票代码 (必需)
   * @param {number} options.multiplier - 时间倍数 (必需)
   * @param {string} options.timespan - 时间单位 (必需: minute, hour, day, week, month, quarter, year)
   * @param {string} options.from - 开始日期 (必需: YYYY-MM-DD)
   * @param {string} options.to - 结束日期 (必需: YYYY-MM-DD)
   * @param {boolean} options.adjusted - 是否调整股价 (默认: true)
   * @param {string} options.sort - 排序方向 (默认: asc)
   * @param {number} options.limit - 数据点数量 (默认: 5000)
   * @returns {Promise<Object>} 格式化后的K线数据
   */
  async getAggregates (options) {
    try {
      // 参数验证
      if (!options.ticker) {
        throw new Error('股票代码(ticker)是必需的')
      }
      if (!options.multiplier) {
        throw new Error('时间倍数(multiplier)是必需的')
      }
      if (!options.timespan) {
        throw new Error('时间单位(timespan)是必需的')
      }
      if (!options.from) {
        throw new Error('开始日期(from)是必需的')
      }
      if (!options.to) {
        throw new Error('结束日期(to)是必需的')
      }

      // 设置默认值
      const params = {
        ticker: options.ticker,
        multiplier: options.multiplier,
        timespan: options.timespan,
        from: options.from,
        to: options.to,
        adjusted: options.adjusted !== undefined ? options.adjusted : true,
        sort: options.sort || 'asc',
        limit: options.limit || 5000
      }

      const response = await api.getAggregates(params)

      if (response && response.success && response.data) {
        const formattedData = this.formatAggregatesData(response.data, options.ticker)
        return formattedData
      } else {
        throw new Error('API响应异常: ' + (response && response.message ? response.message : '未知错误'))
      }
    } catch (error) {
      console.error('❌ [AggregatesService] API调用失败:', error)

      // 显示错误提示
      Toast({
        message: `获取K线数据失败: ${error.message}`,
        position: 'top',
        duration: 2000
      })

      throw error
    }
  },

  /**
   * 获取市场日线数据
   * @param {Object} options - 请求参数
   * @param {string} options.date - 日期 (可选: YYYY-MM-DD)
   * @param {boolean} options.adjusted - 是否调整股价 (默认: true)
   * @returns {Promise<Object>} 格式化后的市场数据
   */
  async getGroupedDaily (options = {}) {
    try {
      console.log('🔄 [AggregatesService] 获取市场日线数据:', options)

      const params = {
        date: options.date || this.getYesterday(),
        adjusted: options.adjusted !== undefined ? options.adjusted : true
      }

      const response = await api.getGroupedDaily(params)

      if (response && response.success && response.data) {
        return this.formatGroupedDailyData(response.data)
      } else {
        throw new Error('API响应异常: ' + (response && response.message ? response.message : '未知错误'))
      }
    } catch (error) {
      console.error('❌ [AggregatesService] 获取市场日线数据失败:', error)
      throw error
    }
  },

  /**
   * 获取日开收数据
   * @param {Object} options - 请求参数
   * @param {string} options.ticker - 股票代码 (必需)
   * @param {string} options.date - 日期 (必需: YYYY-MM-DD)
   * @param {boolean} options.adjusted - 是否调整股价 (默认: true)
   * @returns {Promise<Object>} 格式化后的开收盘数据
   */
  async getDailyOpenClose (options) {
    try {
      console.log('🔄 [AggregatesService] 获取日开收数据:', options)

      if (!options.ticker) {
        throw new Error('股票代码(ticker)是必需的')
      }
      if (!options.date) {
        throw new Error('日期(date)是必需的')
      }

      const params = {
        ticker: options.ticker,
        date: options.date,
        adjusted: options.adjusted !== undefined ? options.adjusted : true
      }

      const response = await api.getDailyOpenClose(params)

      if (response && response.success && response.data) {
        return this.formatDailyOpenCloseData(response.data)
      } else {
        throw new Error('API响应异常: ' + (response && response.message ? response.message : '未知错误'))
      }
    } catch (error) {
      console.error('❌ [AggregatesService] 获取日开收数据失败:', error)
      throw error
    }
  },

  /**
   * 获取前收盘价
   * @param {Object} options - 请求参数
   * @param {string} options.ticker - 股票代码 (必需)
   * @param {boolean} options.adjusted - 是否调整股价 (默认: true)
   * @returns {Promise<Object>} 格式化后的前收盘价数据
   */
  async getPreviousClose (options) {
    try {
      console.log('🔄 [AggregatesService] 获取前收盘价:', options)

      if (!options.ticker) {
        throw new Error('股票代码(ticker)是必需的')
      }

      const params = {
        ticker: options.ticker,
        adjusted: options.adjusted !== undefined ? options.adjusted : true
      }

      const response = await api.getPreviousClose(params)

      if (response && response.success && response.data) {
        return this.formatPreviousCloseData(response.data)
      } else {
        throw new Error('API响应异常: ' + (response && response.message ? response.message : '未知错误'))
      }
    } catch (error) {
      console.error('❌ [AggregatesService] 获取前收盘价失败:', error)
      throw error
    }
  },

  /**
   * 格式化K线数据为HQChart格式
   * @param {Object} data - API返回的原始数据
   * @param {string} ticker - 股票代码
   * @returns {Object} HQChart格式的K线数据
   */
  formatAggregatesData (data, ticker) {
    try {
      if (!data.results || !Array.isArray(data.results)) {
        throw new Error('无效的K线数据格式')
      }

      console.log('📊 [AggregatesService] 原始数据示例:', data.results.slice(0, 2))

      const formattedData = data.results.map(item => {
        // 保留原始时间戳，不进行过度格式化
        const timestamp = item.t

        // 为了兼容性，也提供日期字符串格式
        const date = new Date(timestamp)
        const dateStr = `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`

        const formatted = {
          date: dateStr,
          open: parseFloat(item.o || 0),
          high: parseFloat(item.h || 0),
          low: parseFloat(item.l || 0),
          close: parseFloat(item.c || 0),
          volume: parseInt(item.v || 0),
          amount: parseFloat(item.vw || 0) * parseInt(item.v || 0), // 成交额 = 成交量加权平均价 * 成交量
          timestamp: timestamp // 保留原始时间戳精度
        }

        return formatted
      })

      console.log('✅ [AggregatesService] 格式化完成，数据示例:', formattedData.slice(0, 2))

      const result = {
        symbol: ticker,
        name: ticker,
        data: formattedData,
        count: formattedData.length,
        status: 'success'
      }

      console.log('🎯 [AggregatesService] 最终返回结果:', {
        symbol: result.symbol,
        name: result.name,
        count: result.count,
        status: result.status,
        dataStructure: 'Array[{date, open, high, low, close, volume, amount, timestamp}]'
      })

      return result
    } catch (error) {
      console.error('❌ [AggregatesService] 格式化K线数据失败:', error)
      throw error
    }
  },

  /**
   * 格式化市场日线数据
   * @param {Object} data - API返回的原始数据
   * @returns {Object} 格式化后的市场数据
   */
  formatGroupedDailyData (data) {
    try {
      if (!data.results || !Array.isArray(data.results)) {
        throw new Error('无效的市场数据格式')
      }

      const formattedResults = data.results.map(item => ({
        ticker: item.T,
        open: parseFloat(item.o || 0),
        high: parseFloat(item.h || 0),
        low: parseFloat(item.l || 0),
        close: parseFloat(item.c || 0),
        volume: parseInt(item.v || 0),
        vwap: parseFloat(item.vw || 0),
        timestamp: item.t
      }))

      return {
        results: formattedResults,
        count: formattedResults.length,
        status: 'success'
      }
    } catch (error) {
      console.error('❌ [AggregatesService] 格式化市场数据失败:', error)
      throw error
    }
  },

  /**
   * 格式化日开收数据
   * @param {Object} data - API返回的原始数据
   * @returns {Object} 格式化后的开收盘数据
   */
  formatDailyOpenCloseData (data) {
    try {
      return {
        ticker: data.symbol,
        date: data.from,
        open: parseFloat(data.open || 0),
        high: parseFloat(data.high || 0),
        low: parseFloat(data.low || 0),
        close: parseFloat(data.close || 0),
        volume: parseInt(data.volume || 0),
        afterHours: parseFloat(data.afterHours || 0),
        preMarket: parseFloat(data.preMarket || 0),
        status: 'success'
      }
    } catch (error) {
      console.error('❌ [AggregatesService] 格式化日开收数据失败:', error)
      throw error
    }
  },

  /**
   * 格式化前收盘价数据
   * @param {Object} data - API返回的原始数据
   * @returns {Object} 格式化后的前收盘价数据
   */
  formatPreviousCloseData (data) {
    try {
      if (!data.results || !Array.isArray(data.results) || data.results.length === 0) {
        throw new Error('无效的前收盘价数据格式')
      }

      const result = data.results[0]
      return {
        ticker: result.T,
        previousClose: parseFloat(result.c || 0),
        open: parseFloat(result.o || 0),
        high: parseFloat(result.h || 0),
        low: parseFloat(result.l || 0),
        volume: parseInt(result.v || 0),
        vwap: parseFloat(result.vw || 0),
        timestamp: result.t,
        status: 'success'
      }
    } catch (error) {
      console.error('❌ [AggregatesService] 格式化前收盘价数据失败:', error)
      throw error
    }
  },

  // ======================== 工具方法 ========================

  /**
   * 获取昨天的日期
   * @returns {string} YYYY-MM-DD格式的日期
   */
  getYesterday () {
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    return yesterday.toISOString().split('T')[0]
  },

  /**
   * 获取指定天数前的日期
   * @param {number} days - 天数
   * @returns {string} YYYY-MM-DD格式的日期
   */
  getDaysAgo (days) {
    const date = new Date()
    date.setDate(date.getDate() - days)
    return date.toISOString().split('T')[0]
  },


}

export default AggregatesService
