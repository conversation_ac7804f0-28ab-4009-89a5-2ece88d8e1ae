import * as api from '@/axios/api'
import AggregatesService from './aggregatesService'
import { getLatestPrice, getLatestPrices } from './realTimeWebSocket'

export const KLineService = {
  // 获取分时数据
  async getMinuteData (code, type = 'stock') {
    try {
      // 使用真实API
      const response = await api.getMinuteLine({
        code: code,
        type: type
      })

      if (response && response.success) {
        return this.formatMinuteData(response.data)
      }

      // 如果接口没有数据，返回模拟数据
      return this.generateMockMinuteData()
    } catch (error) {
      return this.generateMockMinuteData()
    }
  },

  // 获取K线数据
  async getKLineData (code, period = 0, type = 'stock') {
    try {
      // 优先使用AGGREGATES API (适用于美股)
      if (type === 'US' || type === 'stock') {
        try {
          const aggregatesData = await this.getAggregatesKLineData(code, period)
          if (aggregatesData && aggregatesData.data && aggregatesData.data.length > 0) {
            return aggregatesData
          }
        } catch (error) {
          // AGGREGATES API失败，降级到原有接口
        }
      }

      // 降级到原有接口
      const response = await api.getMinKEcharts({
        code: code,
        period: period,
        type: type
      })

      if (response && response.success && response.data) {
        return this.formatKLineData(response.data)
      }

      // 如果接口没有数据，返回模拟数据
      return this.generateMockKLineData(period)
    } catch (error) {
      return this.generateMockKLineData(period)
    }
  },

  // 使用AGGREGATES API获取K线数据
  async getAggregatesKLineData (code, period = 0) {
    try {
      const { timespan, multiplier, from, to } = this.getPeriodParams(period)

      const aggregatesData = await AggregatesService.getAggregates({
        ticker: code,
        multiplier: multiplier,
        timespan: timespan,
        from: from,
        to: to,
        adjusted: true,
        sort: 'asc',
        limit: 5000
      })

      return aggregatesData
    } catch (error) {
      throw error
    }
  },

  // 格式化分时数据
  formatMinuteData (data) {
    if (!data || !Array.isArray(data)) {
      return this.generateMockMinuteData()
    }

    const formatted = {
      symbol: data.symbol || 'AAPL',
      name: data.name || 'Apple Inc.',
      time: [],
      price: [],
      volume: [],
      amount: [],
      average: []
    }

    data.forEach(item => {
      formatted.time.push(item.time || item.minute)
      formatted.price.push(parseFloat(item.price || item.current))
      formatted.volume.push(parseInt(item.volume || 0))
      formatted.amount.push(parseFloat(item.amount || 0))
      formatted.average.push(parseFloat(item.average || item.price))
    })

    return formatted
  },

  // 格式化K线数据
  formatKLineData (data) {
    if (!data || !Array.isArray(data)) {
      return this.generateMockKLineData(0)
    }

    const formatted = {
      symbol: data.symbol || 'AAPL',
      name: data.name || 'Apple Inc.',
      data: []
    }

    data.forEach(item => {
      formatted.data.push({
        date: item.date || item.time,
        open: parseFloat(item.open),
        high: parseFloat(item.high),
        low: parseFloat(item.low),
        close: parseFloat(item.close),
        volume: parseInt(item.volume),
        amount: parseFloat(item.amount || 0)
      })
    })

    return formatted
  },

  // 生成模拟分时数据
  generateMockMinuteData () {
    const basePrice = 145.00
    const data = {
      symbol: 'AAPL',
      name: 'Apple Inc.',
      time: [],
      price: [],
      volume: [],
      amount: [],
      average: []
    }

    // 生成一天的分时数据（美股交易时间）
    const startTime = new Date()
    startTime.setHours(9, 30, 0, 0)

    for (let i = 0; i < 390; i++) { // 6.5小时 * 60分钟
      const time = new Date(startTime.getTime() + i * 60000)
      const timeStr = `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`

      const randomChange = (Math.random() - 0.5) * 0.5
      const price = basePrice + randomChange + Math.sin(i / 30) * 2
      const volume = Math.floor(Math.random() * 1000000) + 100000

      data.time.push(timeStr)
      data.price.push(price)
      data.volume.push(volume)
      data.amount.push(price * volume)
      data.average.push(basePrice + Math.sin(i / 30) * 0.5)
    }

    return data
  },

  // 生成模拟K线数据
  generateMockKLineData (period = 0) {
    const data = {
      symbol: 'AAPL',
      name: 'Apple Inc.',
      data: []
    }

    const days = period === 0 ? 100 : 50 // 日线100天，其他50条
    const basePrice = 145.00
    const now = new Date()

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 3600 * 1000)
      const dateStr = `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`

      const open = basePrice + (Math.random() - 0.5) * 5
      const close = open + (Math.random() - 0.5) * 3
      const high = Math.max(open, close) + Math.random() * 2
      const low = Math.min(open, close) - Math.random() * 2
      const volume = Math.floor(Math.random() * 10000000) + 1000000

      data.data.push({
        date: dateStr,
        open: open,
        high: high,
        low: low,
        close: close,
        volume: volume,
        amount: close * volume
      })
    }

    return data
  },

  // 实时数据更新
  async getRealTimeData (code, type = 'stock') {
    try {
      // 优先使用WebSocket服务获取最新价格 (适用于美股)
      if (type === 'US' || type === 'stock') {
        try {
          const priceData = await getLatestPrice(code)
          if (priceData) {
            return {
              price: priceData.price,
              volume: priceData.volume,
              change: priceData.change,
              changePercent: priceData.changePercent,
              time: new Date().toTimeString().substr(0, 5),
              timestamp: priceData.timestamp
            }
          }
        } catch (error) {
          // WebSocket服务失败，降级到原有接口
        }
      }

      // 降级到原有接口
      let response
      if (type === 'US') {
        response = await api.getUsDetail({ code: code })
      } else {
        response = await api.getSingleStock({ code: code })
      }

      if (response && response.success && response.data) {
        return {
          price: parseFloat(response.data.price || response.data.nowPrice),
          volume: parseInt(response.data.volume || 0),
          change: parseFloat(response.data.change || 0),
          changePercent: parseFloat(response.data.changePercent || 0),
          time: new Date().toTimeString().substr(0, 5),
          timestamp: Date.now()
        }
      }

      return null
    } catch (error) {
      return null
    }
  },

  // 批量获取实时数据
  async getBatchRealTimeData (codes, type = 'stock') {
    try {
      // 优先使用WebSocket服务
      if (type === 'US' || type === 'stock') {
        try {
          const pricesData = await getLatestPrices(codes)
          if (pricesData && Object.keys(pricesData).length > 0) {
            return pricesData
          }
        } catch (error) {
          // WebSocket批量服务失败，降级到单个请求
        }
      }

      // 降级到单个请求
      const results = {}
      for (const code of codes) {
        const data = await this.getRealTimeData(code, type)
        if (data) {
          results[code] = data
        }
      }

      return results
    } catch (error) {
      return {}
    }
  },

  // ======================== 工具方法 ========================

  /**
   * 根据周期获取API参数
   * @param {number} period - K线周期
   * @returns {Object} API参数
   */
  getPeriodParams (period) {
    const now = new Date()
    const to = now.toISOString().split('T')[0] // YYYY-MM-DD
    let from, timespan, multiplier

    switch (period) {
      case 0: // 日线
        timespan = 'day'
        multiplier = 1
        from = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 1年前
        break
      case 1: // 1分钟
        timespan = 'minute'
        multiplier = 1
        from = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 7天前
        break
      case 2: // 5分钟
        timespan = 'minute'
        multiplier = 5
        from = new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 5天前（修改：从30天改为5天）
        break
      case 3: // 15分钟
        timespan = 'minute'
        multiplier = 15
        from = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 60天前
        break
      case 4: // 30分钟
        timespan = 'minute'
        multiplier = 30
        from = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 90天前
        break
      case 5: // 60分钟
        timespan = 'hour'
        multiplier = 1
        from = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 180天前
        break
      case 6: // 周线
        timespan = 'week'
        multiplier = 1
        from = new Date(now.getTime() - 2 * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 2年前
        break
      case 7: // 月线
        timespan = 'month'
        multiplier = 1
        from = new Date(now.getTime() - 5 * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 5年前
        break
      default:
        timespan = 'day'
        multiplier = 1
        from = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    }

    return { timespan, multiplier, from, to }
  }
}

export default KLineService
