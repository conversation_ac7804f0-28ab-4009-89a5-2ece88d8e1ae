/**
 * K线图专用WebSocket服务
 * 基于现有的realTimeWebSocket，提供更好的K线数据支持
 */

import { subscribeRealTime, getLatestPrice } from './realTimeWebSocket'
import AggregatesService from './aggregatesService'
import { Toast } from 'mint-ui'

class KLineWebSocketService {
  constructor() {
    this.subscriptions = new Map() // symbol -> { callbacks: [], lastData: {} }
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.pollingIntervals = new Map() // symbol -> intervalId
  }

  /**
   * 订阅股票的实时K线数据
   * @param {string} symbol - 股票代码
   * @param {Function} callback - 数据回调函数
   * @returns {Function} 取消订阅函数
   */
  async subscribe(symbol, callback) {
    try {
      console.log('📡 [KLineWS] 订阅K线实时数据:', symbol)

      // 初始化订阅记录
      if (!this.subscriptions.has(symbol)) {
        this.subscriptions.set(symbol, {
          callbacks: [],
          lastData: null,
          lastUpdate: 0
        })
      }

      const subscription = this.subscriptions.get(symbol)
      subscription.callbacks.push(callback)

      // 尝试WebSocket订阅
      try {
        const unsubscribe = await subscribeRealTime(symbol, (data) => {
          this.handleRealtimeUpdate(symbol, data)
        })

        this.isConnected = true
        console.log('✅ [KLineWS] WebSocket订阅成功:', symbol)

        // 立即获取一次最新数据
        this.fetchLatestData(symbol)

        // 返回取消订阅函数
        return () => {
          this.unsubscribe(symbol, callback)
          if (unsubscribe) unsubscribe()
        }
      } catch (error) {
        console.warn('⚠️ [KLineWS] WebSocket订阅失败，使用轮询模式:', error.message)
        this.startPolling(symbol)

        return () => {
          this.unsubscribe(symbol, callback)
        }
      }
    } catch (error) {
      console.error('❌ [KLineWS] 订阅失败:', error)
      throw error
    }
  }

  /**
   * 取消订阅
   * @param {string} symbol - 股票代码
   * @param {Function} callback - 要移除的回调函数
   */
  unsubscribe(symbol, callback) {
    const subscription = this.subscriptions.get(symbol)
    if (!subscription) return

    // 移除回调函数
    const index = subscription.callbacks.indexOf(callback)
    if (index > -1) {
      subscription.callbacks.splice(index, 1)
    }

    // 如果没有其他订阅者，清理资源
    if (subscription.callbacks.length === 0) {
      this.subscriptions.delete(symbol)
      this.stopPolling(symbol)
      console.log('🗑️ [KLineWS] 清理订阅:', symbol)
    }
  }

  /**
   * 处理实时数据更新
   * @param {string} symbol - 股票代码
   * @param {Object} data - 实时数据
   */
  handleRealtimeUpdate(symbol, data) {
    try {
      const subscription = this.subscriptions.get(symbol)
      if (!subscription) return

      // 数据去重：避免重复推送相同数据
      const now = Date.now()
      if (subscription.lastUpdate && now - subscription.lastUpdate < 1000) {
        // 1秒内重复数据，跳过
        return
      }

      // 格式化K线数据
      const klineData = this.formatRealtimeData(data)
      if (!klineData) return

      subscription.lastData = klineData
      subscription.lastUpdate = now

      // 通知所有订阅者
      subscription.callbacks.forEach(callback => {
        try {
          callback(klineData)
        } catch (error) {
          console.error('❌ [KLineWS] 回调函数执行失败:', error)
        }
      })

      console.log('📈 [KLineWS] 推送实时数据:', symbol, klineData.price)
    } catch (error) {
      console.error('❌ [KLineWS] 处理实时数据失败:', error)
    }
  }

  /**
   * 格式化实时数据为K线格式
   * @param {Object} data - 原始实时数据
   * @returns {Object} K线数据格式
   */
  formatRealtimeData(data) {
    try {
      if (!data || !data.data) return null

      const priceData = data.data
      const price = parseFloat(priceData.price || 0)

      if (price <= 0) return null

      return {
        timestamp: Date.now(),
        open: price, // 实时数据中没有开盘价，用当前价格
        high: price,
        low: price,
        close: price,
        volume: parseInt(priceData.volume || 0),
        price: price,
        change: parseFloat(priceData.change || 0),
        changePercent: parseFloat(priceData.changePercent || 0)
      }
    } catch (error) {
      console.error('❌ [KLineWS] 格式化实时数据失败:', error)
      return null
    }
  }

  /**
   * 获取最新数据
   * @param {string} symbol - 股票代码
   */
  async fetchLatestData(symbol) {
    try {
      const priceData = await getLatestPrice(symbol)
      if (priceData) {
        const mockRealtimeData = {
          data: priceData
        }
        this.handleRealtimeUpdate(symbol, mockRealtimeData)
      }
    } catch (error) {
      console.error('❌ [KLineWS] 获取最新数据失败:', error)
    }
  }

  /**
   * 开始轮询模式
   * @param {string} symbol - 股票代码
   */
  startPolling(symbol) {
    // 清理旧的轮询
    this.stopPolling(symbol)

    console.log('🔄 [KLineWS] 开始轮询模式:', symbol)

    const intervalId = setInterval(async () => {
      await this.fetchLatestData(symbol)
    }, 5000) // 每5秒轮询一次

    this.pollingIntervals.set(symbol, intervalId)

    // 立即获取一次数据
    this.fetchLatestData(symbol)
  }

  /**
   * 停止轮询模式
   * @param {string} symbol - 股票代码
   */
  stopPolling(symbol) {
    const intervalId = this.pollingIntervals.get(symbol)
    if (intervalId) {
      clearInterval(intervalId)
      this.pollingIntervals.delete(symbol)
      console.log('⏹️ [KLineWS] 停止轮询:', symbol)
    }
  }

  /**
   * 获取历史K线数据和实时数据的混合数据
   * @param {string} symbol - 股票代码
   * @param {Object} options - 请求选项
   * @returns {Promise<Array>} K线数据数组
   */
  async getKLineDataWithRealtime(symbol, options = {}) {
    try {
      console.log('📊 [KLineWS] 获取混合K线数据:', symbol, options)

      // 获取历史数据
      const historicalData = await AggregatesService.getAggregates({
        ticker: symbol,
        multiplier: options.multiplier || 1,
        timespan: options.timespan || 'day',
        from: options.from,
        to: options.to,
        adjusted: true,
        sort: 'asc',
        limit: options.limit || 5000
      })

      let klineData = []

      if (historicalData && historicalData.data) {
        // 转换历史数据格式
        klineData = historicalData.data.map(item => ({
          timestamp: new Date(item.date).getTime(),
          open: parseFloat(item.open),
          high: parseFloat(item.high),
          low: parseFloat(item.low),
          close: parseFloat(item.close),
          volume: parseInt(item.volume)
        }))
      }

      // 如果有实时数据，合并或更新最后一条数据
      const subscription = this.subscriptions.get(symbol)
      if (subscription && subscription.lastData) {
        const realtimeData = subscription.lastData
        const lastHistorical = klineData[klineData.length - 1]

        if (lastHistorical) {
          // 检查实时数据是否是同一天/周期的数据
          const historicalDate = new Date(lastHistorical.timestamp)
          const realtimeDate = new Date(realtimeData.timestamp)

          const isSamePeriod = this.isSamePeriod(
            historicalDate,
            realtimeDate,
            options.timespan || 'day'
          )

          if (isSamePeriod) {
            // 更新最后一条数据
            lastHistorical.close = realtimeData.price
            lastHistorical.high = Math.max(lastHistorical.high, realtimeData.price)
            lastHistorical.low = Math.min(lastHistorical.low, realtimeData.price)
            lastHistorical.volume += realtimeData.volume
          } else {
            // 添加新的实时数据点
            klineData.push({
              timestamp: realtimeData.timestamp,
              open: realtimeData.price,
              high: realtimeData.price,
              low: realtimeData.price,
              close: realtimeData.price,
              volume: realtimeData.volume
            })
          }
        }
      }

      console.log('✅ [KLineWS] 混合数据获取完成:', klineData.length, '条')
      return klineData

    } catch (error) {
      console.error('❌ [KLineWS] 获取混合K线数据失败:', error)
      throw error
    }
  }

  /**
   * 判断两个时间是否在同一个周期内
   * @param {Date} date1 - 时间1
   * @param {Date} date2 - 时间2
   * @param {string} timespan - 时间单位
   * @returns {boolean} 是否同一周期
   */
  isSamePeriod(date1, date2, timespan) {
    switch (timespan) {
      case 'minute':
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate() &&
               date1.getHours() === date2.getHours() &&
               date1.getMinutes() === date2.getMinutes()

      case 'hour':
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate() &&
               date1.getHours() === date2.getHours()

      case 'day':
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate()

      case 'week':
        const week1 = Math.floor(date1.getTime() / (7 * 24 * 60 * 60 * 1000))
        const week2 = Math.floor(date2.getTime() / (7 * 24 * 60 * 60 * 1000))
        return week1 === week2

      case 'month':
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth()

      default:
        return false
    }
  }

  /**
   * 清理所有订阅和资源
   */
  cleanup() {
    console.log('🧹 [KLineWS] 清理所有资源')

    // 清理所有轮询
    this.pollingIntervals.forEach((intervalId) => {
      clearInterval(intervalId)
    })
    this.pollingIntervals.clear()

    // 清理订阅
    this.subscriptions.clear()

    this.isConnected = false
  }
}

// 创建单例实例
const klineWebSocketService = new KLineWebSocketService()

export default klineWebSocketService

// 导出便捷方法
export const subscribeKLineRealtime = (symbol, callback) => {
  return klineWebSocketService.subscribe(symbol, callback)
}

export const getKLineDataWithRealtime = (symbol, options) => {
  return klineWebSocketService.getKLineDataWithRealtime(symbol, options)
}

export const cleanup = () => {
  klineWebSocketService.cleanup()
}
