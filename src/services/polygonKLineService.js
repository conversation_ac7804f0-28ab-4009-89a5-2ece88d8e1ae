/**
 * Polygon K线图专用WebSocket服务
 * 基于 polygonWebSocket 服务，专门处理K线图的实时数据更新
 */

import {
  connectPolygonWS,
  disconnectPolygonWS,
  subscribePolygonRealTime,
  unsubscribePolygonRealTime,
  getPolygonWSStatus
} from './polygonWebSocket'
import AggregatesService from './aggregatesService'

class PolygonKLineService {
  constructor() {
    this.subscriptions = new Map() // symbol -> { callbacks: [], lastData: {}, currentBar: {} }
    this.isConnected = false
    this.userId = null
  }

  /**
   * 连接WebSocket服务
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 连接是否成功
   */
  async connect(userId = null) {
    try {

      this.userId = userId || `kline_user_${Date.now()}`
      const connected = await connectPolygonWS(this.userId)

      if (connected) {
        this.isConnected = true
      }

      return connected
    } catch (error) {
      console.error('❌ [PolygonKLine] 连接失败:', error)
      return false
    }
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {

    // 清理所有订阅
    this.subscriptions.clear()
    this.isConnected = false

    // 断开底层WebSocket连接
    disconnectPolygonWS()
  }

  /**
   * 订阅股票的实时K线数据
   * @param {string} symbol - 股票代码
   * @param {Function} callback - 数据回调函数
   * @param {Object} options - 订阅选项
   * @returns {Function} 取消订阅函数
   */
  async subscribe(symbol, callback, options = {}) {
    try {

      // 确保连接已建立
      if (!this.isConnected) {
        const connected = await this.connect()
        if (!connected) {
          throw new Error('无法建立WebSocket连接')
        }
      }

      // 初始化订阅记录
      if (!this.subscriptions.has(symbol)) {
        this.subscriptions.set(symbol, {
          callbacks: [],
          lastData: null,
          currentBar: null,
          period: options.period || 'day'
        })
      }

      const subscription = this.subscriptions.get(symbol)
      subscription.callbacks.push(callback)

      // 如果是第一个订阅者，开始订阅实时数据
      if (subscription.callbacks.length === 1) {
        const unsubscribeFn = await subscribePolygonRealTime(symbol, (data) => {
          this.handleRealtimeUpdate(symbol, data)
        })

        subscription.unsubscribe = unsubscribeFn
      }

      // 返回取消订阅函数
      return () => {
        this.unsubscribe(symbol, callback)
      }
    } catch (error) {
      console.error('❌ [PolygonKLine] 订阅失败:', error)
      throw error
    }
  }

  /**
   * 取消订阅股票实时数据
   * @param {string} symbol - 股票代码
   * @param {Function} callback - 要移除的回调函数
   */
  unsubscribe(symbol, callback) {
    const subscription = this.subscriptions.get(symbol)
    if (!subscription) return

    // 移除回调函数
    const index = subscription.callbacks.indexOf(callback)
    if (index > -1) {
      subscription.callbacks.splice(index, 1)
    }

    // 如果没有其他订阅者，取消订阅
    if (subscription.callbacks.length === 0) {
      if (subscription.unsubscribe) {
        subscription.unsubscribe()
      }
      this.subscriptions.delete(symbol)
    }
  }

  /**
   * 处理实时数据更新
   * @param {string} symbol - 股票代码
   * @param {Object} data - 实时价格数据
   */
  handleRealtimeUpdate(symbol, data) {
    try {
      const subscription = this.subscriptions.get(symbol)
      if (!subscription) return

      // 将实时价格数据转换为K线格式
      const klineData = this.convertToKLineData(data, subscription.period)
      if (!klineData) return

      // 更新当前K线柱
      const updatedBar = this.updateCurrentBar(subscription, klineData)
      if (!updatedBar) return

      subscription.lastData = updatedBar

      // 通知所有订阅者
      subscription.callbacks.forEach(callback => {
        try {
          callback(updatedBar)
        } catch (error) {
          console.error('❌ [PolygonKLine] 回调函数执行失败:', error)
        }
      })
    } catch (error) {
      console.error('❌ [PolygonKLine] 处理实时数据失败:', error)
    }
  }

  /**
   * 将实时价格数据转换为K线数据格式
   * @param {Object} data - 实时价格数据
   * @param {string} period - K线周期
   * @returns {Object} K线数据格式
   */
  convertToKLineData(data, period = 'day') {
    try {
      if (!data || !data.price) {
        return null
      }

      const price = parseFloat(data.price)
      const timestamp = data.timestamp || Date.now()
      const volume = parseInt(data.size || 0)

      // 对于实时数据，我们不需要标准化时间戳，保留原始精度
      return {
        timestamp: timestamp,
        price: price,
        volume: volume,
        period: period
      }
    } catch (error) {
      console.error('❌ [PolygonKLine] 转换K线数据失败:', error)
      return null
    }
  }

  /**
   * 根据周期标准化时间戳
   * @param {number} timestamp - 原始时间戳
   * @param {string} period - K线周期
   * @returns {number} 标准化的时间戳
   */
  normalizeTimestamp(timestamp, period) {
    const date = new Date(timestamp)

    switch (period) {
      case '1min':
      case '5min':
      case '15min':
      case '30min':
        // 分钟级：保留到分钟精度，只清零秒和毫秒
        date.setSeconds(0, 0)
        break

      case '1hour':
      case '2hour':
      case '4hour':
      case '12hour':
        // 小时级：保留到小时精度，只清零分钟、秒和毫秒
        date.setMinutes(0, 0, 0)
        break

      case 'day':
      case 'week':
      case 'month':
        // 日级及以上：保留到日精度，但仍然保持一些时间信息以便图表显示
        // 对于股票数据，可以设置为交易开始时间（例如9:30）
        date.setHours(9, 30, 0, 0)
        break

      default:
        // 对于未知周期，保留原始精度，只清零毫秒
        date.setMilliseconds(0)
        break
    }

    return date.getTime()
  }

  /**
   * 更新当前K线柱
   * @param {Object} subscription - 订阅信息
   * @param {Object} newData - 新的价格数据
   * @returns {Object} 更新后的K线柱数据
   */
  updateCurrentBar(subscription, newData) {
    try {
      const currentBar = subscription.currentBar
      const timestamp = newData.timestamp
      const price = newData.price
      const volume = newData.volume

      // 如果是新的时间周期，创建新的K线柱
      if (!currentBar || currentBar.timestamp !== timestamp) {
        const newBar = {
          timestamp: timestamp,
          open: price,
          high: price,
          low: price,
          close: price,
          volume: volume
        }

        subscription.currentBar = newBar
        return newBar
      }

      // 更新现有K线柱
      currentBar.close = price
      currentBar.high = Math.max(currentBar.high, price)
      currentBar.low = Math.min(currentBar.low, price)
      currentBar.volume += volume

      return { ...currentBar } // 返回副本以避免引用问题
    } catch (error) {
      console.error('❌ [PolygonKLine] 更新K线柱失败:', error)
      return null
    }
  }

  /**
   * 获取历史K线数据和实时数据的混合数据
   * @param {string} symbol - 股票代码
   * @param {Object} options - 请求选项
   * @returns {Promise<Array>} K线数据数组
   */
  async getKLineDataWithRealtime(symbol, options = {}) {
    try {
      console.log('📊 [PolygonKLine] 获取混合K线数据:', symbol, options)

      // 获取历史数据
      const historicalData = await AggregatesService.getAggregates({
        ticker: symbol,
        multiplier: options.multiplier || 1,
        timespan: options.timespan || 'day',
        from: options.from,
        to: options.to,
        adjusted: true,
        sort: 'asc',
        limit: options.limit || 5000
      })

      let klineData = []

      if (historicalData && historicalData.data) {
        // 转换历史数据格式
        klineData = historicalData.data.map(item => ({
          timestamp: item.timestamp || new Date(item.date).getTime(), // 优先使用原始时间戳
          open: parseFloat(item.open),
          high: parseFloat(item.high),
          low: parseFloat(item.low),
          close: parseFloat(item.close),
          volume: parseInt(item.volume)
        }))
      }

      // 如果有实时数据，合并最后一条数据
      const subscription = this.subscriptions.get(symbol)
      if (subscription && subscription.lastData) {
        const realtimeBar = subscription.lastData
        const lastHistorical = klineData[klineData.length - 1]

        if (lastHistorical) {
          // 检查是否是同一个时间周期
          const isSamePeriod = this.isSameTimePeriod(
            lastHistorical.timestamp,
            realtimeBar.timestamp,
            options.timespan || 'day'
          )

          if (isSamePeriod) {
            // 更新最后一条历史数据
            lastHistorical.close = realtimeBar.close
            lastHistorical.high = Math.max(lastHistorical.high, realtimeBar.high)
            lastHistorical.low = Math.min(lastHistorical.low, realtimeBar.low)
            lastHistorical.volume += realtimeBar.volume
          } else {
            // 添加新的实时数据点
            klineData.push(realtimeBar)
          }
        } else {
          // 没有历史数据，直接添加实时数据
          klineData.push(realtimeBar)
        }
      }
      return klineData

    } catch (error) {
      console.error('❌ [PolygonKLine] 获取混合K线数据失败:', error)
      throw error
    }
  }

  /**
   * 判断两个时间戳是否在同一个时间周期内
   * @param {number} timestamp1 - 时间戳1
   * @param {number} timestamp2 - 时间戳2
   * @param {string} timespan - 时间单位
   * @returns {boolean} 是否同一周期
   */
  isSameTimePeriod(timestamp1, timestamp2, timespan) {
    const date1 = new Date(timestamp1)
    const date2 = new Date(timestamp2)

    switch (timespan) {
      case 'minute':
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate() &&
               date1.getHours() === date2.getHours() &&
               date1.getMinutes() === date2.getMinutes()

      case 'hour':
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate() &&
               date1.getHours() === date2.getHours()

      case 'day':
      default:
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate()
    }
  }

  /**
   * 获取服务状态
   * @returns {Object} 服务状态信息
   */
  getStatus() {
    const polygonStatus = getPolygonWSStatus()

    return {
      ...polygonStatus,
      klineSubscriptions: Array.from(this.subscriptions.keys()),
      subscriptionCount: this.subscriptions.size
    }
  }

  /**
   * 清理所有资源
   */
  cleanup() {
    // 取消所有订阅
    this.subscriptions.forEach((subscription, symbol) => {
      if (subscription.unsubscribe) {
        subscription.unsubscribe()
      }
    })

    this.subscriptions.clear()
    this.isConnected = false
  }
}

// 创建单例实例
const polygonKLineService = new PolygonKLineService()

export default polygonKLineService

// 导出便捷方法
export const connectPolygonKLine = (userId) => {
  return polygonKLineService.connect(userId)
}

export const disconnectPolygonKLine = () => {
  return polygonKLineService.disconnect()
}

export const subscribePolygonKLine = (symbol, callback, options) => {
  return polygonKLineService.subscribe(symbol, callback, options)
}

export const unsubscribePolygonKLine = (symbol, callback) => {
  return polygonKLineService.unsubscribe(symbol, callback)
}

export const getPolygonKLineData = (symbol, options) => {
  return polygonKLineService.getKLineDataWithRealtime(symbol, options)
}

export const getPolygonKLineStatus = () => {
  return polygonKLineService.getStatus()
}

export const cleanupPolygonKLine = () => {
  return polygonKLineService.cleanup()
}
