/**
 * Polygon WebSocket Service
 * 基于新的 wss://api.coin-tx.exchange/ws/polygon 接口实现
 * 用于股票实时行情数据推送
 */

import store from '@/store'

class PolygonWebSocket {
  constructor() {
    this.socket = null
    this.isConnected = false
    this.isConnecting = false
    this.userId = null
    this.subscriptions = new Set() // 已订阅的股票代码
    this.callbacks = new Map() // symbol -> callback[]
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
    this.heartbeatTimer = null
    this.heartbeatInterval = 30000 // 30秒心跳间隔
  }

  /**
   * 连接WebSocket
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 连接是否成功
   */
  async connect(userId = null) {
    if (this.isConnected || this.isConnecting) {
      return true
    }

    try {
      this.isConnecting = true
      this.userId = userId || store.getters.userId || `user_${Date.now()}`

      const wsUrl = `wss://api.coin-tx.exchange/ws/polygon?userId=${this.userId}`

      this.socket = new WebSocket(wsUrl)

      return new Promise((resolve) => {
        // 连接成功
        this.socket.onopen = () => {
          this.isConnected = true
          this.isConnecting = false
          this.reconnectAttempts = 0
          this.startHeartbeat()
          resolve(true)
        }

        // 接收消息
        this.socket.onmessage = (event) => {
          this.handleMessage(event.data)
        }

        // 连接关闭
        this.socket.onclose = (event) => {
          this.isConnected = false
          this.isConnecting = false
          this.stopHeartbeat()

          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000) {
            this.scheduleReconnect()
          }
        }

        // 连接错误
        this.socket.onerror = (error) => {
          this.isConnected = false
          this.isConnecting = false
          this.stopHeartbeat()
          resolve(false)
        }

        // 连接超时
        setTimeout(() => {
          if (this.isConnecting) {
            this.socket.close()
            this.isConnecting = false
            resolve(false)
          }
        }, 10000) // 10秒超时
      })
    } catch (error) {
      this.isConnecting = false
      return false
    }
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {

    this.stopHeartbeat()

    if (this.socket) {
      this.socket.close(1000, '主动断开连接')
      this.socket = null
    }

    this.isConnected = false
    this.isConnecting = false
    this.subscriptions.clear()
    this.callbacks.clear()
  }

  /**
   * 订阅股票实时数据
   * @param {string|Array} symbols - 股票代码或代码数组
   * @param {Function} callback - 数据回调函数
   * @returns {Function} 取消订阅函数
   */
  async subscribe(symbols, callback) {
    const symbolArray = Array.isArray(symbols) ? symbols : [symbols]
    const symbolsStr = symbolArray.join(',')
    // 保存回调函数
    symbolArray.forEach(symbol => {
      if (!this.callbacks.has(symbol)) {
        this.callbacks.set(symbol, [])
      }
      this.callbacks.get(symbol).push(callback)
    })

    // 如果未连接，先连接
    if (!this.isConnected) {
      const connected = await this.connect()
      if (!connected) {
        throw new Error('WebSocket连接失败')
      }
    }

    // 发送订阅消息
    const subscribeMessage = {
      action: 'subscribe',
      symbols: symbolsStr
    }

    this.sendMessage(subscribeMessage)

    // 添加到订阅列表
    symbolArray.forEach(symbol => {
      this.subscriptions.add(symbol)
    })

    // 等待一下，然后查询订阅状态
    setTimeout(() => {
      this.querySubscriptions()
    }, 2000)

    // 返回取消订阅函数
    return () => {
      this.unsubscribe(symbolArray, callback)
    }
  }

  /**
   * 取消订阅股票实时数据
   * @param {string|Array} symbols - 股票代码或代码数组
   * @param {Function} callback - 要移除的回调函数
   */
  unsubscribe(symbols, callback = null) {
    const symbolArray = Array.isArray(symbols) ? symbols : [symbols]
    const symbolsStr = symbolArray.join(',')

    // 移除回调函数
    symbolArray.forEach(symbol => {
      if (this.callbacks.has(symbol)) {
        const callbacks = this.callbacks.get(symbol)

        if (callback) {
          const index = callbacks.indexOf(callback)
          if (index > -1) {
            callbacks.splice(index, 1)
          }
        } else {
          callbacks.length = 0 // 清空所有回调
        }

        // 如果没有回调了，从订阅列表移除
        if (callbacks.length === 0) {
          this.subscriptions.delete(symbol)
          this.callbacks.delete(symbol)
        }
      }
    })

    // 发送取消订阅消息
    if (this.isConnected) {
      const unsubscribeMessage = {
        action: 'unsubscribe',
        symbols: symbolsStr
      }
      this.sendMessage(unsubscribeMessage)
    }
  }

  /**
   * 获取当前已订阅的股票列表
   * @returns {Promise<Array>} 订阅的股票代码数组
   */
  async getSubscriptions() {
    if (!this.isConnected) {
      return Array.from(this.subscriptions)
    }

    return new Promise((resolve) => {
      const requestId = Date.now()

      // 临时监听响应
      const tempCallback = (data) => {
        if (data.type === 'subscriptions' && data.requestId === requestId) {
          this.socket.removeEventListener('message', tempCallback)
          resolve(data.data.subscriptions || [])
        }
      }

      this.socket.addEventListener('message', tempCallback)

      // 发送查询消息
      this.sendMessage({
        action: 'getSubscriptions',
        requestId: requestId
      })

      // 5秒超时
      setTimeout(() => {
        this.socket.removeEventListener('message', tempCallback)
        resolve(Array.from(this.subscriptions))
      }, 5000)
    })
  }

  /**
   * 查询当前订阅状态
   */
  querySubscriptions() {
    this.sendMessage({
      action: 'getSubscriptions'
    })
  }

  /**
   * 发送消息到WebSocket服务器
   * @param {Object} message - 要发送的消息对象
   */
  sendMessage(message) {
    if (this.socket && this.isConnected) {
      try {
        const jsonMessage = JSON.stringify(message)
        this.socket.send(jsonMessage)
      } catch (error) {
        console.error('❌ [PolygonWS] 发送消息失败:', error)
      }
    } else {
      console.warn('⚠️ [PolygonWS] WebSocket未连接，无法发送消息')
    }
  }

  /**
   * 处理接收到的消息
   * @param {string} messageData - 消息数据
   */
  handleMessage(messageData) {
    try {
      const data = JSON.parse(messageData)

      switch (data.type) {
        case 'welcome':
          break

        case 'price_update':
          this.handlePriceUpdate(data)
          break

        case 'subscribe_response':
          if (data.status === 'success') {
            console.log('✅ [PolygonWS] 订阅成功:', data.symbols)
          } else {
            console.error('❌ [PolygonWS] 订阅失败:', data.message)
          }
          break

        case 'unsubscribe_response':
          if (data.status === 'success') {
            console.log('✅ [PolygonWS] 取消订阅成功:', data.symbols)
          } else {
            console.error('❌ [PolygonWS] 取消订阅失败:', data.message)
          }
          break

        case 'subscriptions':
          console.log('📋 [PolygonWS] 当前订阅列表:', data.data.subscriptions)
          break

        case 'pong':
          console.log('💓 [PolygonWS] 心跳响应，服务器时间:', new Date(data.serverTime))
          break

        case 'error':
          console.error('❌ [PolygonWS] 服务器错误:', data.message)
          break

        default:
          console.warn('⚠️ [PolygonWS] 未知消息类型:', data.type)
      }
    } catch (error) {
      console.error('❌ [PolygonWS] 解析消息失败:', error, messageData)
    }
  }

  /**
   * 处理价格更新消息
   * @param {Object} data - 价格更新数据
   */
  handlePriceUpdate(data) {
    const symbol = data.symbol
    const priceInfo = data.data

    if (!symbol || !priceInfo) {
      console.warn('⚠️ [PolygonWS] 价格更新数据格式错误:', data)
      return
    }

    // 格式化价格数据
    const formattedData = {
      symbol: symbol,
      price: parseFloat(priceInfo.price),
      size: parseInt(priceInfo.size || 0),
      timestamp: priceInfo.timestamp,
      type: 'price_update'
    }

    // 调用所有订阅该股票的回调函数
    if (this.callbacks.has(symbol)) {
      const callbackCount = this.callbacks.get(symbol).length

      this.callbacks.get(symbol).forEach((callback, index) => {
        try {
          callback(formattedData)
        } catch (error) {
          console.error(`❌ [PolygonWS] 回调函数${index + 1}执行失败:`, error)
        }
      })
    } else {
      console.warn(`⚠️ [PolygonWS] 没有找到股票${symbol}的回调函数`)
    }

  }

  /**
   * 启动心跳检测
   */
  startHeartbeat() {
    this.stopHeartbeat() // 先清理旧的定时器

    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.sendMessage({ action: 'ping' })
      }
    }, this.heartbeatInterval)
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ [PolygonWS] 重连次数超过限制，停止重连')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1) // 指数退避

    setTimeout(async () => {
      console.log(`🔄 [PolygonWS] 开始第${this.reconnectAttempts}次重连`)

      const connected = await this.connect(this.userId)

      if (connected) {
        // 重新订阅之前的股票
        const symbols = Array.from(this.subscriptions)
        if (symbols.length > 0) {

          const subscribeMessage = {
            action: 'subscribe',
            symbols: symbols.join(',')
          }
          this.sendMessage(subscribeMessage)
        }
      } else {
        // 重连失败，继续尝试
        this.scheduleReconnect()
      }
    }, delay)
  }

  /**
   * 获取连接状态
   * @returns {Object} 连接状态信息
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      userId: this.userId,
      subscriptions: Array.from(this.subscriptions),
      reconnectAttempts: this.reconnectAttempts
    }
  }
}

// 创建全局实例
const polygonWS = new PolygonWebSocket()

export default polygonWS

// 导出便捷方法
export function connectPolygonWS(userId) {
  return polygonWS.connect(userId)
}

export function disconnectPolygonWS() {
  return polygonWS.disconnect()
}

export function subscribePolygonRealTime(symbols, callback) {
  return polygonWS.subscribe(symbols, callback)
}

export function unsubscribePolygonRealTime(symbols, callback) {
  return polygonWS.unsubscribe(symbols, callback)
}

export function getPolygonSubscriptions() {
  return polygonWS.getSubscriptions()
}

export function getPolygonWSStatus() {
  return polygonWS.getStatus()
}
