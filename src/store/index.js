import Vue from 'vue'
import Vuex from 'vuex'
// import getters from './getters'
// import actions from './actions'
// import mutations from './mutations'

Vue.use(Vuex)

let state = {
  className: 'black',
  theme: 'red',
  userInfo: { // User information
  },
  user: {},
  bankInfo: {
    bankNo: ''
  },
  hide: false,
  select: '/home', // Menu selection
  token: localStorage.getItem('USERTOKEN') || '', // Get token from localStorage
  isLoggedIn: !!localStorage.getItem('USERTOKEN'), // Login status
  elAlertShow: false,
  setAvatar: '',
  elAlertText: '',
  dialogVisible: false,
  elAlertType: 'warning',
  settingForm: { // Product configuration
    futuresDisplay: false,
    indexDisplay: false,
    kcStockDisplay: false,
    stockDisplay: false
  }
}

export default new Vuex.Store({
  state,
  actions: {
    // Set token after successful login
    setToken ({ commit }, token) {
      commit('SET_TOKEN', token)
    },

    // Clear token on logout
    logout ({ commit }) {
      commit('CLEAR_TOKEN')
    },

    // 设置用户信息
    setUserInfo ({ commit }, userInfo) {
      commit('SET_USER_INFO', userInfo)
    }
  },
  mutations: {
    // Set token
    SET_TOKEN (state, token) {
      state.token = token
      state.isLoggedIn = true
      localStorage.setItem('USERTOKEN', token)
    },

    // Clear token
    CLEAR_TOKEN (state) {
      state.token = ''
      state.isLoggedIn = false
      state.userInfo = {}
      localStorage.removeItem('USERTOKEN')
      localStorage.removeItem('phone')
    },

    // 设置用户信息
    SET_USER_INFO (state, userInfo) {
      state.userInfo = userInfo
    },

    elAlertShow (state, payload) {
      state.elAlertShow = payload.elAlertShow
      state.elAlertText = payload.elAlertText
      if (payload.elAlertType) {
        state.elAlertType = payload.elAlertType
      }
    },
    dialogVisible (state, payload) {
      state.dialogVisible = payload
    }
  },
  getters: {
    // 获取 token
    token: state => state.token,

    // 获取登录状态
    isLoggedIn: state => state.isLoggedIn,

    // 获取用户信息
    userInfo: state => state.userInfo
  }
})
