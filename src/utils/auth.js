/**
 * 认证相关工具函数
 */
import store from '@/store'
import router from '@/router'
import { Toast } from 'mint-ui'

/**
 * 清理URL中的递归重定向参数
 * @param {string} url - 需要清理的URL
 * @returns {string} - 清理后的URL
 */
function cleanRedirectUrl (url) {
  if (!url || typeof url !== 'string') {
    return '/watchlist'
  }

  try {
    // 多次解码URL，直到不能再解码为止
    let cleanUrl = url
    let previousUrl = ''

    while (cleanUrl !== previousUrl && cleanUrl.includes('%')) {
      previousUrl = cleanUrl
      cleanUrl = decodeURIComponent(cleanUrl)
    }

    // 移除登录页面路径
    if (cleanUrl.includes('/futu-login') || cleanUrl.includes('/login')) {
      return '/watchlist'
    }

    // 移除重定向参数
    if (cleanUrl.includes('redirect=')) {
      const urlObj = new URL(cleanUrl, window.location.origin)
      urlObj.searchParams.delete('redirect')
      cleanUrl = urlObj.pathname + urlObj.search
    }

    // 确保返回有效路径
    return cleanUrl.startsWith('/') ? cleanUrl : '/watchlist'
  } catch (error) {
    return '/watchlist'
  }
}

/**
 * 验证登录响应格式
 * @param {Object} response - API响应
 * @returns {boolean}
 */
function validateLoginResponse (response) {
  // 检查基本结构
  if (!response || typeof response !== 'object') {
    return false
  }

  // 检查必需字段
  if (typeof response.status !== 'number' ||
      typeof response.success !== 'boolean' ||
      !response.data ||
      typeof response.data !== 'object') {
    return false
  }

  // 检查成功状态
  if (response.status !== 0 || !response.success) {
    return false
  }

  // 检查 token
  if (!response.data.token || typeof response.data.token !== 'string') {
    return false
  }

  return true
}

/**
 * 获取登录失败的详细错误信息
 * @param {Object} response - API响应
 * @returns {string}
 */
function getLoginErrorMessage (response) {
  if (!response) {
    return '网络请求失败，请检查网络连接'
  }

  if (response.msg) {
    return response.msg
  }

  if (response.status !== 0) {
    return `登录失败，状态码: ${response.status}`
  }

  if (!response.success) {
    return '登录失败，服务器返回失败状态'
  }

  if (!response.data) {
    return '登录失败，响应数据格式错误'
  }

  if (!response.data.token) {
    return '登录失败，未获取到访问令牌'
  }

  return '登录失败，未知错误'
}

/**
 * 处理登录成功
 * @param {Object} response - 登录接口返回的响应数据
 * @param {string} redirectPath - 登录成功后的重定向路径
 */
export function handleLoginSuccess (response, redirectPath = '/watchlist') {
  try {
    // 使用验证函数检查响应格式
    if (!validateLoginResponse(response)) {
      const errorMsg = getLoginErrorMessage(response)
      throw new Error(errorMsg)
    }

    // 获取 token 和用户信息
    const token = response.data.token
    // const userInfo = response.data.userInfo || response.data.user || response.data

    // 存储 token 到 store
    store.dispatch('setToken', token)

    // 存储用户信息到 store
    // if (userInfo) {
    //   store.dispatch('setUserInfo', userInfo)
    // }

    // 存储手机号（如果有）
    // if (userInfo.phone || userInfo.mobile) {
    //   localStorage.setItem('phone', userInfo.phone || userInfo.mobile)
    // }

    // 额外存储 token key（如果需要）
    if (response.data.key) {
      localStorage.setItem('TOKEN_KEY', response.data.key)
    }

    // 显示成功提示
    Toast({
      message: 'Login successful',
      position: 'top',
      duration: 1500
    })

    // 跳转到指定页面
    setTimeout(() => {
      // 获取重定向路径，并清理可能的递归重定向
      const rawRedirect = router.currentRoute.query.redirect || redirectPath
      const cleanRedirect = cleanRedirectUrl(rawRedirect)

      // 使用 replace 避免在历史记录中留下登录页面
      router.replace(cleanRedirect)
    }, 1000)

    return true
  } catch (error) {

    Toast({
      message: error.message || '登录处理失败',
      position: 'top',
      duration: 2000
    })

    return false
  }
}

/**
 * 处理登出
 */
export function handleLogout () {
  // 清除 store 中的登录状态
  store.dispatch('logout')

  // 显示提示
  Toast({
    message: '已退出登录',
    position: 'top',
    duration: 1500
  })

  // 跳转到登录页面
  router.replace('/futu-login')
}

/**
 * 检查是否已登录
 * @returns {boolean}
 */
export function isLoggedIn () {
  return store.getters.isLoggedIn && !!store.getters.token
}

/**
 * 获取当前用户 token
 * @returns {string}
 */
export function getToken () {
  return store.getters.token
}

/**
 * 获取当前用户信息
 * @returns {Object}
 */
export function getUserInfo () {
  return store.getters.userInfo
}

/**
 * 需要登录的页面路由守卫
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 * @param {Function} next - 下一步函数
 */
export function requireAuth (to, from, next) {
  if (isLoggedIn()) {
    next()
  } else {
    Toast({
      message: '请先登录',
      position: 'top',
      duration: 2000
    })

    // 避免递归重定向：如果目标路径已经是登录页面，直接跳转
    if (to.path.includes('/futu-login')) {
      next()
    } else {
      // 清理目标路径，确保不包含已有的重定向参数
      const cleanPath = cleanRedirectUrl(to.fullPath)

      next({
        path: '/futu-login',
        query: { redirect: cleanPath !== '/watchlist' ? cleanPath : undefined }
      })
    }
  }
}

/**
 * 邮箱格式验证
 * @param {string} email
 * @returns {boolean}
 */
export function validateEmail (email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 牛牛号格式验证（11位纯数字，类似手机号格式）
 * @param {string} futuId
 * @returns {boolean}
 */
export function validateFutuId (futuId) {
  const futuIdRegex = /^\d{11}$/
  return futuIdRegex.test(futuId)
}

/**
 * 登录账号验证（支持邮箱和牛牛号两种格式）
 * @param {string} account
 * @returns {boolean}
 */
export function validateLoginAccount (account) {
  return validateEmail(account) || validateFutuId(account)
}

/**
 * 测试登录响应处理（仅用于开发调试）
 * @param {Object} mockResponse - 模拟的API响应
 * @returns {boolean}
 */
export function testLoginResponse (mockResponse) {
  try {
    const isValid = validateLoginResponse(mockResponse)

    return isValid
  } catch (error) {
    return false
  }
}

/**
 * 密码强度验证
 * @param {string} password
 * @returns {Object}
 */
export function validatePassword (password) {
  const result = {
    isValid: false,
    message: '',
    strength: 0
  }

  if (!password) {
    result.message = '请输入密码'
    return result
  }

  if (password.length < 6) {
    result.message = '密码长度至少6位'
    return result
  }

  if (password.length > 20) {
    result.message = '密码长度不能超过20位'
    return result
  }

  // 计算密码强度
  let strength = 0
  if (/[a-z]/.test(password)) strength++
  if (/[A-Z]/.test(password)) strength++
  if (/[0-9]/.test(password)) strength++
  if (/[^a-zA-Z0-9]/.test(password)) strength++

  result.strength = strength
  result.isValid = true

  if (strength < 2) {
    result.message = '密码强度较弱，建议包含字母和数字'
  } else if (strength < 3) {
    result.message = '密码强度中等'
  } else {
    result.message = '密码强度较强'
  }

  return result
}

/**
 * 格式化用户信息显示
 * @param {Object} userInfo
 * @returns {Object}
 */
export function formatUserInfo (userInfo) {
  if (!userInfo) return {}

  return {
    name: userInfo.name || userInfo.username || userInfo.nickname || '用户',
    email: userInfo.email || userInfo.mail || '',
    phone: userInfo.phone || userInfo.mobile || '',
    avatar: userInfo.avatar || userInfo.headImg || '',
    id: userInfo.id || userInfo.userId || userInfo.uid || ''
  }
}
