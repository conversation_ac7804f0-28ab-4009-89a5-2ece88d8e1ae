import dayjs from 'dayjs'
import isLeapYear from 'dayjs/plugin/isLeapYear' // import plugin
import relativeTime from 'dayjs/plugin/relativeTime'
import durationPlug from 'dayjs/plugin/duration'
import 'dayjs/locale/zh-cn' // import locale

dayjs.extend(isLeapYear) // use plugin
dayjs.extend(relativeTime)
dayjs.extend(durationPlug)
dayjs.locale('zh-cn') // use locale

export const dateFormat = (date, template = 'YYYY-MM-DD') => {
  if (!date) return ''
  return dayjs(date).format(template)
}

export const dateDuration = (time, unit = 'milliseconds') => {
  const ctx = dayjs.duration(time, unit)
  const { $d: ret } = ctx
  return {
    ctx,
    ...ret
  }
}

export const dateFromNow = (date, withoutSuffix = false) => {
  return dayjs(date).fromNow(withoutSuffix)
}

/**
 * K线数据时间范围工具函数
 */

/**
 * 获取今天的日期字符串
 * @returns {string} YYYY-MM-DD格式的今天日期
 */
export const getTodayDate = () => {
  return dayjs().format('YYYY-MM-DD')
}

/**
 * 获取昨天的日期字符串
 * @returns {string} YYYY-MM-DD格式的昨天日期
 */
export const getYesterdayDate = () => {
  return dayjs().subtract(1, 'day').format('YYYY-MM-DD')
}

/**
 * 获取N天前的日期字符串
 * @param {number} days 天数
 * @returns {string} YYYY-MM-DD格式的日期
 */
export const getDaysAgoDate = (days) => {
  return dayjs().subtract(days, 'day').format('YYYY-MM-DD')
}

/**
 * 根据K线周期获取合适的时间范围
 * @param {string} timespan 时间单位 (minute, hour, day, week, month)
 * @param {number} multiplier 时间倍数
 * @returns {Object} {from, to} 时间范围
 */
export const getKlineTimeRange = (timespan, multiplier = 1) => {
  const to = getTodayDate()
  let from

  switch (timespan) {
    case 'minute':
      // 分钟级K线：获取最近几天的数据
      if (multiplier <= 5) {
        from = getDaysAgoDate(3) // 3天
      } else if (multiplier <= 15) {
        from = getDaysAgoDate(5) // 5天
      } else {
        from = getDaysAgoDate(7) // 7天
      }
      break

    case 'hour':
      // 小时级K线：获取最近一个月的数据
      from = getDaysAgoDate(30)
      break

    case 'day':
      // 日线：获取最近一年的数据
      from = getDaysAgoDate(365)
      break

    case 'week':
      // 周线：获取最近2年的数据
      from = getDaysAgoDate(730)
      break

    case 'month':
      // 月线：获取最近5年的数据
      from = getDaysAgoDate(1825)
      break

    default:
      from = getDaysAgoDate(30)
  }

  return { from, to }
}

/**
 * 获取当前交易日的开始和结束时间（美股时间）
 * @returns {Object} {from, to, date} 当前交易日的时间范围
 */
export const getTodayTradingRange = () => {
  const today = dayjs()

  return {
    from: today.hour(9).minute(30).second(0).format('YYYY-MM-DD HH:mm:ss'),
    to: today.hour(16).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss'),
    date: today.format('YYYY-MM-DD')
  }
}

/**
 * 检查当前是否在美股交易时间内
 * @returns {boolean} 是否在交易时间
 */
export const isMarketOpen = () => {
  const now = dayjs()
  const hour = now.hour()
  const minute = now.minute()
  const currentTime = hour * 60 + minute

  // 美股交易时间：9:30 AM - 4:00 PM
  const marketOpen = 9 * 60 + 30  // 9:30
  const marketClose = 16 * 60     // 16:00

  return currentTime >= marketOpen && currentTime <= marketClose
}

export default dayjs
