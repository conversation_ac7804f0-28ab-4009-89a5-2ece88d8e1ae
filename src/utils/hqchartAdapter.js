import KLineService from '@/services/klineService'
import AggregatesService from '@/services/aggregatesService'
import { getLatestPrice, subscribeRealTime } from '@/services/realTimeWebSocket'

// HQChart数据适配器
export const HQChartAdapter = {
  // 处理网络请求
  async NetworkFilter (data, callback) {
    try {
      switch (data.Name) {
        // 分时图数据
        case 'MinuteChartContainer::RequestMinuteData':
          await this.handleMinuteData(data, callback)
          break

        case 'MinuteChartContainer::RequestHistoryMinuteData':
          await this.handleHistoryMinuteData(data, callback)
          break

        // K线图数据
        case 'KLineChartContainer::RequestHistoryData':
          await this.handleKLineData(data, callback)
          break

        case 'KLineChartContainer::RequestRealtimeData':
          await this.handleRealtimeData(data, callback)
          break

        case 'KLineChartContainer::ReqeustHistoryMinuteData':
          await this.handleMinuteKLineData(data, callback)
          break

        case 'KLineChartContainer::RequestMinuteRealtimeData':
          await this.handleMinuteRealtimeData(data, callback)
          break

        default:
          callback({ error: '未实现的接口' })
      }
    } catch (error) {
      callback({ error: error.message })
    }
  },

  // 处理分时数据请求
  async handleMinuteData (data, callback) {
    try {
      const symbol = data.Request.Data.symbol

      // 获取前一日收盘价作为基准
      const previousClose = await AggregatesService.getPreviousClose({
        ticker: symbol,
        adjusted: true
      })

      // 获取当日分钟级K线数据
      const today = new Date().toISOString().split('T')[0]
      const aggregatesData = await AggregatesService.getAggregates({
        ticker: symbol,
        multiplier: 1,
        timespan: 'minute',
        from: today,
        to: today,
        adjusted: true,
        sort: 'asc',
        limit: 500
      })

      // 转换为分时图格式
      const minuteData = this.convertToMinuteFormat(aggregatesData, previousClose.previousClose)

      // 转换为HQChart格式
      const hqData = {
        code: 0,
        stock: [{
          symbol: minuteData.symbol,
          name: minuteData.name,
          minute: {
            time: minuteData.time,
            price: minuteData.price,
            vol: minuteData.volume,
            amount: minuteData.amount,
            avprice: minuteData.average
          },
          yclose: previousClose.previousClose || 145.00
        }]
      }

      callback(hqData)
    } catch (error) {

      // 降级到原有服务
      const minuteData = await KLineService.getMinuteData(symbol)

      const hqData = {
        code: 0,
        stock: [{
          symbol: minuteData.symbol,
          name: minuteData.name,
          minute: {
            time: minuteData.time,
            price: minuteData.price,
            vol: minuteData.volume,
            amount: minuteData.amount,
            avprice: minuteData.average
          },
          yclose: minuteData.price[0] || 145.00
        }]
      }

      callback(hqData)
    }
  },

  // 处理历史分时数据
  async handleHistoryMinuteData (data, callback) {
    // 使用相同的分时数据
    await this.handleMinuteData(data, callback)
  },

  // 处理K线数据请求
  async handleKLineData (data, callback) {
    try {
      const symbol = data.Request.Data.symbol
      const period = data.Request.Data.period || 0

      // 根据周期确定时间单位和倍数
      const { timespan, multiplier, from, to } = this.getPeriodParams(period)

      // 使用AGGREGATES API获取K线数据
      const aggregatesData = await AggregatesService.getAggregates({
        ticker: symbol,
        multiplier: multiplier,
        timespan: timespan,
        from: from,
        to: to,
        adjusted: true,
        sort: 'asc',
        limit: 5000
      })



      // 转换为HQChart格式
      const hqData = {
        code: 0,
        data: {
          symbol: aggregatesData.symbol,
          name: aggregatesData.name,
          data: aggregatesData.data.map(item => [
            parseInt(item.date.replace(/\//g, '')), // 日期转数字
            item.open,
            item.high,
            item.low,
            item.close,
            item.volume,
            item.amount
          ])
        }
      }

      console.log('✅ [HQChartAdapter] K线数据处理完成:', {
        symbol: symbol,
        dataPoints: aggregatesData.data.length,
        status: aggregatesData.status,
        hqDataSample: hqData.data.data.slice(0, 2) // 显示转换后的前2个数据点
      })

      callback(hqData)
    } catch (error) {
      // 降级到原有服务
      const klineData = await KLineService.getKLineData(symbol, period)

      const hqData = {
        code: 0,
        data: {
          symbol: klineData.symbol,
          name: klineData.name,
          data: klineData.data.map(item => [
            parseInt(item.date.replace(/\//g, '')),
            item.open,
            item.high,
            item.low,
            item.close,
            item.volume,
            item.amount
          ])
        }
      }

      callback(hqData)
    }
  },

  // 处理实时数据请求
  async handleRealtimeData (data, callback) {
    try {
      const symbol = data.Request.Data.symbol

      // 使用WebSocket服务获取最新价格
      const priceData = await getLatestPrice(symbol)

      if (priceData) {
        const today = new Date()
        const dateNum = parseInt(`${today.getFullYear()}${(today.getMonth() + 1).toString().padStart(2, '0')}${today.getDate().toString().padStart(2, '0')}`)

        const hqData = {
          code: 0,
          data: {
            symbol: symbol,
            data: [[
              dateNum,
              priceData.price,
              priceData.price,
              priceData.price,
              priceData.price,
              priceData.volume,
              priceData.price * priceData.volume
            ]]
          }
        }

        callback(hqData)
      } else {

        // 降级到原有服务
        const realtimeData = await KLineService.getRealTimeData(symbol)

        if (realtimeData) {
          const today = new Date()
          const dateNum = parseInt(`${today.getFullYear()}${(today.getMonth() + 1).toString().padStart(2, '0')}${today.getDate().toString().padStart(2, '0')}`)

          const hqData = {
            code: 0,
            data: {
              symbol: symbol,
              data: [[
                dateNum,
                realtimeData.price,
                realtimeData.price,
                realtimeData.price,
                realtimeData.price,
                realtimeData.volume,
                realtimeData.price * realtimeData.volume
              ]]
            }
          }

          callback(hqData)
        } else {
          callback({ code: -1, error: '获取实时数据失败' })
        }
      }
    } catch (error) {
      callback({ code: -1, error: error.message })
    }
  },

  // 处理分钟K线数据
  async handleMinuteKLineData (data, callback) {
    const symbol = data.Request.Data.symbol
    const period = data.Request.Data.period || 1 // 分钟周期

    const klineData = await KLineService.getKLineData(symbol, period)

    // 转换为HQChart分钟K线格式
    const hqData = {
      code: 0,
      data: {
        symbol: klineData.symbol,
        name: klineData.name,
        data: klineData.data.map(item => {
          // 分钟K线需要时间戳
          const dateParts = item.date.split('/')
          const date = new Date(dateParts[0], dateParts[1] - 1, dateParts[2])
          const dateNum = parseInt(`${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}`)
          const time = 935 // 默认时间 09:35

          return [
            dateNum,
            time,
            item.open,
            item.high,
            item.low,
            item.close,
            item.volume,
            item.amount
          ]
        })
      }
    }

    callback(hqData)
  },

  // 处理分钟实时数据
  async handleMinuteRealtimeData (data, callback) {
    // 使用实时数据
    await this.handleRealtimeData(data, callback)
  },

  // ======================== 工具方法 ========================

  /**
   * 根据周期获取API参数
   * @param {number} period - HQChart周期
   * @returns {Object} API参数
   */
  getPeriodParams (period) {
    const now = new Date()
    const to = now.toISOString().split('T')[0] // YYYY-MM-DD
    let from, timespan, multiplier

    switch (period) {
      case 0: // 日线
        timespan = 'day'
        multiplier = 1
        from = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 1年前
        break
      case 1: // 1分钟
        timespan = 'minute'
        multiplier = 1
        from = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 7天前
        break
      case 2: // 5分钟
        timespan = 'minute'
        multiplier = 5
        from = new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 5天前（修改：从30天改为5天）
        break
      case 3: // 15分钟
        timespan = 'minute'
        multiplier = 15
        from = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 60天前
        break
      case 4: // 30分钟
        timespan = 'minute'
        multiplier = 30
        from = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 90天前
        break
      case 5: // 60分钟
        timespan = 'hour'
        multiplier = 1
        from = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 180天前
        break
      case 6: // 周线
        timespan = 'week'
        multiplier = 1
        from = new Date(now.getTime() - 2 * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 2年前
        break
      case 7: // 月线
        timespan = 'month'
        multiplier = 1
        from = new Date(now.getTime() - 5 * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 5年前
        break
      default:
        timespan = 'day'
        multiplier = 1
        from = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    }

    return { timespan, multiplier, from, to }
  },

  /**
   * 订阅实时数据更新
   * @param {string} symbol - 股票代码
   * @param {Function} callback - 数据更新回调
   */
  subscribeRealtimeUpdates (symbol, callback) {
    // 订阅WebSocket实时数据
    subscribeRealTime(symbol, (data) => {
      try {
        // 转换为HQChart实时数据格式
        const today = new Date()
        const dateNum = parseInt(`${today.getFullYear()}${(today.getMonth() + 1).toString().padStart(2, '0')}${today.getDate().toString().padStart(2, '0')}`)

        const hqData = {
          code: 0,
          data: {
            symbol: symbol,
            data: [[
              dateNum,
              data.data.price,
              data.data.price,
              data.data.price,
              data.data.price,
              data.data.volume,
              data.data.price * data.data.volume
            ]]
          }
        }

        callback(hqData)
      } catch (error) {
        // 实时数据回调处理失败，静默处理
      }
    })
  },

  /**
   * 将K线数据转换为分时图格式
   * @param {Object} aggregatesData - K线数据
   * @param {number} previousClose - 前收盘价
   * @returns {Object} 分时图格式数据
   */
  convertToMinuteFormat (aggregatesData, previousClose) {
    const time = []
    const price = []
    const volume = []
    const amount = []
    const average = []

    let totalAmount = 0
    let totalVolume = 0

    aggregatesData.data.forEach((item, index) => {
      // 时间格式转换 (HH:MM)
      const date = new Date(item.timestamp)
      const timeStr = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`

      time.push(timeStr)
      price.push(item.close)
      volume.push(item.volume)
      amount.push(item.amount)

      // 计算累计成交额和成交量
      totalAmount += item.amount
      totalVolume += item.volume

      // 计算平均价
      const avgPrice = totalVolume > 0 ? totalAmount / totalVolume : item.close
      average.push(parseFloat(avgPrice.toFixed(2)))
    })

    return {
      symbol: aggregatesData.symbol,
      name: aggregatesData.name,
      time: time,
      price: price,
      volume: volume,
      amount: amount,
      average: average
    }
  }
}

export default HQChartAdapter
