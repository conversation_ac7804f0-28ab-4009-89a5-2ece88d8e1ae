/**
 * 技术指标计算工具
 * 参考HQChart实现各种技术指标的计算
 */

// 简单移动平均线
function SMA (data, period) {
  const result = []
  for (let i = period - 1; i < data.length; i++) {
    let sum = 0
    for (let j = 0; j < period; j++) {
      sum += data[i - j]
    }
    result.push(sum / period)
  }
  return result
}

// 指数移动平均线
function EMA (data, period) {
  const result = []
  const multiplier = 2 / (period + 1)

  if (data.length === 0) return result

  // 第一个值使用简单平均
  result[0] = data[0]

  for (let i = 1; i < data.length; i++) {
    result[i] = (data[i] * multiplier) + (result[i - 1] * (1 - multiplier))
  }

  return result
}

// 标准差计算
function StandardDeviation (data, period) {
  const result = []

  for (let i = period - 1; i < data.length; i++) {
    let sum = 0
    let mean = 0

    // 计算平均值
    for (let j = 0; j < period; j++) {
      mean += data[i - j]
    }
    mean = mean / period

    // 计算标准差
    for (let j = 0; j < period; j++) {
      sum += Math.pow(data[i - j] - mean, 2)
    }

    result.push(Math.sqrt(sum / period))
  }

  return result
}

/**
 * MACD指标计算
 * @param {Array} prices 价格数组
 * @param {Number} fastPeriod 快线周期，默认12
 * @param {Number} slowPeriod 慢线周期，默认26
 * @param {Number} signalPeriod 信号线周期，默认9
 */
export function calculateMACD (prices, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
  if (prices.length < slowPeriod) {
    return { DIF: [], DEA: [], MACD: [] }
  }

  const fastEMA = EMA(prices, fastPeriod)
  const slowEMA = EMA(prices, slowPeriod)

  // 计算DIF线
  const DIF = []
  const startIndex = slowPeriod - fastPeriod

  for (let i = 0; i < slowEMA.length; i++) {
    DIF.push(fastEMA[i + startIndex] - slowEMA[i])
  }

  // 计算DEA线（DIF的EMA）
  const DEA = EMA(DIF, signalPeriod)

  // 计算MACD柱状图
  const MACD = []
  const deaStartIndex = signalPeriod - 1

  for (let i = 0; i < DEA.length; i++) {
    MACD.push((DIF[i + deaStartIndex] - DEA[i]) * 2)
  }

  return {
    DIF: DIF.slice(-1)[0] || 0,
    DEA: DEA.slice(-1)[0] || 0,
    MACD: MACD.slice(-1)[0] || 0
  }
}

/**
 * KDJ指标计算
 * @param {Array} highs 最高价数组
 * @param {Array} lows 最低价数组
 * @param {Array} closes 收盘价数组
 * @param {Number} period K周期，默认9
 * @param {Number} kPeriod K平滑周期，默认3
 * @param {Number} dPeriod D平滑周期，默认3
 */
export function calculateKDJ (highs, lows, closes, period = 9, kPeriod = 3, dPeriod = 3) {
  if (closes.length < period) {
    return { K: 50, D: 50, J: 50 }
  }

  const RSV = []

  // 计算RSV
  for (let i = period - 1; i < closes.length; i++) {
    let highestHigh = Math.max(...highs.slice(i - period + 1, i + 1))
    let lowestLow = Math.min(...lows.slice(i - period + 1, i + 1))

    if (highestHigh === lowestLow) {
      RSV.push(50)
    } else {
      RSV.push((closes[i] - lowestLow) / (highestHigh - lowestLow) * 100)
    }
  }

  // 计算K值（RSV的移动平均）
  const K = SMA(RSV, kPeriod)

  // 计算D值（K的移动平均）
  const D = SMA(K, dPeriod)

  // 计算J值
  const J = []
  for (let i = 0; i < D.length; i++) {
    J.push(3 * K[i + (K.length - D.length)] - 2 * D[i])
  }

  return {
    K: K.slice(-1)[0] || 50,
    D: D.slice(-1)[0] || 50,
    J: J.slice(-1)[0] || 50
  }
}

/**
 * BOLL指标计算
 * @param {Array} prices 价格数组
 * @param {Number} period 周期，默认20
 * @param {Number} stdDev 标准差倍数，默认2
 */
export function calculateBOLL (prices, period = 20, stdDev = 2) {
  if (prices.length < period) {
    return { UPPER: 0, MID: 0, LOWER: 0 }
  }

  const MA = SMA(prices, period)
  const SD = StandardDeviation(prices, period)

  const UPPER = []
  const LOWER = []

  for (let i = 0; i < MA.length; i++) {
    UPPER.push(MA[i] + stdDev * SD[i])
    LOWER.push(MA[i] - stdDev * SD[i])
  }

  return {
    UPPER: UPPER.slice(-1)[0] || 0,
    MID: MA.slice(-1)[0] || 0,
    LOWER: LOWER.slice(-1)[0] || 0
  }
}

/**
 * EMA指标计算
 * @param {Array} prices 价格数组
 * @param {Number} period1 第一个周期，默认12
 * @param {Number} period2 第二个周期，默认26
 */
export function calculateEMA (prices, period1 = 12, period2 = 26) {
  const EMA12 = EMA(prices, period1)
  const EMA26 = EMA(prices, period2)

  return {
    EMA12: EMA12.slice(-1)[0] || 0,
    EMA26: EMA26.slice(-1)[0] || 0
  }
}

/**
 * SAR指标计算
 * @param {Array} highs 最高价数组
 * @param {Array} lows 最低价数组
 * @param {Number} acceleration 加速因子，默认0.02
 * @param {Number} maximum 最大加速因子，默认0.2
 */
export function calculateSAR (highs, lows, acceleration = 0.02, maximum = 0.2) {
  if (highs.length < 2) {
    return { VALUE: 0 }
  }

  let sar = lows[0]
  let ep = highs[0] // 极值点
  let af = acceleration // 加速因子
  let isUpTrend = true

  for (let i = 1; i < highs.length; i++) {
    // 计算新的SAR值
    sar = sar + af * (ep - sar)

    if (isUpTrend) {
      // 上升趋势
      if (lows[i] <= sar) {
        // 趋势反转
        isUpTrend = false
        sar = ep
        ep = lows[i]
        af = acceleration
      } else {
        // 继续上升趋势
        if (highs[i] > ep) {
          ep = highs[i]
          af = Math.min(af + acceleration, maximum)
        }
      }
    } else {
      // 下降趋势
      if (highs[i] >= sar) {
        // 趋势反转
        isUpTrend = true
        sar = ep
        ep = highs[i]
        af = acceleration
      } else {
        // 继续下降趋势
        if (lows[i] < ep) {
          ep = lows[i]
          af = Math.min(af + acceleration, maximum)
        }
      }
    }
  }

  return {
    VALUE: sar
  }
}

/**
 * KC指标计算（Keltner Channels）
 * @param {Array} highs 最高价数组
 * @param {Array} lows 最低价数组
 * @param {Array} closes 收盘价数组
 * @param {Number} period 周期，默认20
 * @param {Number} multiplier 倍数，默认2
 */
export function calculateKC (highs, lows, closes, period = 20, multiplier = 2) {
  if (closes.length < period) {
    return { UPPER: 0, MID: 0, LOWER: 0 }
  }

  // 计算典型价格
  const typicalPrices = []
  for (let i = 0; i < closes.length; i++) {
    typicalPrices.push((highs[i] + lows[i] + closes[i]) / 3)
  }

  const MA = SMA(typicalPrices, period)

  // 计算真实波幅
  const trueRanges = []
  for (let i = 1; i < closes.length; i++) {
    const tr1 = highs[i] - lows[i]
    const tr2 = Math.abs(highs[i] - closes[i - 1])
    const tr3 = Math.abs(lows[i] - closes[i - 1])
    trueRanges.push(Math.max(tr1, tr2, tr3))
  }

  const ATR = SMA(trueRanges, period)

  const UPPER = []
  const LOWER = []

  for (let i = 0; i < MA.length; i++) {
    const atrIndex = Math.min(i, ATR.length - 1)
    UPPER.push(MA[i] + multiplier * ATR[atrIndex])
    LOWER.push(MA[i] - multiplier * ATR[atrIndex])
  }

  return {
    UPPER: UPPER.slice(-1)[0] || 0,
    MID: MA.slice(-1)[0] || 0,
    LOWER: LOWER.slice(-1)[0] || 0
  }
}

/**
 * ARBR指标计算
 * @param {Array} highs 最高价数组
 * @param {Array} lows 最低价数组
 * @param {Array} closes 收盘价数组
 * @param {Array} opens 开盘价数组
 * @param {Number} period 周期，默认26
 */
export function calculateARBR (highs, lows, closes, opens, period = 26) {
  if (closes.length < period) {
    return { AR: 100, BR: 100 }
  }

  let arSum = 0
  let brSum = 0
  let arDenominator = 0
  let brDenominator = 0

  const startIndex = Math.max(0, closes.length - period)

  for (let i = startIndex; i < closes.length; i++) {
    // AR计算
    arSum += highs[i] - opens[i]
    arDenominator += opens[i] - lows[i]

    // BR计算
    if (i > 0) {
      brSum += Math.max(0, highs[i] - closes[i - 1])
      brDenominator += Math.max(0, closes[i - 1] - lows[i])
    }
  }

  const AR = arDenominator !== 0 ? (arSum / arDenominator) * 100 : 100
  const BR = brDenominator !== 0 ? (brSum / brDenominator) * 100 : 100

  return {
    AR: AR,
    BR: BR
  }
}

/**
 * 生成模拟K线数据
 * @param {Number} count 数据数量
 * @param {Number} basePrice 基础价格
 */
export function generateMockKLineData (count = 100, basePrice = 100) {
  const data = {
    opens: [],
    highs: [],
    lows: [],
    closes: [],
    volumes: []
  }

  let currentPrice = basePrice

  for (let i = 0; i < count; i++) {
    const change = (Math.random() - 0.5) * 4 // -2% to +2%
    const open = currentPrice
    const close = open + change
    const high = Math.max(open, close) + Math.random() * 2
    const low = Math.min(open, close) - Math.random() * 2
    const volume = Math.floor(Math.random() * 1000000) + 100000

    data.opens.push(open)
    data.highs.push(high)
    data.lows.push(low)
    data.closes.push(close)
    data.volumes.push(volume)

    currentPrice = close
  }

  return data
}
