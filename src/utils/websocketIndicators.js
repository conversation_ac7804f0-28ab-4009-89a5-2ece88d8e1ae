/**
 * WebSocket技术指标数据模拟器
 * 模拟实时技术指标数据推送
 */

import {
  calculateMACD,
  calculateKDJ,
  calculateBOLL,
  calculateEMA,
  calculateSAR,
  calculateKC,
  calculateARBR
} from './technicalIndicators'

class IndicatorWebSocket {
  constructor () {
    this.socket = null
    this.isConnected = false
    this.callbacks = new Map()
    this.mockTimer = null
    this.klineData = {
      opens: [],
      highs: [],
      lows: [],
      closes: [],
      volumes: []
    }
  }

  // 连接WebSocket（模拟）
  connect (symbol) {
    // 模拟连接成功
    setTimeout(() => {
      this.isConnected = true
      this.onConnected()

      // 开始模拟数据推送
      this.startMockDataPush(symbol)
    }, 1000)
  }

  // 断开连接
  disconnect () {
    this.isConnected = false

    if (this.mockTimer) {
      clearInterval(this.mockTimer)
      this.mockTimer = null
    }
  }

  // 订阅技术指标数据
  subscribe (indicators, callback) {
    indicators.forEach(indicator => {
      if (!this.callbacks.has(indicator)) {
        this.callbacks.set(indicator, [])
      }
      this.callbacks.get(indicator).push(callback)
    })
  }

  // 取消订阅
  unsubscribe (indicator, callback) {
    if (this.callbacks.has(indicator)) {
      const callbacks = this.callbacks.get(indicator)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  // 连接成功回调
  onConnected () {
    // 连接成功
  }

  // 开始模拟数据推送
  startMockDataPush (symbol) {
    // 初始化K线数据
    this.initializeKLineData()

    // 每5秒推送一次新数据
    this.mockTimer = setInterval(() => {
      this.pushNewKLineData()
      this.calculateAndPushIndicators()
    }, 5000)
  }

  // 初始化K线数据
  initializeKLineData () {
    const basePrice = 121.69 // FUTU的当前价格

    // 生成100个历史数据点
    for (let i = 0; i < 100; i++) {
      const change = (Math.random() - 0.5) * 4 // -2% to +2%
      const open = basePrice + (Math.random() - 0.5) * 10
      const close = open + change
      const high = Math.max(open, close) + Math.random() * 2
      const low = Math.min(open, close) - Math.random() * 2
      const volume = Math.floor(Math.random() * 1000000) + 100000

      this.klineData.opens.push(open)
      this.klineData.highs.push(high)
      this.klineData.lows.push(low)
      this.klineData.closes.push(close)
      this.klineData.volumes.push(volume)
    }
  }

  // 推送新的K线数据
  pushNewKLineData () {
    const lastClose = this.klineData.closes[this.klineData.closes.length - 1]
    const change = (Math.random() - 0.5) * 3 // -1.5% to +1.5%

    const open = lastClose
    const close = open + change
    const high = Math.max(open, close) + Math.random() * 1.5
    const low = Math.min(open, close) - Math.random() * 1.5
    const volume = Math.floor(Math.random() * 1000000) + 100000

    // 添加新数据
    this.klineData.opens.push(open)
    this.klineData.highs.push(high)
    this.klineData.lows.push(low)
    this.klineData.closes.push(close)
    this.klineData.volumes.push(volume)

    // 保持最近100个数据点
    if (this.klineData.closes.length > 100) {
      this.klineData.opens.shift()
      this.klineData.highs.shift()
      this.klineData.lows.shift()
      this.klineData.closes.shift()
      this.klineData.volumes.shift()
    }
  }

  // 计算并推送技术指标
  calculateAndPushIndicators () {
    try {
      // 计算各种技术指标
      const indicators = {
        MACD: calculateMACD(this.klineData.closes),
        KDJ: calculateKDJ(this.klineData.highs, this.klineData.lows, this.klineData.closes),
        BOLL: calculateBOLL(this.klineData.closes),
        EMA: calculateEMA(this.klineData.closes),
        SAR: calculateSAR(this.klineData.highs, this.klineData.lows),
        KC: calculateKC(this.klineData.highs, this.klineData.lows, this.klineData.closes),
        ARBR: calculateARBR(this.klineData.highs, this.klineData.lows, this.klineData.closes, this.klineData.opens)
      }

      // 推送数据给订阅者
      this.callbacks.forEach((callbacks, indicator) => {
        if (indicators[indicator]) {
          callbacks.forEach(callback => {
            try {
              callback({
                indicator,
                data: indicators[indicator],
                timestamp: Date.now(),
                price: this.klineData.closes[this.klineData.closes.length - 1]
              })
            } catch (error) {
              // 推送技术指标数据失败，静默处理
            }
          })
        }
      })

    } catch (error) {
      // 计算技术指标失败，静默处理
    }
  }

  // 获取当前价格
  getCurrentPrice () {
    return this.klineData.closes[this.klineData.closes.length - 1] || 121.69
  }

  // 获取历史数据
  getHistoryData (count = 50) {
    const length = this.klineData.closes.length
    const start = Math.max(0, length - count)

    return {
      opens: this.klineData.opens.slice(start),
      highs: this.klineData.highs.slice(start),
      lows: this.klineData.lows.slice(start),
      closes: this.klineData.closes.slice(start),
      volumes: this.klineData.volumes.slice(start)
    }
  }
}

// 创建全局实例
const indicatorWS = new IndicatorWebSocket()

export default indicatorWS

// 导出便捷方法
export function connectIndicatorWebSocket (symbol) {
  return indicatorWS.connect(symbol)
}

export function subscribeIndicators (indicators, callback) {
  return indicatorWS.subscribe(indicators, callback)
}

export function unsubscribeIndicators (indicator, callback) {
  return indicatorWS.unsubscribe(indicator, callback)
}

export function disconnectIndicatorWebSocket () {
  return indicatorWS.disconnect()
}

export function getCurrentPrice () {
  return indicatorWS.getCurrentPrice()
}

export function getHistoryData (count) {
  return indicatorWS.getHistoryData(count)
}
