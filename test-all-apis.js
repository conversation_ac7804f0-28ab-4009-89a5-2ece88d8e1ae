const axios = require('axios')
const fs = require('fs')

// 配置axios默认请求
const apiClient = axios.create({
  baseURL: 'http://localhost:3001',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 测试结果记录
const testResults = {
  timestamp: new Date().toISOString(),
  totalAPIs: 0,
  successCount: 0,
  failureCount: 0,
  results: []
}

// 所有API接口列表
const apiList = [
  // 市场行情类
  { name: 'getMarket', url: '/api/stock/getMarket.do', method: 'post', params: { type: 'index' }, category: '市场行情' },
  { name: 'getStock', url: '/api/stock/getStock.do', method: 'post', params: { pageSize: 10, type: 'US' }, category: '市场行情' },
  { name: 'getFutures', url: '/api/stock/getFutures.do', method: 'post', params: { pageSize: 10 }, category: '市场行情' },
  { name: 'getSingleStock', url: '/api/stock/getSingleStock.do', method: 'post', params: { code: 'AAPL', market: 'US' }, category: '市场行情' },
  { name: 'getUsDetail', url: '/api/stock/getSingleStockByCode.do', method: 'post', params: { code: 'AAPL' }, category: '市场行情' },
  { name: 'stockgetlhb', url: '/api/stock/getlhb.do', method: 'post', params: {}, category: '市场行情' },
  { name: 'getMinK_Echarts', url: '/api/stock/getMinK_Echarts.do', method: 'post', params: { code: 'AAPL', time: 15, type: 'US' }, category: '市场行情' },
  { name: 'stockDayKline', url: '/api/stock/stockDayKline.do', method: 'post', params: { code: '000001.SS' }, category: '市场行情' },
  { name: 'stockMinuteKline', url: '/api/stock/stockMinuteKline.do', method: 'post', params: { code: '000001.SS' }, category: '市场行情' },
  { name: 'stockWeekKline', url: '/api/stock/stockWeekKline.do', method: 'post', params: { code: '000001.SS' }, category: '市场行情' },
  { name: 'stockMonthKline', url: '/api/stock/stockMonthKline.do', method: 'post', params: { code: '000001.SS' }, category: '市场行情' },
  { name: 'getNewDetailByPc', url: '/api/stock/getNewDetailByPc.do', method: 'post', params: {}, category: '市场行情' },
  { name: 'getIndices', url: '/api/stock/getIndices.do', method: 'post', params: {}, category: '市场行情' },
  { name: 'getIntlIndices', url: '/api/stock/getIntlIndices.do', method: 'post', params: {}, category: '市场行情' },
  { name: 'getFuturesCommon', url: '/api/stock/getFuturesCommon.do', method: 'post', params: { symbol: 'GC', market: 'COMEX' }, category: '市场行情' },
  { name: 'searchstock', url: '/api/stock/search.do', method: 'post', params: { keyword: 'apple' }, category: '市场行情' },
  { name: 'getsearchrecord', url: '/user/getsearchrecord.do', method: 'post', params: {}, category: '市场行情' },
  { name: 'deletesearchrecord', url: '/user/deletesearchrecord.do', method: 'post', params: { id: 1 }, category: '市场行情' },
  { name: 'getAllBuy', url: '/user/order/getAllBuy.do', method: 'post', params: {}, category: '市场行情' },
  { name: 'orderSort', url: '/user/order/orderSort.do', method: 'post', params: {}, category: '市场行情' },
  { name: 'stockhis', url: '/api/stock/stockhis.do', method: 'post', params: { code: 'AAPL' }, category: '市场行情' },

  // 用户相关
  { name: 'getInfo', url: '/user/getInfo.do', method: 'post', params: {}, category: '用户信息' },
  { name: 'updateUser', url: '/user/updateUser.do', method: 'post', params: { nickName: 'test' }, category: '用户信息' },
  { name: 'auth', url: '/user/auth.do', method: 'post', params: {}, category: '用户信息' },
  { name: 'realname', url: '/user/realname.do', method: 'post', params: { realName: '测试', idNumber: '123456' }, category: '用户信息' },
  { name: 'sendmsgEmail', url: '/sms/sendmsgEmail.do', method: 'post', params: { email: '<EMAIL>' }, category: '用户信息' },
  { name: 'getUserInfo', url: '/user/getUserInfo.do', method: 'post', params: {}, category: '用户信息' },
  { name: 'getbankCard', url: '/user/cash/getbankCard.do', method: 'post', params: {}, category: '用户信息' },
  { name: 'addimgs', url: '/user/addimgs.do', method: 'post', params: {}, category: '用户信息' },

  // 登录注册
  { name: 'login', url: '/api/user/login.do', method: 'post', params: { phone: '***********', password: '123456' }, category: '登录注册' },
  { name: 'register', url: '/api/user/reg.do', method: 'post', params: { phone: '***********', password: '123456', code: '1234' }, category: '登录注册' },
  { name: 'sendSms', url: '/sms/sendSms.do', method: 'post', params: { phone: '***********' }, category: '登录注册' },
  { name: 'verifyCode', url: '/sms/verifyCode.do', method: 'post', params: { phone: '***********', code: '1234' }, category: '登录注册' },
  { name: 'findPwd', url: '/api/user/updatePwd.do', method: 'post', params: { phone: '***********', password: '123456', code: '1234' }, category: '登录注册' },
  { name: 'logout', url: '/api/user/logout.do', method: 'post', params: {}, category: '登录注册' },

  // 交易相关
  { name: 'buy', url: '/api/stock/buy.do', method: 'post', params: { stockCode: 'AAPL', amount: 1 }, category: '交易操作' },
  { name: 'sell', url: '/api/stock/sell.do', method: 'post', params: { orderId: 'test123' }, category: '交易操作' },
  { name: 'orderList', url: '/user/order/list.do', method: 'post', params: { state: 0, pageNum: 1, pageSize: 10 }, category: '交易操作' },
  { name: 'cancelOrder', url: '/user/order/cancel.do', method: 'post', params: { orderId: 'test123' }, category: '交易操作' },
  { name: 'liquidation', url: '/user/order/liquidation.do', method: 'post', params: { orderId: 'test123' }, category: '交易操作' },
  { name: 'fandian', url: '/user/fandian/getfandian.do', method: 'post', params: {}, category: '交易操作' },
  { name: 'holdList', url: '/user/order/holdList.do', method: 'post', params: { pageNum: 1, pageSize: 10 }, category: '交易操作' },
  { name: 'sellList', url: '/user/order/sellList.do', method: 'post', params: { pageNum: 1, pageSize: 10 }, category: '交易操作' },
  { name: 'getOrderById', url: '/user/order/getOrderById.do', method: 'post', params: { orderId: 'test123' }, category: '交易操作' },
  { name: 'getHoldLine', url: '/user/order/getHoldLine.do', method: 'post', params: {}, category: '交易操作' },
  { name: 'orderprofit', url: '/user/order/orderprofit.do', method: 'post', params: { type: 'day' }, category: '交易操作' },
  { name: 'userrecharge', url: '/user/recharge/add.do', method: 'post', params: { orderType: 0, money: 100 }, category: '交易操作' },
  { name: 'withdraw', url: '/user/recharge/withdraw.do', method: 'post', params: { money: 100, withPwd: '123456', bankId: 1 }, category: '交易操作' },
  { name: 'cashrecord', url: '/user/cash/cashrecord.do', method: 'post', params: { pageNum: 1, pageSize: 10 }, category: '交易操作' },
  { name: 'rechargerecord', url: '/user/recharge/rechargerecord.do', method: 'post', params: { pageNum: 1, pageSize: 10 }, category: '交易操作' },
  { name: 'transloglist', url: '/user/trans/transloglist.do', method: 'post', params: { pageNum: 1, pageSize: 10 }, category: '交易操作' },
  { name: 'transfer', url: '/user/trans/transfer.do', method: 'post', params: { phone: '***********', money: 100 }, category: '交易操作' },
  { name: 'saveBankCard', url: '/user/cash/saveBankCard.do', method: 'post', params: { bankNo: '****************', bankName: '测试银行', bankAddress: '测试支行' }, category: '交易操作' },
  { name: 'updateBankCard', url: '/user/cash/updateBankCard.do', method: 'post', params: { id: 1, bankNo: '****************' }, category: '交易操作' },
  { name: 'updatePay', url: '/api/pay/juhe1/updatePay.do', method: 'post', params: { payOrderId: 'test123' }, category: '交易操作' },
  { name: 'checkPayUpdate', url: '/api/pay/juhe1/checkPay.do', method: 'post', params: { payOrderId: 'test123' }, category: '交易操作' },

  // 自选股相关
  { name: 'getMyList', url: '/user/option/list.do', method: 'post', params: {}, category: '自选股' },
  { name: 'addOption', url: '/user/addOption.do', method: 'post', params: { code: 'AAPL', name: '苹果公司' }, category: '自选股' },
  { name: 'delOption', url: '/user/delOption.do', method: 'post', params: { code: 'AAPL', name: '苹果公司' }, category: '自选股' },
  { name: 'isOption', url: '/user/isOption.do', method: 'post', params: { code: 'AAPL' }, category: '自选股' },

  // 其他功能
  { name: 'getnoticeinfo', url: '/api/news/getnoticeinfo.do', method: 'post', params: { id: 1 }, category: '其他功能' },
  { name: 'getnoticelist', url: '/api/news/getnoticelist.do', method: 'post', params: { pageNum: 1, pageSize: 10 }, category: '其他功能' },
  { name: 'user_report_list', url: '/user/trans/user_report_list.do', method: 'post', params: { pageNum: 1, pageSize: 10 }, category: '其他功能' },
  { name: 'fandian_list', url: '/user/fandian/fandian_list.do', method: 'post', params: { pageNum: 1, pageSize: 10 }, category: '其他功能' },
  { name: 'getMessage', url: '/api/news/getMessage.do', method: 'post', params: {}, category: '其他功能' },
  { name: 'getUpTime', url: '/user/getUpTime.do', method: 'post', params: {}, category: '其他功能' },
  { name: 'getSms', url: '/api/system/getSms.do', method: 'post', params: {}, category: '其他功能' },
  { name: 'getVersion', url: '/api/system/getVersion.do', method: 'post', params: {}, category: '其他功能' },
  { name: 'getListMarkets', url: '/user/trans/getListMarkets.do', method: 'post', params: {}, category: '其他功能' },
  { name: 'payType', url: '/user/payType.do', method: 'post', params: {}, category: '其他功能' },
  { name: 'getCapitalPlan', url: '/user/getCapitalPlan.do', method: 'post', params: {}, category: '其他功能' },
  { name: 'getIndexMarket', url: '/api/system/getIndexMarket.do', method: 'post', params: {}, category: '其他功能' },
  { name: 'getTradeMarket', url: '/api/system/getTradeMarket.do', method: 'post', params: {}, category: '其他功能' }
]

// 测试单个API
async function testAPI(api) {
  const startTime = Date.now()
  const result = {
    name: api.name,
    url: api.url,
    method: api.method,
    category: api.category,
    params: api.params,
    timestamp: new Date().toISOString()
  }

  try {
    console.log(`\n📍 测试接口: ${api.name} (${api.category})`)
    console.log(`   URL: ${api.url}`)
    console.log(`   参数:`, JSON.stringify(api.params))

    const response = await apiClient({
      method: api.method,
      url: api.url,
      data: api.params
    })

    const responseTime = Date.now() - startTime

    result.status = 'success'
    result.responseTime = `${responseTime}ms`
    result.statusCode = response.status
    result.response = {
      success: response.data.success || false,
      code: response.data.code,
      msg: response.data.msg || response.data.message,
      hasData: !!response.data.data,
      dataType: Array.isArray(response.data.data) ? 'array' : typeof response.data.data
    }

    console.log(`   ✅ 成功 - 响应时间: ${responseTime}ms`)
    console.log(`   返回: success=${response.data.success}, code=${response.data.code}, msg=${response.data.msg || response.data.message}`)

    testResults.successCount++

  } catch (error) {
    const responseTime = Date.now() - startTime

    result.status = 'failure'
    result.responseTime = `${responseTime}ms`

    if (error.response) {
      result.statusCode = error.response.status
      result.error = {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        message: error.response.data.msg || error.response.data.message || error.message
      }
      console.log(`   ❌ 失败 - HTTP ${error.response.status}: ${error.response.statusText}`)
      console.log(`   错误信息: ${result.error.message}`)
    } else if (error.request) {
      result.error = {
        type: 'network',
        message: '网络错误，无法连接到服务器',
        details: error.message
      }
      console.log(`   ❌ 网络错误: ${error.message}`)
    } else {
      result.error = {
        type: 'unknown',
        message: error.message
      }
      console.log(`   ❌ 未知错误: ${error.message}`)
    }

    testResults.failureCount++
  }

  return result
}

// 批量测试API
async function testAllAPIs() {
  console.log('🚀 开始测试所有API接口...')
  console.log(`📅 测试时间: ${new Date().toLocaleString()}`)
  console.log(`🌐 目标服务器: http://localhost:3001`)
  console.log(`📊 总共需要测试 ${apiList.length} 个接口`)
  console.log('='.repeat(80))

  testResults.totalAPIs = apiList.length

  // 按类别分组测试
  const categories = [...new Set(apiList.map(api => api.category))]

  for (const category of categories) {
    console.log(`\n\n🏷️  测试类别: ${category}`)
    console.log('-'.repeat(60))

    const categoryAPIs = apiList.filter(api => api.category === category)

    for (const api of categoryAPIs) {
      const result = await testAPI(api)
      testResults.results.push(result)

      // 避免请求过快
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  // 生成测试报告
  generateReport()
}

// 生成测试报告
function generateReport() {
  console.log('\n\n' + '='.repeat(80))
  console.log('📊 测试结果汇总')
  console.log('='.repeat(80))
  console.log(`✅ 成功: ${testResults.successCount} 个接口`)
  console.log(`❌ 失败: ${testResults.failureCount} 个接口`)
  console.log(`📈 成功率: ${((testResults.successCount / testResults.totalAPIs) * 100).toFixed(2)}%`)

  // 按类别统计
  console.log('\n📋 按类别统计:')
  const categories = [...new Set(testResults.results.map(r => r.category))]

  categories.forEach(category => {
    const categoryResults = testResults.results.filter(r => r.category === category)
    const successCount = categoryResults.filter(r => r.status === 'success').length
    const failureCount = categoryResults.filter(r => r.status === 'failure').length
    console.log(`   ${category}: 成功 ${successCount}, 失败 ${failureCount}`)
  })

  // 失败的接口详情
  const failedAPIs = testResults.results.filter(r => r.status === 'failure')
  if (failedAPIs.length > 0) {
    console.log('\n❌ 失败接口详情:')
    failedAPIs.forEach(api => {
      console.log(`\n   接口: ${api.name}`)
      console.log(`   URL: ${api.url}`)
      console.log(`   错误: ${api.error.message || api.error.type}`)
      if (api.error.status) {
        console.log(`   HTTP状态: ${api.error.status}`)
      }
    })
  }

  // 保存详细报告
  const reportPath = './api-test-report-' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.json'
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2))
  console.log(`\n💾 详细测试报告已保存到: ${reportPath}`)

  // 生成Markdown格式的报告
  generateMarkdownReport()
}

// 生成Markdown格式报告
function generateMarkdownReport() {
  let markdown = `# API接口测试报告

## 📊 测试概览

- **测试时间**: ${testResults.timestamp}
- **测试服务器**: http://localhost:3001
- **接口总数**: ${testResults.totalAPIs}
- **成功数量**: ${testResults.successCount}
- **失败数量**: ${testResults.failureCount}
- **成功率**: ${((testResults.successCount / testResults.totalAPIs) * 100).toFixed(2)}%

## 📈 分类统计

| 类别 | 总数 | 成功 | 失败 | 成功率 |
|------|------|------|------|--------|
`

  const categories = [...new Set(testResults.results.map(r => r.category))]

  categories.forEach(category => {
    const categoryResults = testResults.results.filter(r => r.category === category)
    const total = categoryResults.length
    const success = categoryResults.filter(r => r.status === 'success').length
    const failure = categoryResults.filter(r => r.status === 'failure').length
    const rate = ((success / total) * 100).toFixed(1)

    markdown += `| ${category} | ${total} | ${success} | ${failure} | ${rate}% |\n`
  })

  // 添加成功接口列表
  markdown += `\n## ✅ 成功接口列表\n\n`
  markdown += `| 接口名 | 类别 | 响应时间 | 返回码 | 返回消息 |\n`
  markdown += `|--------|------|----------|---------|----------|\n`

  testResults.results
    .filter(r => r.status === 'success')
    .forEach(api => {
      markdown += `| ${api.name} | ${api.category} | ${api.responseTime} | ${api.response.code || 'N/A'} | ${api.response.msg || 'N/A'} |\n`
    })

  // 添加失败接口详情
  const failedAPIs = testResults.results.filter(r => r.status === 'failure')
  if (failedAPIs.length > 0) {
    markdown += `\n## ❌ 失败接口详情\n\n`

    failedAPIs.forEach(api => {
      markdown += `### ${api.name}\n\n`
      markdown += `- **URL**: ${api.url}\n`
      markdown += `- **方法**: ${api.method}\n`
      markdown += `- **参数**: \`${JSON.stringify(api.params)}\`\n`
      markdown += `- **错误类型**: ${api.error.type || 'HTTP错误'}\n`
      markdown += `- **错误信息**: ${api.error.message}\n`
      if (api.error.status) {
        markdown += `- **HTTP状态码**: ${api.error.status}\n`
      }
      markdown += `\n`
    })
  }

  // 添加建议
  markdown += `\n## 💡 建议\n\n`

  if (failedAPIs.length > 0) {
    markdown += `1. 检查失败的接口是否需要特定的认证token\n`
    markdown += `2. 确认接口参数是否正确\n`
    markdown += `3. 验证后端服务是否正常运行\n`

    // 检查是否有认证相关的失败
    const authFailures = failedAPIs.filter(api =>
      api.error.status === 401 ||
      api.error.message?.includes('登录') ||
      api.error.message?.includes('token')
    )

    if (authFailures.length > 0) {
      markdown += `\n### ⚠️ 认证问题\n\n`
      markdown += `发现 ${authFailures.length} 个接口可能存在认证问题，建议先完成登录获取token后再测试这些接口。\n`
    }
  } else {
    markdown += `所有接口测试通过！建议进行更深入的功能测试。\n`
  }

  const mdPath = './api-test-report.md'
  fs.writeFileSync(mdPath, markdown)
  console.log(`📝 Markdown格式报告已保存到: ${mdPath}`)
}

// 开始测试
testAllAPIs().catch(error => {
  console.error('测试过程中发生错误:', error)
  process.exit(1)
})
