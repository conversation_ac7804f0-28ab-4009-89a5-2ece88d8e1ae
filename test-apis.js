const https = require('https');
const http = require('http');
const querystring = require('querystring');

// API接口配置
const BASE_URL = 'http://localhost:3001';

// 所有需要测试的API接口
const API_ENDPOINTS = {
  // 行情数据接口
  "stockgetlhb": { method: "GET", path: "/api/stock/getlhb.do", params: {} },
  "getMarket": { method: "GET", path: "/api/stock/getMarket.do", params: {} },
  "getStock": { method: "GET", path: "/api/stock/getStock.do", params: {} },
  "getSingleStock": { method: "POST", path: "/api/stock/getSingleStock.do", params: { code: "AAPL" } },
  "getUsDetail": { method: "POST", path: "/api/stock/getSingleStockByCode.do", params: { code: "AAPL" } },
  "getFutures": { method: "GET", path: "/api/futures/queryList.do", params: {} },
  
  // K线图相关
  "getKLineData": { method: "GET", path: "/api/stock/getKline.do", params: { code: "AAPL" } },
  "getKLinePirce": { method: "GET", path: "/api/stock/getPrice.do", params: { code: "AAPL" } },
  "getMinK_Echarts": { method: "POST", path: "/api/stock/getMinK_Echarts.do", params: { code: "AAPL" } },
  "getDayK": { method: "POST", path: "/api/stock/getDayK.do", params: { code: "AAPL" } },
  "getMinK": { method: "POST", path: "/api/stock/getMinK.do", params: { code: "AAPL" } },
  "getMinuteLine": { method: "POST", path: "/api/realTime/findStock.do", params: { code: "AAPL" } },
  
  // 指数相关
  "getIndexMarket": { method: "GET", path: "/api/index/queryHomeIndex.do", params: {} },
  "getListMarket": { method: "GET", path: "/api/index/queryListIndex.do", params: {} },
  "getSingleIndex": { method: "POST", path: "/api/index/querySingleIndex.do", params: { code: "DJI" } },
  "queryIndexNews": { method: "POST", path: "/api/index/queryIndexNews.do", params: {} },
  
  // 期货相关
  "getHomeFutures": { method: "GET", path: "/api/futures/queryHome.do", params: {} },
  "getListFutures": { method: "GET", path: "/api/futures/queryList.do", params: {} },
  "queryTrans": { method: "GET", path: "/api/futures/queryTrans.do", params: {} },
  "querySingleMarket": { method: "GET", path: "/api/futures/querySingleMarket.do", params: { code: "CL" } },
  "queryFuturesByCode": { method: "GET", path: "/api/futures/queryFuturesByCode.do", params: { code: "CL" } },
  
  // 用户认证相关
  "login": { method: "POST", path: "/api/user/login.do", params: { phone: "13800138000", password: "123456" } },
  "register": { method: "POST", path: "/api/user/reg.do", params: { phone: "13800138000", password: "123456", code: "1234" } },
  "logout": { method: "POST", path: "/api/user/logout.do", params: {} },
  "checkPhone": { method: "POST", path: "/api/user/checkPhone.do", params: { phone: "13800138000" } },
  "forgetPas": { method: "GET", path: "/api/user/updatePwd.do", params: { phone: "13800138000", password: "123456", code: "1234" } },
  "changePassword": { method: "GET", path: "/user/updatePwd.do", params: { oldPassword: "123456", newPassword: "654321" } },
  
  // 验证码相关
  "getCode": { method: "GET", path: "/api/sms/sendRegSms.do", params: { phone: "13800138000" } },
  "sendForgetSms": { method: "GET", path: "/api/sms/sendForgetSms.do", params: { phone: "13800138000" } },
  "getCode2": { method: "GET", path: "/code/getCode.do", params: {} },
  "checkCode": { method: "GET", path: "/code/checkCode.do", params: { code: "1234" } },
  
  // 交易相关
  "buy": { method: "POST", path: "/user/buy.do", params: { code: "AAPL", price: "150", amount: "100" } },
  "sell": { method: "POST", path: "/user/sell.do", params: { id: "123" } },
  "indexBuy": { method: "POST", path: "/user/buyIndex.do", params: { code: "DJI", amount: "1000" } },
  "sellIndex": { method: "POST", path: "/user/sellIndex.do", params: { id: "123" } },
  "buyFutures": { method: "POST", path: "/user/buyFutures.do", params: { code: "CL", amount: "1000" } },
  "sellFutures": { method: "POST", path: "/user/sellFutures.do", params: { id: "123" } },
  "guadan": { method: "POST", path: "/user/addOrder.do", params: { code: "AAPL", price: "150", amount: "100" } },
  "delGuaDan": { method: "POST", path: "/user/delOrder.do", params: { id: "123" } },
  "getorderList": { method: "POST", path: "/user/orderList.do", params: {} },
  "findBySn": { method: "GET", path: "/user/position/findBySn.do", params: { sn: "123456" } },
  
  // 持仓相关
  "getOrderList": { method: "POST", path: "/user/position/list.do", params: {} },
  "getIndexOrderList": { method: "POST", path: "/user/index/position/list.do", params: {} },
  "getFuturesOrderList": { method: "POST", path: "/user/futures/position/list.do", params: {} },
  
  // 自选股相关
  "addOption": { method: "POST", path: "/user/addOption.do", params: { code: "AAPL" } },
  "delOption": { method: "POST", path: "/user/delOption.do", params: { code: "AAPL" } },
  "getMyList": { method: "POST", path: "/user/option/list.do", params: {} },
  "isOption": { method: "POST", path: "/user/isOption.do", params: { code: "AAPL" } },
  
  // 用户信息相关
  "getUserInfo": { method: "POST", path: "/user/getUserInfo.do", params: {} },
  "userAuth": { method: "POST", path: "/user/auth.do", params: { name: "张三", idCard: "110101199001011234" } },
  
  // 银行卡相关
  "addBankCard": { method: "POST", path: "/user/bank/add.do", params: { bankCard: "****************", bankName: "工商银行" } },
  "updateBankCard": { method: "POST", path: "/user/bank/update.do", params: { id: "123", bankCard: "****************" } },
  "getBankCard": { method: "POST", path: "/user/bank/getBankInfo.do", params: {} },
  
  // 资金相关
  "AmtChange": { method: "POST", path: "/user/transAmt.do", params: { amount: "1000", type: "1" } },
  "cashDetail": { method: "POST", path: "/user/cash/list.do", params: {} },
  "rechargeList": { method: "POST", path: "/user/recharge/list.do", params: {} },
  "withdrawList": { method: "POST", path: "/user/withdraw/list.do", params: {} },
  "inMoney": { method: "POST", path: "/user/recharge/inMoney.do", params: { amount: "1000" } },
  "outMoney": { method: "POST", path: "/user/withdraw/outMoney.do", params: { amount: "500" } },
  "canceloutMoney": { method: "POST", path: "/user/withdraw/cancel.do", params: { id: "123" } },
  
  // 系统配置相关
  "getProductSetting": { method: "POST", path: "/api/admin/getProductSetting.do", params: {} },
  "getSetting": { method: "POST", path: "/api/admin/getSetting.do", params: {} },
  "getIndexSetting": { method: "POST", path: "/api/admin/getIndexSetting.do", params: {} },
  "getFuturesSetting": { method: "POST", path: "/api/admin/getFuturesSetting.do", params: {} },
  "getBannerByPlat": { method: "POST", path: "/api/site/getBannerByPlat.do", params: {} },
  "getInfoSite": { method: "POST", path: "/api/site/getInfo.do", params: {} },
  
  // 公告资讯相关
  "getArtList": { method: "POST", path: "/api/art/list.do", params: {} },
  "getArtDetail": { method: "POST", path: "/api/art/detail.do", params: { id: "123" } },
  "queryNewsList": { method: "GET", path: "/api/news/getNewsList.do", params: { pageNum: 1, pageSize: 15, type: 1 } },
  "queryNewsDetail": { method: "GET", path: "/api/news/getDetail.do", params: { id: "123" } },
  "getNoticeList": { method: "POST", path: "/user/cash/getMessagelist.do", params: {} },
  
  // 支付相关
  "getPayInfo": { method: "POST", path: "/api/site/getPayInfo.do", params: {} },
  "getPayInfoDetail": { method: "POST", path: "/api/site/getPayInfoById.do", params: { id: "123" } },
  
  // 其他功能
  "uploadimg": { method: "POST", path: "/user/upload.do", params: {} },
  "findSpreadRateOne": { method: "POST", path: "/api/user/findSpreadRateOne.do", params: {} }
};

// 发送HTTP请求的函数
function makeRequest(apiName, config) {
  return new Promise((resolve) => {
    const { method, path, params } = config;
    
    let postData = '';
    let fullPath = path;
    
    if (method === 'GET' && Object.keys(params).length > 0) {
      fullPath += '?' + querystring.stringify(params);
    } else if (method === 'POST') {
      postData = JSON.stringify(params);
    }
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: fullPath,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'API-Test-Client/1.0'
      }
    };
    
    if (method === 'POST') {
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }
    
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            apiName,
            method,
            path: fullPath,
            statusCode: res.statusCode,
            success: true,
            data: jsonData
          });
        } catch (e) {
          resolve({
            apiName,
            method,
            path: fullPath,
            statusCode: res.statusCode,
            success: false,
            error: 'JSON Parse Error',
            rawData: data
          });
        }
      });
    });
    
    req.on('error', (e) => {
      resolve({
        apiName,
        method,
        path: fullPath,
        success: false,
        error: e.message
      });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        apiName,
        method,
        path: fullPath,
        success: false,
        error: 'Request Timeout'
      });
    });
    
    if (method === 'POST') {
      req.write(postData);
    }
    
    req.end();
  });
}

// 批量测试所有API
async function testAllAPIs() {
  console.log('开始测试所有API接口...\n');
  
  const results = [];
  const apiNames = Object.keys(API_ENDPOINTS);
  
  for (let i = 0; i < apiNames.length; i++) {
    const apiName = apiNames[i];
    const config = API_ENDPOINTS[apiName];
    
    console.log(`[${i + 1}/${apiNames.length}] 测试 ${apiName}...`);
    
    const result = await makeRequest(apiName, config);
    results.push(result);
    
    // 简单的状态输出
    if (result.success && result.statusCode === 200) {
      console.log(`✅ ${apiName}: SUCCESS`);
    } else {
      console.log(`❌ ${apiName}: ${result.error || 'HTTP ' + result.statusCode}`);
    }
    
    // 避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  return results;
}

// 生成Mock数据文件
function generateMockFile(results) {
  const mockData = {
    generateTime: new Date().toISOString(),
    totalAPIs: results.length,
    successCount: results.filter(r => r.success && r.statusCode === 200).length,
    apis: {}
  };
  
  results.forEach(result => {
    mockData.apis[result.apiName] = {
      method: result.method,
      path: result.path,
      statusCode: result.statusCode,
      success: result.success,
      response: result.data || { error: result.error, rawData: result.rawData }
    };
  });
  
  return JSON.stringify(mockData, null, 2);
}

// 主函数
async function main() {
  try {
    const results = await testAllAPIs();
    
    console.log('\n=== 测试总结 ===');
    console.log(`总接口数: ${results.length}`);
    console.log(`成功响应: ${results.filter(r => r.success && r.statusCode === 200).length}`);
    console.log(`请求失败: ${results.filter(r => !r.success).length}`);
    console.log(`其他状态码: ${results.filter(r => r.success && r.statusCode !== 200).length}`);
    
    const mockContent = generateMockFile(results);
    
    require('fs').writeFileSync('./api-test-results.json', mockContent);
    console.log('\n✅ 测试结果已保存到 api-test-results.json');
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 运行测试
main();